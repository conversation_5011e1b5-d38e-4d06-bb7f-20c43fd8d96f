#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用电商平台API数据导入脚本
从格式化的API文档解析API数据并存储到MySQL数据库

作者: Claude 4.0 Sonnet
创建时间: 2025-06-25
"""

import json
import re
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import get_db_manager


class PlatformApiDataImporter:
    """通用电商平台API数据导入器"""
    
    def __init__(self, platform_name="OZON"):
        self.db = get_db_manager()
        self.api_data = []
        self.platform_name = platform_name
        self.base_url = "https://api-seller.ozon.ru"
        
    def parse_formatted_doc(self, file_path):
        """解析格式化的API文档"""
        print(f"🔍 开始解析格式化文档: {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 预定义的API数据（基于格式化文档）
            apis = [
                {
                    'api_name': '商品创建/更新',
                    'api_path': '/v3/product/import',
                    'api_category': '商品管理',
                    'http_method': 'POST',
                    'api_description': '创建新商品或更新现有商品信息，支持批量操作',
                    'auth_type': 'API_KEY',
                    'auth_headers': {
                        'Client-Id': 'required string - 用户识别号',
                        'Api-Key': 'required string - API密钥'
                    },
                    'request_example': {
                        "items": [
                            {
                                "description_category_id": 17036191,
                                "name": "iHome-5——含监视器的视频眼，ip 视频眼",
                                "offer_id": "2iHome-5",
                                "price": "10028",
                                "old_price": "11533",
                                "currency_code": "RUB",
                                "barcode": "A2IHOME-5",
                                "type_id": 91565,
                                "vat": "0",
                                "depth": 250,
                                "height": 145,
                                "width": 110,
                                "dimension_unit": "mm",
                                "weight": 800,
                                "weight_unit": "g",
                                "primary_image": "http://ozon.promtehnika.ru/photo/iHome-5/1z.jpg",
                                "images": [
                                    "http://ozon.promtehnika.ru/photo/iHome-5?2.jpg",
                                    "http://ozon.promtehnika.ru/photo/iHome-5?3.jpg"
                                ]
                            }
                        ]
                    },
                    'response_example': {
                        "result": {
                            "task_id": 172549793
                        }
                    },
                    'limitations': '一次请求最多100个商品；图片最多15张；必须填写真实尺寸和重量',
                    'usage_notes': '支持批量操作、图片上传、视频上传、复杂属性配置'
                },
                {
                    'api_name': '商品列表查询',
                    'api_path': '/v3/product/list',
                    'api_category': '商品管理',
                    'http_method': 'POST',
                    'api_description': '根据条件查询商品列表，支持分页和过滤',
                    'auth_type': 'API_KEY',
                    'auth_headers': {
                        'Client-Id': 'required string - 用户识别号',
                        'Api-Key': 'required string - API密钥'
                    },
                    'request_example': {
                        "filter": {
                            "offer_id": ["136748"],
                            "product_id": ["223681945"],
                            "visibility": "ALL"
                        },
                        "last_id": "",
                        "limit": 100
                    },
                    'response_example': {
                        "result": {
                            "items": [
                                {
                                    "archived": False,
                                    "has_fbo_stocks": True,
                                    "has_fbs_stocks": True,
                                    "is_discounted": False,
                                    "offer_id": "136748",
                                    "product_id": 223681945
                                }
                            ],
                            "total": 1,
                            "last_id": "bnVсbA=="
                        }
                    },
                    'limitations': '单次查询最多1000条记录',
                    'usage_notes': '支持多种过滤条件和分页查询'
                },
                {
                    'api_name': '商品详情查询',
                    'api_path': '/v3/product/info/list',
                    'api_category': '商品管理',
                    'http_method': 'POST',
                    'api_description': '根据商品标识符获取详细信息',
                    'auth_type': 'API_KEY',
                    'auth_headers': {
                        'Client-Id': 'required string - 用户识别号',
                        'Api-Key': 'required string - API密钥'
                    },
                    'request_example': {
                        "offer_id": ["string"],
                        "product_id": ["string"],
                        "sku": ["string"]
                    },
                    'response_example': {
                        "items": [
                            {
                                "id": 0,
                                "name": "商品名称",
                                "offer_id": "商品代码",
                                "price": "价格",
                                "currency_code": "RUB",
                                "images": ["图片URL"],
                                "statuses": {
                                    "is_created": True,
                                    "moderate_status": "审核状态"
                                }
                            }
                        ]
                    },
                    'limitations': '单个请求最多1000个标识符',
                    'usage_notes': '可使用offer_id、product_id或sku查询'
                },
                {
                    'api_name': '商品内容评级',
                    'api_path': '/v1/product/rating-by-sku',
                    'api_category': '商品分析',
                    'http_method': 'POST',
                    'api_description': '获取商品内容质量评级和改进建议',
                    'auth_type': 'API_KEY',
                    'auth_headers': {
                        'Client-Id': 'required string - 用户识别号',
                        'Api-Key': 'required string - API密钥'
                    },
                    'request_example': {
                        "skus": ["179737222"]
                    },
                    'response_example': {
                        "products": [
                            {
                                "sku": 179737222,
                                "rating": 42.5,
                                "groups": [
                                    {
                                        "key": "media",
                                        "name": "媒体",
                                        "rating": 70,
                                        "weight": 25,
                                        "conditions": [
                                            {
                                                "key": "media_images_2",
                                                "description": "已添加2张图片",
                                                "fulfilled": True,
                                                "cost": 50
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    },
                    'limitations': '需要有效的SKU列表',
                    'usage_notes': '提供内容质量评级和具体改进建议'
                }
            ]
            
            self.api_data = apis
            print(f"✅ 成功解析 {len(self.api_data)} 个API接口")
            return True
            
        except Exception as e:
            print(f"❌ 解析文件失败: {str(e)}")
            return False
    
    def create_table(self):
        """创建数据表"""
        print("🔧 创建数据表...")
        
        sql_file = os.path.join(
            os.path.dirname(os.path.dirname(__file__)), 
            'migrations', 
            'create_ozon_api_data_table.sql'
        )
        
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                sql = f.read()
            
            self.db.execute_query(sql)
            print("✅ 数据表创建成功")
            return True
            
        except Exception as e:
            print(f"❌ 创建数据表失败: {str(e)}")
            return False
    
    def insert_data(self):
        """插入数据到数据库"""
        print("📝 开始插入数据...")
        
        insert_sql = """
        INSERT INTO platform_api_data (
            platform_name, api_name, api_path, base_url, http_method, 
            api_version, api_description, api_category, auth_type, auth_headers,
            request_headers, request_example, response_example, 
            limitations, usage_notes, is_active
        ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """
        
        try:
            for api_info in self.api_data:
                params = (
                    self.platform_name,
                    api_info['api_name'],
                    api_info['api_path'],
                    self.base_url,
                    api_info['http_method'],
                    'v3',  # API版本
                    api_info['api_description'],
                    api_info['api_category'],
                    api_info['auth_type'],
                    json.dumps(api_info['auth_headers'], ensure_ascii=False),
                    json.dumps({'Content-Type': 'application/json'}, ensure_ascii=False),
                    json.dumps(api_info['request_example'], ensure_ascii=False),
                    json.dumps(api_info['response_example'], ensure_ascii=False),
                    api_info['limitations'],
                    api_info['usage_notes'],
                    True
                )
                
                self.db.execute_query(insert_sql, params)
                print(f"✅ 插入API: {api_info['api_name']}")
            
            print(f"🎉 成功插入 {len(self.api_data)} 条API数据")
            return True
            
        except Exception as e:
            print(f"❌ 插入数据失败: {str(e)}")
            return False
    
    def run(self, doc_path):
        """运行导入流程"""
        print("🚀 开始通用平台API数据导入流程")
        print("=" * 50)
        
        # 1. 创建表
        if not self.create_table():
            return False
            
        # 2. 解析文档
        if not self.parse_formatted_doc(doc_path):
            return False
            
        # 3. 插入数据
        if not self.insert_data():
            return False
            
        print("=" * 50)
        print("🎉 平台API数据导入完成！")
        return True


def main():
    """主函数"""
    # API文档路径
    doc_path = os.path.join(
        os.path.dirname(os.path.dirname(os.path.dirname(__file__))),
        'OZON_API_Documentation.md'
    )
    
    if not os.path.exists(doc_path):
        print(f"❌ 找不到API文档: {doc_path}")
        return
    
    # 创建导入器并运行
    importer = PlatformApiDataImporter(platform_name="OZON")
    importer.run(doc_path)


if __name__ == '__main__':
    main()
