"""
OZON商品同步系统监控工具
实时监控系统状态、性能指标和错误情况
"""

import sys
import os
import time
import json
from datetime import datetime, timedelta
from typing import Dict, List

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import get_db_manager
from services.ozon_api_service import OzonApiService
from services.ozon_sync_scheduler import ozon_sync_scheduler

class SystemMonitor:
    """系统监控器"""
    
    def __init__(self):
        self.db = get_db_manager()
        self.ozon_api_service = OzonApiService()
        
    def get_system_health(self) -> Dict:
        """获取系统健康状态"""
        health = {
            'timestamp': datetime.now().isoformat(),
            'overall_status': 'healthy',
            'components': {}
        }
        
        # 检查数据库连接
        try:
            self.db.execute_query("SELECT 1")
            health['components']['database'] = {
                'status': 'healthy',
                'message': '数据库连接正常'
            }
        except Exception as e:
            health['components']['database'] = {
                'status': 'unhealthy',
                'message': f'数据库连接失败: {e}'
            }
            health['overall_status'] = 'unhealthy'
        
        # 检查调度器状态
        try:
            scheduler_status = ozon_sync_scheduler.get_scheduler_status()
            if scheduler_status['is_running']:
                health['components']['scheduler'] = {
                    'status': 'healthy',
                    'message': '调度器运行正常',
                    'details': {
                        'jobs_count': len(scheduler_status['next_jobs']),
                        'full_sync_interval': scheduler_status['full_sync_interval_hours'],
                        'incremental_sync_interval': scheduler_status['incremental_sync_interval_hours']
                    }
                }
            else:
                health['components']['scheduler'] = {
                    'status': 'warning',
                    'message': '调度器未运行'
                }
        except Exception as e:
            health['components']['scheduler'] = {
                'status': 'unhealthy',
                'message': f'调度器检查失败: {e}'
            }
            health['overall_status'] = 'unhealthy'
        
        # 检查API配置
        try:
            has_config = bool(self.ozon_api_service.client_id and self.ozon_api_service.api_key)
            if has_config:
                health['components']['api_config'] = {
                    'status': 'healthy',
                    'message': 'API配置完整'
                }
            else:
                health['components']['api_config'] = {
                    'status': 'warning',
                    'message': 'API配置不完整'
                }
        except Exception as e:
            health['components']['api_config'] = {
                'status': 'unhealthy',
                'message': f'API配置检查失败: {e}'
            }
        
        return health
    
    def get_sync_metrics(self) -> Dict:
        """获取同步指标"""
        try:
            # 商品统计
            product_stats = self.db.execute_query("""
                SELECT 
                    COUNT(*) as total_products,
                    SUM(CASE WHEN api_sync_status = 'detail_synced' THEN 1 ELSE 0 END) as synced_products,
                    SUM(CASE WHEN api_sync_status = 'failed' THEN 1 ELSE 0 END) as failed_products,
                    SUM(CASE WHEN local_product_id IS NOT NULL THEN 1 ELSE 0 END) as linked_products,
                    MAX(last_detail_sync_at) as last_sync_time
                FROM ozon_products
            """)[0]
            
            # 最近24小时同步统计
            recent_stats = self.db.execute_query("""
                SELECT 
                    COUNT(*) as total_syncs,
                    SUM(CASE WHEN sync_status = 'success' THEN 1 ELSE 0 END) as successful_syncs,
                    SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed_syncs,
                    AVG(duration_seconds) as avg_duration,
                    SUM(total_processed) as total_processed
                FROM ozon_api_sync_log
                WHERE started_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
            """)[0]
            
            # 计算成功率
            success_rate = 0
            if recent_stats['total_syncs'] > 0:
                success_rate = (recent_stats['successful_syncs'] / recent_stats['total_syncs']) * 100
            
            return {
                'products': {
                    'total': int(product_stats['total_products']),
                    'synced': int(product_stats['synced_products']),
                    'failed': int(product_stats['failed_products']),
                    'linked': int(product_stats['linked_products']),
                    'sync_completion_rate': (int(product_stats['synced_products']) / max(int(product_stats['total_products']), 1)) * 100,
                    'last_sync_time': product_stats['last_sync_time'].isoformat() if product_stats['last_sync_time'] else None
                },
                'recent_syncs': {
                    'total_syncs': int(recent_stats['total_syncs'] or 0),
                    'successful_syncs': int(recent_stats['successful_syncs'] or 0),
                    'failed_syncs': int(recent_stats['failed_syncs'] or 0),
                    'success_rate': round(success_rate, 2),
                    'avg_duration': round(float(recent_stats['avg_duration'] or 0), 2),
                    'total_processed': int(recent_stats['total_processed'] or 0)
                }
            }
            
        except Exception as e:
            return {'error': f'获取同步指标失败: {e}'}
    
    def get_error_summary(self) -> Dict:
        """获取错误摘要"""
        try:
            # 最近的错误
            recent_errors = self.db.execute_query("""
                SELECT sync_task_id, api_endpoint, sync_type, error_message, started_at
                FROM ozon_api_sync_log
                WHERE sync_status = 'failed' 
                AND started_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ORDER BY started_at DESC
                LIMIT 10
            """)
            
            # 错误类型统计
            error_types = self.db.execute_query("""
                SELECT 
                    SUBSTRING_INDEX(error_message, ':', 1) as error_type,
                    COUNT(*) as count
                FROM ozon_api_sync_log
                WHERE sync_status = 'failed' 
                AND started_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
                AND error_message IS NOT NULL
                GROUP BY error_type
                ORDER BY count DESC
                LIMIT 5
            """)
            
            return {
                'recent_errors': [
                    {
                        'task_id': error['sync_task_id'],
                        'endpoint': error['api_endpoint'],
                        'type': error['sync_type'],
                        'message': error['error_message'][:100] + '...' if len(error['error_message']) > 100 else error['error_message'],
                        'time': error['started_at'].isoformat()
                    }
                    for error in recent_errors
                ],
                'error_types': [
                    {
                        'type': error['error_type'],
                        'count': int(error['count'])
                    }
                    for error in error_types
                ]
            }
            
        except Exception as e:
            return {'error': f'获取错误摘要失败: {e}'}
    
    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        try:
            # 查询性能统计
            query_performance = self.db.execute_query("""
                SELECT 
                    api_endpoint,
                    AVG(duration_seconds) as avg_duration,
                    MIN(duration_seconds) as min_duration,
                    MAX(duration_seconds) as max_duration,
                    COUNT(*) as call_count
                FROM ozon_api_sync_log
                WHERE started_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                AND duration_seconds IS NOT NULL
                GROUP BY api_endpoint
            """)
            
            # 数据库连接池状态（如果可用）
            try:
                pool_stats = {
                    'active_connections': 'N/A',
                    'pool_size': 'N/A',
                    'checked_out': 'N/A'
                }
            except:
                pool_stats = {'error': '无法获取连接池状态'}
            
            return {
                'api_performance': [
                    {
                        'endpoint': perf['api_endpoint'],
                        'avg_duration': round(float(perf['avg_duration']), 3),
                        'min_duration': round(float(perf['min_duration']), 3),
                        'max_duration': round(float(perf['max_duration']), 3),
                        'call_count': int(perf['call_count'])
                    }
                    for perf in query_performance
                ],
                'database_pool': pool_stats
            }
            
        except Exception as e:
            return {'error': f'获取性能指标失败: {e}'}
    
    def generate_report(self) -> Dict:
        """生成完整的监控报告"""
        report = {
            'report_time': datetime.now().isoformat(),
            'system_health': self.get_system_health(),
            'sync_metrics': self.get_sync_metrics(),
            'error_summary': self.get_error_summary(),
            'performance_metrics': self.get_performance_metrics()
        }
        
        return report
    
    def print_dashboard(self):
        """打印监控仪表板"""
        report = self.generate_report()
        
        print("=" * 80)
        print("OZON商品同步系统监控仪表板")
        print(f"报告时间: {report['report_time']}")
        print("=" * 80)
        
        # 系统健康状态
        health = report['system_health']
        print(f"\n🏥 系统健康状态: {health['overall_status'].upper()}")
        for component, status in health['components'].items():
            icon = "✅" if status['status'] == 'healthy' else "⚠️" if status['status'] == 'warning' else "❌"
            print(f"  {icon} {component}: {status['message']}")
        
        # 同步指标
        metrics = report['sync_metrics']
        if 'error' not in metrics:
            print(f"\n📊 同步指标:")
            products = metrics['products']
            print(f"  商品总数: {products['total']}")
            print(f"  已同步: {products['synced']} ({products['sync_completion_rate']:.1f}%)")
            print(f"  失败: {products['failed']}")
            print(f"  已关联: {products['linked']}")
            
            recent = metrics['recent_syncs']
            print(f"  最近24小时同步: {recent['total_syncs']} 次")
            print(f"  成功率: {recent['success_rate']}%")
            print(f"  平均耗时: {recent['avg_duration']}秒")
        
        # 错误摘要
        errors = report['error_summary']
        if 'error' not in errors and errors['recent_errors']:
            print(f"\n🚨 最近错误 ({len(errors['recent_errors'])} 个):")
            for error in errors['recent_errors'][:3]:
                print(f"  • {error['time'][:19]} - {error['message'][:60]}...")
        
        # 性能指标
        performance = report['performance_metrics']
        if 'error' not in performance and performance['api_performance']:
            print(f"\n⚡ API性能:")
            for api in performance['api_performance']:
                print(f"  {api['endpoint']}: 平均 {api['avg_duration']}秒 ({api['call_count']} 次调用)")
        
        print("\n" + "=" * 80)
    
    def save_report(self, filename: str = None):
        """保存监控报告到文件"""
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"monitor_report_{timestamp}.json"
        
        report = self.generate_report()
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)
            print(f"监控报告已保存到: {filename}")
        except Exception as e:
            print(f"保存报告失败: {e}")

def main():
    """主函数"""
    monitor = SystemMonitor()
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--save':
            monitor.save_report()
        elif sys.argv[1] == '--json':
            report = monitor.generate_report()
            print(json.dumps(report, ensure_ascii=False, indent=2, default=str))
        else:
            print("用法: python monitor_system.py [--save|--json]")
    else:
        monitor.print_dashboard()

if __name__ == "__main__":
    main()
