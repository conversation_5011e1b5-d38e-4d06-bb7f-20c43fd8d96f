#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
仪表板路由
"""

from flask import Blueprint, jsonify, request
from models.database import get_dashboard_data, get_total_count, update_suggested_price, get_product_info_pricing_params, update_product_info_parameter, get_single_keyword_data, batch_update_empty_links_with_template_params
from utils.parse_excel_optimized import calculateDetailedOzonPrice
from utils.cache import cache_result, get_cache, CacheKeyGenerator, invalidate_cache_pattern
import hashlib

dashboard_bp = Blueprint('dashboard', __name__)

# 模板路由已移除，前端使用React SPA

@dashboard_bp.route('/api/dashboard-data')
def api_dashboard_data():
    """获取仪表板数据API（带缓存优化）"""
    try:
        page = int(request.args.get('page', 1))
        per_page = int(request.args.get('per_page', 10))
        has_links = request.args.get('has_links', 'false').lower() == 'true'
        no_links = request.args.get('no_links', 'false').lower() == 'true'

        # 生成缓存键
        cache_key = f"dashboard_data:{page}:{per_page}:{has_links}:{no_links}"
        cache = get_cache()

        # 尝试从缓存获取
        cached_result = cache.get(cache_key)
        if cached_result:
            return jsonify(cached_result)

        # 获取数据
        data = get_dashboard_data(page=page, per_page=per_page, has_links=has_links, no_links=no_links)
        total = get_total_count(has_links=has_links, no_links=no_links)

        result = {
            'success': True,
            'data': data,
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': total,
                'pages': (total + per_page - 1) // per_page
            }
        }

        # 缓存结果（60秒）
        cache.set(cache_key, result, ttl=60)

        return jsonify(result)

    except Exception as e:
        print(f"❌ 获取仪表板数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/calculate-price', methods=['POST'])
def api_calculate_price():
    """计算OZON价格API"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        procurement_cost = float(data.get('procurement_cost', 46))
        packaging_cost = float(data.get('packaging_cost', 10))
        weight = int(data.get('weight', 180))
        target_profit = float(data.get('target_profit', 100))
        promo_discount = float(data.get('promo_discount', 15))
        second_promo_discount = float(data.get('second_promo_discount', 10))

        if not keyword:
            return jsonify({
                'success': False,
                'error': '关键词不能为空'
            }), 400

        # 调用价格计算函数
        result = calculateDetailedOzonPrice(
            keyword=keyword,
            procurement_cost=procurement_cost,
            packaging_cost=packaging_cost,
            weight=weight,
            target_profit=target_profit,
            promo_discount=promo_discount,
            second_promo_discount=second_promo_discount
        )
        
        return jsonify({
            'success': True,
            'data': result
        })
        
    except Exception as e:
        print(f"❌ 价格计算失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/update-price', methods=['POST'])
def api_update_price():
    """更新建议价格API"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        price_cny = data.get('price_cny')
        price_rub = data.get('price_rub')
        
        if not keyword:
            return jsonify({
                'success': False,
                'error': '关键词不能为空'
            }), 400
        
        # 更新价格
        success = update_suggested_price(keyword, price_cny, price_rub)
        
        return jsonify({
            'success': success,
            'message': '价格更新成功' if success else '价格更新失败'
        })
        
    except Exception as e:
        print(f"❌ 更新价格失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/save_price', methods=['POST'])
def api_save_price():
    """保存价格API"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        price_cny = data.get('price_cny')
        price_rub = data.get('price_rub')

        if not keyword:
            return jsonify({
                'success': False,
                'error': '关键词不能为空'
            }), 400

        if not price_cny:
            return jsonify({
                'success': False,
                'error': '价格不能为空'
            }), 400

        # 更新价格
        success = update_suggested_price(keyword, price_cny, price_rub)

        if success:
            return jsonify({
                'success': True,
                'message': '价格保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '数据库更新失败'
            })

    except Exception as e:
        print(f"❌ 保存价格失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500

@dashboard_bp.route('/api/save-parameter', methods=['POST'])
def api_save_parameter():
    """保存单个参数API"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        field = data.get('field')
        value = data.get('value')

        if not keyword or not field:
            return jsonify({
                'success': False,
                'error': '关键词和字段名不能为空'
            }), 400

        if value is None:
            return jsonify({
                'success': False,
                'error': '参数值不能为空'
            }), 400

        # 将keywordId转换回原始keyword
        original_keyword = keyword.replace('_', ' ')

        # 保存到product_info表
        success = update_product_info_parameter(original_keyword, field, value)

        if success:
            return jsonify({
                'success': True,
                'message': f'参数 {field} 保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': f'保存参数 {field} 失败，可能未找到匹配的产品记录'
            })

    except Exception as e:
        print(f"❌ 保存参数失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/keyword-data')
def api_keyword_data():
    """获取单个关键词数据API（优化版本）"""
    try:
        keyword = request.args.get('keyword')
        if not keyword:
            return jsonify({
                'success': False,
                'error': '缺少关键词参数'
            }), 400

        # 直接查询指定关键词的数据
        keyword_data = get_single_keyword_data(keyword)

        if not keyword_data:
            return jsonify({
                'success': False,
                'error': '未找到指定关键词'
            }), 404

        return jsonify({
            'success': True,
            'data': keyword_data
        })

    except Exception as e:
        print(f"❌ 获取关键词数据失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/batch-update-empty-links', methods=['POST'])
def api_batch_update_empty_links():
    """批量更新商品链接为空的记录API"""
    try:
        print("🔄 开始批量更新商品链接为空的记录...")

        # 执行批量更新
        result = batch_update_empty_links_with_template_params()

        if result['success']:
            print(f"✅ 批量更新完成: {result}")
            return jsonify(result)
        else:
            print(f"❌ 批量更新失败: {result['error']}")
            return jsonify(result), 500

    except Exception as e:
        print(f"❌ 批量更新API失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_bp.route('/api/delete-product', methods=['POST'])
def api_delete_product():
    """删除商品链接API"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')
        product_index = data.get('product_index')  # 商品在列表中的索引（从0开始）

        if not keyword:
            return jsonify({
                'success': False,
                'error': '关键词不能为空'
            }), 400

        if product_index is None:
            return jsonify({
                'success': False,
                'error': '商品索引不能为空'
            }), 400

        from models.database import get_db_manager

        # 1. 获取当前的商品数据
        query = """
        SELECT prices, links FROM keywords_search
        WHERE keyword = %s
        """

        results = get_db_manager().execute_query(query, (keyword,))
        if not results:
            return jsonify({
                'success': False,
                'error': '未找到指定关键词的数据'
            }), 404

        row = results[0]
        current_prices = row['prices'] or ''
        current_links = row['links'] or ''

        # 2. 解析当前数据
        prices_list = current_prices.split(';') if current_prices else []
        links_list = current_links.split(';') if current_links else []

        # 3. 验证索引有效性
        if product_index < 0 or product_index >= len(prices_list) or product_index >= len(links_list):
            return jsonify({
                'success': False,
                'error': f'商品索引 {product_index} 超出范围'
            }), 400

        # 4. 删除指定索引的商品
        if product_index < len(prices_list):
            prices_list.pop(product_index)
        if product_index < len(links_list):
            links_list.pop(product_index)

        # 5. 重新组合数据
        new_prices = ';'.join(prices_list) if prices_list else ''
        new_links = ';'.join(links_list) if links_list else ''

        # 6. 更新数据库
        update_query = """
        UPDATE keywords_search
        SET prices = %s, links = %s, updated_at = CURRENT_TIMESTAMP
        WHERE keyword = %s
        """

        rows_affected = get_db_manager().execute_query(update_query, (new_prices, new_links, keyword), fetch=False)

        if rows_affected and rows_affected > 0:
            print(f"✅ 成功删除商品: {keyword} 索引 {product_index}")
            return jsonify({
                'success': True,
                'message': f'成功删除商品 {product_index + 1}',
                'remaining_count': len(prices_list)
            })
        else:
            return jsonify({
                'success': False,
                'error': '数据库更新失败'
            }), 500

    except Exception as e:
        print(f"❌ 删除商品失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500

@dashboard_bp.route('/api/clear-all-products', methods=['POST'])
def api_clear_all_products():
    """清空所有商品链接API"""
    try:
        data = request.get_json()
        keyword = data.get('keyword')

        if not keyword:
            return jsonify({
                'success': False,
                'error': '关键词不能为空'
            }), 400

        from models.database import get_db_manager

        # 清空指定关键词的所有商品数据
        update_query = """
        UPDATE keywords_search
        SET prices = '', links = '', updated_at = CURRENT_TIMESTAMP
        WHERE keyword = %s
        """

        rows_affected = get_db_manager().execute_query(update_query, (keyword,), fetch=False)

        if rows_affected and rows_affected > 0:
            print(f"✅ 成功清空所有商品: {keyword}")
            return jsonify({
                'success': True,
                'message': f'成功清空关键词 "{keyword}" 的所有商品链接'
            })
        else:
            return jsonify({
                'success': False,
                'error': '未找到指定关键词或数据库更新失败'
            }), 404

    except Exception as e:
        print(f"❌ 清空所有商品失败: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器错误: {str(e)}'
        }), 500

@dashboard_bp.route('/api/debug-keyword')
def api_debug_keyword():
    """调试关键词API"""
    try:
        keyword = request.args.get('keyword', 'IIIF150 B1')

        from models.database import get_db_manager

        # 直接查询关键词
        query = """
        SELECT keyword, links, CHAR_LENGTH(links) as links_length,
               CASE
                   WHEN links IS NULL THEN 'NULL'
                   WHEN links = '' THEN 'EMPTY_STRING'
                   WHEN links = '[]' THEN 'EMPTY_ARRAY'
                   ELSE 'HAS_DATA'
               END as links_status
        FROM keywords_search
        WHERE keyword LIKE %s
        """

        results = get_db_manager().execute_query(query, (f'%{keyword}%',))

        return jsonify({
            'success': True,
            'keyword': keyword,
            'results': results
        })

    except Exception as e:
        print(f"❌ 调试关键词失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500
