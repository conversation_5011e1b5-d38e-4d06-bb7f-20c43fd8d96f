-- 图片管理和OZON上传系统 - 数据库迁移脚本
-- 创建日期: 2025-06-22
-- 描述: 创建图片上传任务、OZON上传任务、系统配置等新增表

-- 创建图片上传任务表
CREATE TABLE IF NOT EXISTS image_upload_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(50) UNIQUE NOT NULL COMMENT '任务唯一标识',
    local_file_path VARCHAR(500) NOT NULL COMMENT '本地文件路径',
    github_url VARCHAR(500) COMMENT 'GitHub图片URL',
    github_repo_path VARCHAR(500) COMMENT 'GitHub仓库中的路径',
    product_id INT COMMENT '关联的产品ID',
    keyword VARCHAR(255) COMMENT '关联的关键词',
    file_size BIGINT COMMENT '文件大小(字节)',
    file_hash VARCHAR(64) COMMENT '文件哈希值',
    status ENUM('pending', 'uploading', 'success', 'failed') DEFAULT 'pending' COMMENT '上传状态',
    error_message TEXT COMMENT '错误信息',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_status (status),
    INDEX idx_product_id (product_id),
    INDEX idx_keyword (keyword),
    INDEX idx_task_id (task_id),
    FOREIGN KEY (product_id) REFERENCES product_info(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片上传任务表';

-- 创建OZON上传任务表
CREATE TABLE IF NOT EXISTS ozon_upload_tasks (
    id INT PRIMARY KEY AUTO_INCREMENT,
    task_id VARCHAR(50) UNIQUE NOT NULL COMMENT '任务唯一标识',
    product_ids JSON NOT NULL COMMENT '产品ID列表',
    ozon_task_id VARCHAR(100) COMMENT 'OZON返回的任务ID',
    batch_size INT DEFAULT 0 COMMENT '批次大小',
    status ENUM('pending', 'uploading', 'success', 'failed', 'partial') DEFAULT 'pending' COMMENT '上传状态',
    success_count INT DEFAULT 0 COMMENT '成功数量',
    failed_count INT DEFAULT 0 COMMENT '失败数量',
    error_details JSON COMMENT '错误详情',
    upload_config JSON COMMENT '上传配置',
    retry_count INT DEFAULT 0 COMMENT '重试次数',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    completed_at TIMESTAMP NULL COMMENT '完成时间',
    INDEX idx_status (status),
    INDEX idx_ozon_task_id (ozon_task_id),
    INDEX idx_task_id (task_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='OZON上传任务表';

-- 创建系统配置表
CREATE TABLE IF NOT EXISTS system_config (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    config_type ENUM('string', 'json', 'encrypted', 'boolean', 'integer') DEFAULT 'string' COMMENT '配置类型',
    description TEXT COMMENT '配置描述',
    is_sensitive BOOLEAN DEFAULT FALSE COMMENT '是否为敏感信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_config_key (config_key),
    INDEX idx_config_type (config_type),
    INDEX idx_is_sensitive (is_sensitive)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='系统配置表';

-- 创建图片产品映射表
CREATE TABLE IF NOT EXISTS image_product_mapping (
    id INT PRIMARY KEY AUTO_INCREMENT,
    image_filename VARCHAR(255) NOT NULL COMMENT '图片文件名',
    extracted_model_name VARCHAR(255) COMMENT '从文件名提取的型号',
    product_id INT COMMENT '匹配的产品ID',
    keyword VARCHAR(255) COMMENT '关联的关键词',
    match_confidence DECIMAL(3,2) DEFAULT 0.00 COMMENT '匹配置信度(0-1)',
    match_method ENUM('exact', 'fuzzy', 'manual', 'keyword') DEFAULT 'exact' COMMENT '匹配方法',
    is_verified BOOLEAN DEFAULT FALSE COMMENT '是否已验证',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_filename (image_filename),
    INDEX idx_product_id (product_id),
    INDEX idx_keyword (keyword),
    INDEX idx_match_confidence (match_confidence),
    INDEX idx_match_method (match_method),
    FOREIGN KEY (product_id) REFERENCES product_info(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='图片产品映射表';

-- 创建配置审计日志表
CREATE TABLE IF NOT EXISTS config_audit_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id VARCHAR(100) COMMENT '用户ID',
    config_key VARCHAR(100) COMMENT '配置键',
    action ENUM('create', 'update', 'delete') NOT NULL COMMENT '操作类型',
    old_value TEXT COMMENT '旧值',
    new_value TEXT COMMENT '新值',
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '操作时间',
    INDEX idx_config_key (config_key),
    INDEX idx_timestamp (timestamp),
    INDEX idx_user_id (user_id),
    INDEX idx_action (action)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='配置审计日志表';
