#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用工具函数 - OZON价格计算器
"""

import re
import requests
import time
from typing import Dict, Any, Optional

# 汇率缓存
_exchange_rate_cache = {
    'rate': 13.5,  # 默认汇率
    'timestamp': 0,  # 上次获取时间
    'cache_duration': 3600  # 缓存1小时
}

def get_current_exchange_rate() -> float:
    """
    获取当前CNY到RUB的实时汇率（带缓存）

    Returns:
        汇率值（1 CNY = X RUB）
    """
    global _exchange_rate_cache
    current_time = time.time()

    # 检查缓存是否有效
    if (current_time - _exchange_rate_cache['timestamp']) < _exchange_rate_cache['cache_duration']:
        return _exchange_rate_cache['rate']

    try:
        # 使用免费的汇率API
        response = requests.get('https://api.exchangerate-api.com/v4/latest/CNY', timeout=5)
        if response.status_code == 200:
            data = response.json()
            rate = data.get('rates', {}).get('RUB', 13.5)

            # 更新缓存
            _exchange_rate_cache['rate'] = rate
            _exchange_rate_cache['timestamp'] = current_time

            print(f"✅ 获取并缓存实时汇率: 1 CNY = {rate} RUB")
            return rate
    except Exception as e:
        print(f"⚠️ 获取实时汇率失败: {e}")

    # 返回缓存中的汇率或默认汇率
    return _exchange_rate_cache['rate']

def calculateLogisticsCost(weight: int, price_rub: float = 0) -> Dict[str, Any]:
    """
    根据重量和价格计算OZON物流费用

    Args:
        weight: 商品重量（克）
        price_rub: 商品价格（卢布）

    Returns:
        物流费用详情字典
    """
    # 根据价格和重量确定物流分组
    if price_rub <= 1500:
        # 价格≤1500卢布
        if weight >= 1 and weight <= 500:
            # Extra Small: 1-500克, 最高1500卢布
            base_fee = 2.9
            weight_fee_per_gram = 0.035
            logistics_group = "Extra Small"
        elif weight >= 501 and weight <= 25000:
            # Budget: 501-25000克, 最高1500卢布
            base_fee = 22.5
            weight_fee_per_gram = 0.025
            logistics_group = "Budget"
        else:
            # 默认使用Extra Small
            base_fee = 2.9
            weight_fee_per_gram = 0.035
            logistics_group = "Extra Small"
    elif price_rub >= 1501 and price_rub <= 7000:
        # 价格1501-7000卢布
        if weight >= 1 and weight <= 2000:
            # Small: 1-2000克, 1501-7000卢布
            base_fee = 16.0
            weight_fee_per_gram = 0.043
            logistics_group = "Small"
        elif weight >= 2001 and weight <= 25000:
            # Big: 2001-25000克, 1501-7000卢布
            base_fee = 25.0
            weight_fee_per_gram = 0.05
            logistics_group = "Big"
        else:
            # 默认使用Small
            base_fee = 16.0
            weight_fee_per_gram = 0.043
            logistics_group = "Small"
    elif price_rub >= 7001 and price_rub <= 250000:
        # 价格7001-250000卢布
        if weight >= 1 and weight <= 5000:
            # Premium Small: 1-5000克, 7001-250000卢布
            base_fee = 30.0
            weight_fee_per_gram = 0.06
            logistics_group = "Premium Small"
        elif weight >= 5001 and weight <= 25000:
            # Premium Big: 5001-25000克, 7001-250000卢布
            base_fee = 40.0
            weight_fee_per_gram = 0.08
            logistics_group = "Premium Big"
        else:
            # 默认使用Premium Small
            base_fee = 30.0
            weight_fee_per_gram = 0.06
            logistics_group = "Premium Small"
    else:
        # 价格超出范围，使用默认Small
        base_fee = 16.0
        weight_fee_per_gram = 0.043
        logistics_group = "Small (Default)"

    weight_fee = weight * weight_fee_per_gram
    total_logistics_cost = base_fee + weight_fee

    return {
        'cost': total_logistics_cost,
        'group': logistics_group,
        'base_fee': base_fee,
        'weight_fee': weight_fee,
        'weight_fee_per_gram': weight_fee_per_gram,
        'weight': weight,
        'price_rub': price_rub,
        'details': f"{logistics_group}: ¥{base_fee:.2f}(基础费) + ¥{weight_fee_per_gram:.3f}/克 × {weight}克 = ¥{total_logistics_cost:.2f}"
    }

def calculateDetailedOzonPrice(keyword: str, procurement_cost: float = 46.0,
                              packaging_cost: float = 10.0, weight: int = 180,
                              target_profit: float = 100.0, promo_discount: float = 15.0,
                              second_promo_discount: float = 10.0) -> Dict[str, Any]:
    """
    详细的OZON价格计算器 - 为每个定价策略单独计算物流费用

    Args:
        keyword: 商品关键词
        procurement_cost: 采购成本（人民币）
        packaging_cost: 包装成本（人民币）
        weight: 商品重量（克）
        target_profit: 目标利润（人民币）
        promo_discount: 一级促销折扣（百分比）
        second_promo_discount: 二级促销折扣（百分比）

    Returns:
        包含详细计算结果的字典
    """
    try:
        # 获取当前汇率
        exchange_rate = get_current_exchange_rate()

        # OZON佣金率（分层）
        commission_threshold = 1500  # 卢布
        commission_low = 0.12  # 12% for products under 1500 RUB
        commission_high = 0.20  # 20% for products over 1500 RUB
        
        # OZON佣金率（分层）
        commission_threshold = 1500  # 卢布
        commission_low = 0.12  # 12% for products under 1500 RUB
        commission_high = 0.20  # 20% for products over 1500 RUB

        # 计算三种定价策略
        results = []

        # 策略1：基础定价（使用传入的目标利润）
        target_profit_cny = target_profit
        target_profit_rub = target_profit_cny * exchange_rate

        # 先用临时物流费用计算初步价格
        temp_logistics = calculateLogisticsCost(weight, 3000)  # 使用中等价格估算
        temp_total_cost_cny = procurement_cost + packaging_cost + temp_logistics['cost']
        temp_total_cost_rub = temp_total_cost_cny * exchange_rate

        # 计算售价（考虑佣金）
        base_price_rub = temp_total_cost_rub + target_profit_rub
        commission_rate = commission_low if base_price_rub < commission_threshold else commission_high
        selling_price_rub = base_price_rub / (1 - commission_rate)
        selling_price_cny = selling_price_rub / exchange_rate

        # 根据实际售价重新计算物流费用
        logistics_info = calculateLogisticsCost(weight, selling_price_rub)
        actual_total_cost_cny = procurement_cost + packaging_cost + logistics_info['cost']
        actual_total_cost_rub = actual_total_cost_cny * exchange_rate

        # 重新计算最终售价
        final_base_price_rub = actual_total_cost_rub + target_profit_rub
        final_commission_rate = commission_low if final_base_price_rub < commission_threshold else commission_high
        final_selling_price_rub = final_base_price_rub / (1 - final_commission_rate)
        final_selling_price_cny = final_selling_price_rub / exchange_rate

        # 实际佣金和利润
        actual_commission_rub = final_selling_price_rub * final_commission_rate
        actual_commission_cny = actual_commission_rub / exchange_rate
        actual_profit_rub = final_selling_price_rub - actual_total_cost_rub - actual_commission_rub
        actual_profit_cny = actual_profit_rub / exchange_rate
        profit_margin = (actual_profit_cny / final_selling_price_cny) * 100
        
        results.append({
            'type': '建议售价',
            'selling_price_cny': final_selling_price_cny,
            'selling_price_rub': final_selling_price_rub,
            'commission_rate': final_commission_rate * 100,
            'commission_cny': actual_commission_cny,
            'commission_rub': actual_commission_rub,
            'profit_cny': actual_profit_cny,
            'profit_rub': actual_profit_rub,
            'profit_margin': profit_margin,
            'logistics_info': logistics_info
        })

        # 策略2：一级促销（使用传入的折扣）
        promo_discount_rate = promo_discount / 100.0
        promo_price_rub = final_selling_price_rub * (1 - promo_discount_rate)
        promo_price_cny = promo_price_rub / exchange_rate

        # 根据促销价格重新计算物流费用
        promo_logistics_info = calculateLogisticsCost(weight, promo_price_rub)
        promo_total_cost_cny = procurement_cost + packaging_cost + promo_logistics_info['cost']
        promo_total_cost_rub = promo_total_cost_cny * exchange_rate

        promo_commission_rate = commission_low if promo_price_rub < commission_threshold else commission_high
        promo_commission_rub = promo_price_rub * promo_commission_rate
        promo_commission_cny = promo_commission_rub / exchange_rate
        promo_profit_rub = promo_price_rub - promo_total_cost_rub - promo_commission_rub
        promo_profit_cny = promo_profit_rub / exchange_rate
        promo_profit_margin = (promo_profit_cny / promo_price_cny) * 100
        
        results.append({
            'type': '一级促销',
            'selling_price_cny': promo_price_cny,
            'selling_price_rub': promo_price_rub,
            'commission_rate': promo_commission_rate * 100,
            'commission_cny': promo_commission_cny,
            'commission_rub': promo_commission_rub,
            'profit_cny': promo_profit_cny,
            'profit_rub': promo_profit_rub,
            'profit_margin': promo_profit_margin,
            'discount': promo_discount,
            'logistics_info': promo_logistics_info
        })

        # 策略3：二级促销（使用传入的二级折扣）
        second_discount_rate = second_promo_discount / 100.0
        second_promo_price_rub = final_selling_price_rub * (1 - second_discount_rate)
        second_promo_price_cny = second_promo_price_rub / exchange_rate

        # 根据二级促销价格重新计算物流费用
        second_logistics_info = calculateLogisticsCost(weight, second_promo_price_rub)
        second_total_cost_cny = procurement_cost + packaging_cost + second_logistics_info['cost']
        second_total_cost_rub = second_total_cost_cny * exchange_rate

        second_commission_rate = commission_low if second_promo_price_rub < commission_threshold else commission_high
        second_commission_rub = second_promo_price_rub * second_commission_rate
        second_commission_cny = second_commission_rub / exchange_rate
        second_profit_rub = second_promo_price_rub - second_total_cost_rub - second_commission_rub
        second_profit_cny = second_profit_rub / exchange_rate
        second_profit_margin = (second_profit_cny / second_promo_price_cny) * 100
        
        results.append({
            'type': '二级促销',
            'selling_price_cny': second_promo_price_cny,
            'selling_price_rub': second_promo_price_rub,
            'commission_rate': second_commission_rate * 100,
            'commission_cny': second_commission_cny,
            'commission_rub': second_commission_rub,
            'profit_cny': second_profit_cny,
            'profit_rub': second_profit_rub,
            'profit_margin': second_profit_margin,
            'discount': second_promo_discount,
            'logistics_info': second_logistics_info
        })

        return {
            'success': True,
            'keyword': keyword,
            'exchange_rate': exchange_rate,
            'costs': {
                'procurement_cny': procurement_cost,
                'packaging_cny': packaging_cost,
                'logistics_cny': logistics_info['cost'],  # 使用建议售价的物流费用作为基准
                'total_cny': actual_total_cost_cny,
                'total_rub': actual_total_cost_rub
            },
            'logistics_details': {
                'weight': weight,
                'base_cost': logistics_info['base_fee'],
                'per_gram_cost': logistics_info['weight_fee_per_gram'],
                'total_cost': logistics_info['cost'],
                'group': logistics_info['group'],
                'details': logistics_info['details']
            },
            'pricing_strategies': results
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'keyword': keyword
        }

def extract_suggested_price(answer: str) -> Dict[str, Any]:
    """
    从AI分析中提取建议价格信息并转换为人民币

    Args:
        answer: AI分析文本

    Returns:
        包含价格信息的字典
    """
    if not answer or answer.strip() == '':
        return {'rub': '未提取到价格', 'cny': '未提取到价格'}

    # 匹配多种价格建议格式
    patterns = [
        r'建议价格在\s*(\d+[\d\s,]*)\s*[₽卢布]',
        r'建议.*?(\d+[\d\s,]*)\s*[₽卢布]',
        r'价格.*?(\d+[\d\s,]*)\s*[₽卢布]',
        r'(\d+[\d\s,]*)\s*[₽卢布].*?竞争'
    ]

    for pattern in patterns:
        m = re.search(pattern, str(answer), re.IGNORECASE)
        if m:
            rub_price = m.group(1).replace(' ', '').replace(',', '')
            try:
                # 使用当前汇率转换为人民币
                rub_amount = float(rub_price)
                exchange_rate = get_current_exchange_rate()
                cny_amount = rub_amount / exchange_rate

                rub_display = f"{rub_price} ₽"
                cny_display = f"¥{cny_amount:.2f}"

                return {
                    'rub': rub_display,
                    'cny': cny_display,
                    'rub_value': rub_amount,
                    'cny_value': cny_amount
                }
            except ValueError:
                pass

    return {'rub': '未提取到价格', 'cny': '未提取到价格'}
