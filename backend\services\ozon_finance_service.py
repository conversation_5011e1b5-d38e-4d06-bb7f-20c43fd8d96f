#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON财务交易数据同步服务
"""

import json
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from models.database import get_db_manager

logger = logging.getLogger(__name__)

class OzonFinanceService:
    """OZON财务交易数据同步服务"""
    
    def __init__(self):
        self.db_manager = get_db_manager()
        self.api_config = None
        self._load_api_config()
    
    def _load_api_config(self):
        """加载财务API配置"""
        try:
            query = """
            SELECT 
                api_path, base_url, http_method,
                request_body_schema, request_example,
                auth_headers, request_headers
            FROM platform_api_data 
            WHERE id = 7 AND is_active = 1
            """
            
            result = self.db_manager.execute_query(query)
            if result:
                self.api_config = result[0]
                logger.info("✅ 财务API配置加载成功")
            else:
                logger.error("❌ 未找到财务API配置")
                
        except Exception as e:
            logger.error(f"❌ 加载财务API配置失败: {e}")
    
    def get_latest_transaction_date(self) -> Optional[str]:
        """获取数据库中最新的交易日期"""
        try:
            query = """
            SELECT MAX(operation_date) as latest_date 
            FROM ozon_finance_transactions
            """
            
            result = self.db_manager.execute_query(query)
            if result and result[0]['latest_date']:
                latest_date = result[0]['latest_date']
                # 转换为字符串格式
                if isinstance(latest_date, datetime):
                    return latest_date.strftime('%Y-%m-%d')
                return str(latest_date)[:10]
            
            # 如果没有数据，返回30天前的日期
            default_date = datetime.now() - timedelta(days=30)
            return default_date.strftime('%Y-%m-%d')
            
        except Exception as e:
            logger.error(f"❌ 获取最新交易日期失败: {e}")
            return None
    
    def fetch_finance_transactions(self, date_from: str, date_to: str = None) -> List[Dict]:
        """从OZON API获取财务交易数据"""
        if not self.api_config:
            logger.error("❌ 财务API配置未加载")
            return []
        
        if not date_to:
            date_to = datetime.now().strftime('%Y-%m-%d')
        
        try:
            # 构建请求URL
            base_url = self.api_config.get('base_url', 'https://api-seller.ozon.ru')
            api_path = self.api_config.get('api_path', '/v3/finance/transaction/list')
            url = f"{base_url}{api_path}"
            
            # 构建请求体
            request_body = {
                "filter": {
                    "date": {
                        "from": f"{date_from}T00:00:00.000Z",
                        "to": f"{date_to}T23:59:59.999Z"
                    },
                    "operation_type": [],
                    "posting_number": "",
                    "transaction_type": "all"
                },
                "page": 1,
                "page_size": 1000
            }
            
            # 构建请求头
            headers = {
                'Content-Type': 'application/json',
                'Client-Id': '2024407390',  # 从现有配置获取
                'Api-Key': 'your-api-key-here'  # 需要配置实际的API密钥
            }
            
            logger.info(f"🔄 请求财务交易数据: {date_from} 到 {date_to}")
            logger.info(f"📡 请求URL: {url}")
            
            response = requests.post(
                url,
                json=request_body,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('result') and data['result'].get('operations'):
                    transactions = data['result']['operations']
                    logger.info(f"✅ 获取到 {len(transactions)} 条财务交易记录")
                    return transactions
                else:
                    logger.warning("⚠️ API响应中没有交易数据")
                    return []
            else:
                logger.error(f"❌ API请求失败: {response.status_code}")
                logger.error(f"响应内容: {response.text}")
                return []
                
        except Exception as e:
            logger.error(f"❌ 获取财务交易数据失败: {e}")
            return []
    
    def save_transactions_to_db(self, transactions: List[Dict]) -> Tuple[int, int]:
        """保存交易数据到数据库"""
        if not transactions:
            return 0, 0
        
        success_count = 0
        error_count = 0
        
        try:
            for transaction in transactions:
                try:
                    # 提取交易数据
                    operation_id = transaction.get('operation_id', '')
                    operation_type = transaction.get('operation_type', '')
                    operation_type_name = transaction.get('operation_type_name', '')
                    operation_date = transaction.get('operation_date', '')
                    
                    # 处理金额和货币
                    accruals_for_sale = transaction.get('accruals_for_sale', {})
                    amount = float(accruals_for_sale.get('amount', 0))
                    currency_code = accruals_for_sale.get('currency_code', 'RUB')
                    
                    # 处理其他字段
                    posting_number = transaction.get('posting', {}).get('posting_number', '')
                    services = transaction.get('services', [])
                    
                    # 确定交易类别
                    transaction_category = self._determine_transaction_category(operation_type)
                    
                    # 检查是否已存在
                    check_query = """
                    SELECT id FROM ozon_finance_transactions 
                    WHERE operation_id = %s
                    """
                    
                    existing = self.db_manager.execute_query(check_query, (operation_id,))
                    
                    if existing:
                        logger.debug(f"⏭️ 交易已存在，跳过: {operation_id}")
                        continue
                    
                    # 插入新记录
                    insert_query = """
                    INSERT INTO ozon_finance_transactions (
                        operation_id, operation_type, operation_type_name,
                        operation_date, amount, currency_code,
                        posting_number, transaction_category,
                        services_data, raw_data,
                        created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        NOW(), NOW()
                    )
                    """
                    
                    params = (
                        operation_id,
                        operation_type,
                        operation_type_name,
                        operation_date,
                        amount,
                        currency_code,
                        posting_number,
                        transaction_category,
                        json.dumps(services, ensure_ascii=False),
                        json.dumps(transaction, ensure_ascii=False)
                    )
                    
                    result = self.db_manager.execute_query(insert_query, params, fetch=False)
                    
                    if result:
                        success_count += 1
                        logger.debug(f"✅ 保存交易成功: {operation_id}")
                    else:
                        error_count += 1
                        logger.error(f"❌ 保存交易失败: {operation_id}")
                        
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ 处理交易记录失败: {e}")
                    logger.error(f"交易数据: {transaction}")
            
            logger.info(f"📊 交易数据保存完成: 成功 {success_count} 条, 失败 {error_count} 条")
            return success_count, error_count
            
        except Exception as e:
            logger.error(f"❌ 批量保存交易数据失败: {e}")
            return success_count, error_count
    
    def _determine_transaction_category(self, operation_type: str) -> str:
        """根据操作类型确定交易类别"""
        category_mapping = {
            'OperationAgentDeliveredToCustomer': 'sales_income',
            'MarketplaceRedistributionOfAcquiringOperation': 'acquiring_fee',
            'MarketplaceRedistributionOfDeliveryServicesOperation': 'delivery_fee',
            'OperationMarketplaceAgencyFeeAggregator3PLGlobal': 'agency_fee',
            'OperationClaim': 'claim',
            'OperationCorrectionSeller': 'correction',
            'OperationMarketplaceServicePremiumPromotion': 'promotion_fee'
        }
        
        return category_mapping.get(operation_type, 'other')
    
    def sync_latest_transactions(self) -> Dict:
        """同步最新的财务交易数据"""
        logger.info("🔄 开始同步最新财务交易数据")
        
        try:
            # 获取最新日期
            latest_date = self.get_latest_transaction_date()
            if not latest_date:
                logger.error("❌ 无法获取最新交易日期")
                return {'success': False, 'error': '无法获取最新交易日期'}
            
            # 从最新日期开始同步到今天
            today = datetime.now().strftime('%Y-%m-%d')
            
            logger.info(f"📅 同步日期范围: {latest_date} 到 {today}")
            
            # 获取交易数据
            transactions = self.fetch_finance_transactions(latest_date, today)
            
            if not transactions:
                logger.info("ℹ️ 没有新的交易数据")
                return {
                    'success': True,
                    'message': '没有新的交易数据',
                    'sync_count': 0,
                    'error_count': 0
                }
            
            # 保存到数据库
            success_count, error_count = self.save_transactions_to_db(transactions)
            
            result = {
                'success': True,
                'message': f'财务交易数据同步完成',
                'sync_count': success_count,
                'error_count': error_count,
                'date_range': f'{latest_date} 到 {today}',
                'total_fetched': len(transactions)
            }
            
            logger.info(f"🎉 财务交易数据同步完成: {result}")
            return result
            
        except Exception as e:
            logger.error(f"❌ 同步财务交易数据失败: {e}")
            return {
                'success': False,
                'error': f'同步失败: {str(e)}'
            }


# 创建服务实例
ozon_finance_service = OzonFinanceService()


def sync_finance_transactions():
    """同步财务交易数据的便捷函数"""
    return ozon_finance_service.sync_latest_transactions()


if __name__ == '__main__':
    # 直接运行时执行同步
    result = sync_finance_transactions()
    print(json.dumps(result, ensure_ascii=False, indent=2))
