#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
上传超时检测定时任务
"""

import logging
from datetime import datetime
from services.upload_timeout_detector import UploadTimeoutDetector

logger = logging.getLogger(__name__)

def check_upload_timeouts():
    """
    检查上传超时的定时任务
    每5分钟执行一次
    """
    try:
        logger.info("开始执行上传超时检测任务")
        start_time = datetime.now()
        
        detector = UploadTimeoutDetector()
        
        # 1. 检测超时商品
        timeout_products = detector.detect_timeout_products()
        
        if not timeout_products:
            logger.info("没有发现超时商品")
            return
        
        # 2. 处理每个超时商品
        processed_count = 0
        failed_count = 0
        
        for product in timeout_products:
            product_id = product['id']
            product_name = product.get('product_name', 'Unknown')
            upload_duration = product.get('upload_duration_minutes', 0)
            
            logger.info(f"处理超时商品: ID={product_id}, 名称={product_name}, 上传时长={upload_duration}分钟")
            
            if detector.handle_timeout_product(product_id):
                processed_count += 1
            else:
                failed_count += 1
        
        # 3. 处理准备重试的商品
        retry_products = detector.get_products_ready_for_retry()
        retry_count = 0
        
        for product in retry_products:
            product_id = product['id']
            if detector.reset_product_for_retry(product_id):
                retry_count += 1
                logger.info(f"商品 {product_id} 已重置为待上传状态")
        
        # 4. 记录执行结果
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        logger.info(f"上传超时检测任务完成: "
                   f"检测到超时商品={len(timeout_products)}, "
                   f"处理成功={processed_count}, "
                   f"处理失败={failed_count}, "
                   f"重试商品={retry_count}, "
                   f"耗时={duration:.2f}秒")
        
        # 5. 检查是否需要告警
        _check_alert_threshold(detector, len(timeout_products))
        
    except Exception as e:
        logger.error(f"上传超时检测任务执行失败: {e}")

def cleanup_upload_logs():
    """
    清理旧的上传日志
    每天凌晨2点执行
    """
    try:
        logger.info("开始执行上传日志清理任务")
        
        detector = UploadTimeoutDetector()
        deleted_count = detector.cleanup_old_logs()
        
        logger.info(f"上传日志清理任务完成，清理了 {deleted_count} 条记录")
        
    except Exception as e:
        logger.error(f"上传日志清理任务执行失败: {e}")

def generate_timeout_report():
    """
    生成超时报告
    每小时执行一次
    """
    try:
        logger.info("开始生成超时报告")
        
        detector = UploadTimeoutDetector()
        stats = detector.get_timeout_statistics()
        
        if stats:
            logger.info(f"超时统计报告: "
                       f"总商品数={stats.get('total_products', 0)}, "
                       f"上传中={stats.get('uploading_count', 0)}, "
                       f"超时={stats.get('timeout_count', 0)}, "
                       f"重试中={stats.get('retrying_count', 0)}, "
                       f"超时失败={stats.get('failed_by_timeout_count', 0)}, "
                       f"平均超时次数={stats.get('avg_timeout_count', 0):.2f}")
        
    except Exception as e:
        logger.error(f"生成超时报告失败: {e}")

def _check_alert_threshold(detector: UploadTimeoutDetector, timeout_count: int):
    """
    检查是否需要告警
    
    Args:
        detector: 超时检测器实例
        timeout_count: 当前超时商品数量
    """
    try:
        from services.config_service import ExtendedConfig
        config = ExtendedConfig()
        
        alert_threshold = int(config.get_config('upload_timeout.alert_threshold', '10'))
        
        if timeout_count >= alert_threshold:
            logger.warning(f"⚠️ 超时商品数量告警: 当前超时商品 {timeout_count} 个，超过阈值 {alert_threshold}")
            
            # 这里可以集成邮件、短信、钉钉等告警方式
            # 暂时只记录日志
            
    except Exception as e:
        logger.error(f"检查告警阈值失败: {e}")

# 如果使用Celery，可以这样定义任务
try:
    from celery import Celery
    
    # 假设已经配置了Celery实例
    app = Celery('upload_timeout_tasks')
    
    @app.task
    def celery_check_upload_timeouts():
        """Celery版本的超时检测任务"""
        return check_upload_timeouts()
    
    @app.task
    def celery_cleanup_upload_logs():
        """Celery版本的日志清理任务"""
        return cleanup_upload_logs()
    
    @app.task
    def celery_generate_timeout_report():
        """Celery版本的报告生成任务"""
        return generate_timeout_report()
        
except ImportError:
    # 如果没有安装Celery，跳过
    pass

# 如果使用APScheduler，可以这样配置
def setup_scheduler():
    """
    设置定时任务调度器
    """
    try:
        from apscheduler.schedulers.background import BackgroundScheduler
        from apscheduler.triggers.cron import CronTrigger
        
        scheduler = BackgroundScheduler()
        
        # 每5分钟检查一次超时
        scheduler.add_job(
            check_upload_timeouts,
            CronTrigger(minute='*/5'),
            id='check_upload_timeouts',
            name='检查上传超时',
            replace_existing=True
        )
        
        # 每天凌晨2点清理日志
        scheduler.add_job(
            cleanup_upload_logs,
            CronTrigger(hour=2, minute=0),
            id='cleanup_upload_logs',
            name='清理上传日志',
            replace_existing=True
        )
        
        # 每小时生成报告
        scheduler.add_job(
            generate_timeout_report,
            CronTrigger(minute=0),
            id='generate_timeout_report',
            name='生成超时报告',
            replace_existing=True
        )
        
        scheduler.start()
        logger.info("定时任务调度器启动成功")
        
        return scheduler
        
    except ImportError:
        logger.warning("APScheduler未安装，无法启动定时任务")
        return None
    except Exception as e:
        logger.error(f"设置定时任务失败: {e}")
        return None

if __name__ == "__main__":
    # 直接运行时执行一次检测
    logging.basicConfig(level=logging.INFO)
    check_upload_timeouts()
