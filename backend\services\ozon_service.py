#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON上传服务
基于tempfile/ozon_product_uploader.py，提供产品批量上传到OZON的服务
"""

import requests
import json
import uuid
import logging
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from models.database import db_manager
from services.config_service import config_service

logger = logging.getLogger(__name__)

# OZON API配置
OZON_BASE_URL = "https://api-seller.ozon.ru"

# 固定的分类和类型ID
DESCRIPTION_CATEGORY_ID = 53567477
TYPE_ID = 504866229
LANGUAGE = "ZH_HANS"

class OzonService:
    """OZON上传服务类"""
    
    def __init__(self):
        self.client_id = None
        self.api_key = None
        self._load_config()
    
    def _load_config(self):
        """加载OZON配置 - 优先从环境变量读取"""
        try:
            import os

            # 优先从环境变量读取敏感配置
            # 注意：敏感配置只从环境变量读取，不再从config_service获取
            self.client_id = os.environ.get('OZON_CLIENT_ID') or config_service.get_config('ozon_client_id', '2962343')
            self.api_key = os.environ.get('OZON_API_KEY') or ''

            if not self.api_key:
                logger.warning("OZON API Key未配置，请设置OZON_API_KEY环境变量")
            else:
                logger.info(f"OZON配置加载成功，Client ID: {self.client_id}")

        except Exception as e:
            logger.error(f"加载OZON配置失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self._load_config()
    
    def get_products_ready_for_upload(self, limit: int = 10, allow_reuploads: bool = True) -> List[Dict]:
        """获取准备上传的产品列表

        Args:
            limit: 返回的产品数量限制
            allow_reuploads: 是否允许重新上传已上传的产品
        """
        try:
            if allow_reuploads:
                # 允许重新上传：包括所有有图片的产品
                query = """
                SELECT * FROM product_info
                WHERE main_image_url IS NOT NULL
                AND main_image_url != ''
                AND main_image_url != 'NULL'
                ORDER BY
                    CASE
                        WHEN ozon_upload_status IS NULL OR ozon_upload_status = 'none' THEN 1
                        WHEN ozon_upload_status = 'failed' THEN 2
                        WHEN ozon_upload_status = 'uploaded' THEN 3
                        ELSE 4
                    END,
                    last_image_update DESC
                LIMIT %s
                """
            else:
                # 原有逻辑：只获取未上传的产品
                query = """
                SELECT * FROM product_info
                WHERE main_image_url IS NOT NULL
                AND main_image_url != ''
                AND main_image_url != 'NULL'
                AND (ozon_upload_status IS NULL OR ozon_upload_status = 'none')
                ORDER BY last_image_update DESC
                LIMIT %s
                """

            results = db_manager.execute_query(query, (limit,))

            if results:
                logger.info(f"获取到 {len(results)} 个可上传的产品 (允许重新上传: {allow_reuploads})")
                return results
            else:
                logger.info("没有找到可上传的产品")
                return []

        except Exception as e:
            logger.error(f"获取准备上传的产品失败: {e}")
            return []
    
    def build_rich_content_json(self, product: Dict) -> str:
        """构建JSON富内容"""
        try:
            model_name = product.get('model_name', 'Unknown')

            rich_content = {
                "content": [
                    {
                        "widgetName": "list",
                        "theme": "bullet",
                        "blocks": [
                            {
                                "text": {
                                    "size": "size2",
                                    "align": "left",
                                    "color": "color1",
                                    "items": [
                                        {
                                            "type": "text",
                                            "content": f"Подходит для {model_name}. Проверьте модель вашего устройства перед покупкой."
                                        }
                                    ]
                                },
                                "title": {
                                    "items": [{"type": "text", "content": "【Совместимость】"}],
                                    "size": "size4",
                                    "align": "left",
                                    "color": "color1"
                                }
                            },
                            {
                                "text": {
                                    "size": "size2",
                                    "align": "left",
                                    "color": "color1",
                                    "items": [
                                        {
                                            "type": "text",
                                            "content": "Для замены старого, разбитого, поцарапанного или поврежденного LCD дисплей."
                                        }
                                    ]
                                },
                                "title": {
                                    "items": [{"type": "text", "content": "【Замена】"}],
                                    "size": "size4",
                                    "align": "left",
                                    "color": "color1"
                                }
                            },
                            {
                                "text": {
                                    "size": "size2",
                                    "align": "left",
                                    "color": "color1",
                                    "items": [
                                        {
                                            "type": "text",
                                            "content": "Все продукты проходят строгий двойной тест на заводе, 100% идеальное состояние перед отправкой."
                                        }
                                    ]
                                },
                                "title": {
                                    "items": [{"type": "text", "content": "【Качество】"}],
                                    "size": "size4",
                                    "align": "left",
                                    "color": "color1"
                                }
                            },
                            {
                                "text": {
                                    "size": "size2",
                                    "align": "left",
                                    "color": "color1",
                                    "items": [
                                        {
                                            "type": "text",
                                            "content": "Это технический процесс, обычно выполняемый профессиональными инженерами. Мы настоятельно рекомендуем посмотреть обучающее видео перед ремонтом и рекомендуем доверить работу профессиональным инженерам, чтобы снизить риск повреждения телефона."
                                        }
                                    ]
                                },
                                "title": {
                                    "items": [{"type": "text", "content": "【Установка】"}],
                                    "size": "size4",
                                    "align": "left",
                                    "color": "color1"
                                }
                            },
                            {
                                "text": {
                                    "size": "size2",
                                    "align": "left",
                                    "color": "color1",
                                    "items": [
                                        {
                                            "type": "text",
                                            "content": "Если у вас возникнут какие-либо вопросы, пожалуйста, свяжитесь с нами, и мы ответим вам в течение 24 часов."
                                        }
                                    ]
                                },
                                "title": {
                                    "items": [{"type": "text", "content": "【Сервис】"}],
                                    "size": "size4",
                                    "align": "left",
                                    "color": "color1"
                                }
                            }
                        ]
                    }
                ],
                "version": 0.3
            }

            return json.dumps(rich_content, ensure_ascii=False, separators=(',', ':'))

        except Exception as e:
            logger.error(f"构建JSON富内容失败: {e}")
            return ""

    def build_product_attributes(self, product: Dict) -> List[Dict]:
        """构建产品属性数据"""
        try:
            attributes = [
                # 必填属性
                {
                    "id": 85,  # 品牌 - 使用"Нет бренда"
                    "complex_id": 0,
                    "values": [{"dictionary_value_id": 126745801}]
                },
                {
                    "id": 8229,  # 类型
                    "complex_id": 0,
                    "values": [{"dictionary_value_id": TYPE_ID}]
                },
                {
                    "id": 22444,  # 备件种类
                    "complex_id": 0,
                    "values": [{"dictionary_value_id": 971738583}]
                },
                {
                    "id": 9048,  # 商品代码
                    "complex_id": 0,
                    "values": [{"value": product.get('item_code', 'Unknown Code')}]
                },
                # 可选属性
                {
                    "id": 4389,  # 制造国
                    "complex_id": 0,
                    "values": [{"dictionary_value_id": 90296}]
                },
                {
                    "id": 10096,  # 商品颜色
                    "complex_id": 0,
                    "values": [{"dictionary_value_id": 61574}]
                },
                {
                    "id": 21615,  # 材料
                    "complex_id": 0,
                    "values": [{"dictionary_value_id": 970742314}]
                },
                {
                    "id": 4382,  # 尺寸
                    "complex_id": 0,
                    "values": [{"value": product.get('dimensions', '168.0 x 81.0 x 17.0 mm')}]
                },
                {
                    "id": 4383,  # 重量
                    "complex_id": 0,
                    "values": [{"value": str(int(float(product.get('gross_weight', '180')) or 100))}]
                },
                {
                    "id": 4180,  # 产品名称
                    "complex_id": 0,
                    "values": [{"value": product.get('product_name', f"Дисплей для смартфона, предназначенный для модели {product.get('model_name', 'Unknown')}, цифровой сенсорный компонент экрана, черный каркас.")}]
                },
                {
                    "id": 4191,  # 描述 - 使用单个换行符
                    "complex_id": 0,
                    "values": [{"value": f"✔ 【Совместимость】: Подходит для {product.get('model_name', 'Unknown')}. Проверьте модель вашего устройства перед покупкой.\n✔ 【Замена】: Для замены старого, разбитого, поцарапанного или поврежденного LCD дисплей.\n✔ 【Качество】: Все продукты проходят строгий двойной тест на заводе, 100% идеальное состояние перед отправкой.\n✔【Установка】: Это технический процесс, обычно выполняемый профессиональными инженерами. Мы настоятельно рекомендуем посмотреть обучающее видео перед ремонтом и рекомендуем доверить работу профессиональным инженерам, чтобы снизить риск повреждения телефона.\n✔ 【Сервис】: Если у вас возникнут какие-либо вопросы, пожалуйста, свяжитесь с нами, и мы ответим вам в течение 24 часов."}]
                },
                {
                    "id": 22336,  # 关键字
                    "complex_id": 0,
                    "values": [{"value": self._process_keywords(product)}]
                },
                {
                    "id": 23171,  # OZON主题标签
                    "complex_id": 0,
                    "values": [{"value": self._process_theme_tags(product)}]
                },
                {
                    "id": 8513,  # 每包数量
                    "complex_id": 0,
                    "values": [{"value": "1"}]
                },
                {
                    "id": 11650,  # 原厂包装数量
                    "complex_id": 0,
                    "values": [{"value": "1"}]
                },
                {
                    "id": 4384,  # 配置
                    "complex_id": 0,
                    "values": [{"value": "1 жидкокристаллический дисплей，1 комплект инструментов для разборки"}]
                }
            ]

            # 添加JSON富内容 - 已验证属性ID 11254 正确工作
            try:
                rich_content_json = self.build_rich_content_json(product)
                if rich_content_json:
                    attributes.append({
                        "id": 11254,  # JSON富内容属性ID
                        "complex_id": 0,
                        "values": [{"value": rich_content_json}]
                    })
                    logger.debug(f"添加JSON富内容成功，长度: {len(rich_content_json)} 字符")
            except Exception as e:
                logger.warning(f"添加JSON富内容失败: {e}")

            return attributes

        except Exception as e:
            logger.error(f"构建产品属性失败: {e}")
            return []

    def _process_keywords(self, product: Dict) -> str:
        """智能处理产品关键词"""
        try:
            logger.info(f"🔍 开始处理产品 {product.get('id', 'Unknown')} 的关键词")

            # 优先使用keywords字段
            keywords = product.get('keywords', '').strip()
            logger.info(f"📋 产品keywords字段: '{keywords}'")

            if keywords:
                # 移除#号并清理格式
                processed_keywords = keywords.replace('#', '').strip()
                logger.info(f"✅ 使用keywords字段: {processed_keywords}")
                return processed_keywords

            # 回退到tags字段
            tags = product.get('tags', '').strip()
            logger.info(f"📋 产品tags字段: '{tags}'")

            if tags:
                processed_tags = tags.replace('#', '').strip()
                logger.info(f"✅ 回退到tags字段: {processed_tags}")
                return processed_tags

            # 基于产品信息生成默认关键词
            model_name = product.get('model_name', '').strip()
            logger.info(f"📋 产品model_name字段: '{model_name}'")

            if model_name:
                default_keywords = f"lcd display {model_name.lower()}"
                logger.info(f"✅ 生成默认关键词: {default_keywords}")
                return default_keywords

            # 最后的默认值
            logger.warning(f"❌ 产品ID {product.get('id', 'Unknown')} 无法获取关键词，使用默认值")
            return 'lcd display'

        except Exception as e:
            logger.error(f"❌ 处理关键词失败: {e}")
            return 'lcd display'

    def _process_theme_tags(self, product: Dict) -> str:
        """处理OZON主题标签（用于社交化推荐）"""
        try:
            logger.info(f"🏷️ 开始处理产品 {product.get('id', 'Unknown')} 的主题标签")

            # 获取tags字段
            tags = product.get('tags', '').strip()
            logger.info(f"📋 产品tags字段: '{tags}'")

            if tags:
                # 基本格式清理：将逗号分隔改为空格分隔
                cleaned_tags = tags.replace(',', ' ')
                # 清理多余空格
                import re
                cleaned_tags = re.sub(r'\s+', ' ', cleaned_tags).strip()
                logger.info(f"✅ 清理后的tags字段: {cleaned_tags}")
                return cleaned_tags

            # 如果没有tags，返回空字符串
            logger.info("⚠️ 没有tags字段，返回空字符串")
            return ""

        except Exception as e:
            logger.error(f"❌ 处理主题标签失败: {e}")
            return ""

    def build_single_product_data(self, product: Dict) -> Dict:
        """构建单个产品的上传数据"""
        try:
            # 记录产品基本信息用于调试
            product_id = product.get('id', 'Unknown')
            model_name = product.get('model_name', 'Unknown')
            logger.debug(f"🔧 开始构建产品数据: ID={product_id}, 型号={model_name}")

            # 构建属性数据
            attributes = self.build_product_attributes(product)

            # 构建图片数据
            images = []
            if product.get('main_image_url'):
                images.append(product['main_image_url'])

            # 构建视频复杂属性数据
            complex_attributes = []
            if product.get('video_url'):
                video_url = product['video_url'].strip()
                video_name = f"Video for {product.get('model_name', 'Product')}"

                video_complex = {
                    "attributes": [
                        {
                            "complex_id": 100001,
                            "id": 21841,  # 视频URL属性ID
                            "values": [{"value": video_url}]
                        },
                        {
                            "complex_id": 100001,
                            "id": 21837,  # 视频名称属性ID
                            "values": [{"value": video_name}]
                        }
                    ]
                }
                complex_attributes.append(video_complex)

            # 安全地处理数值字段，记录详细的调试信息
            def safe_float_convert(field_name: str, value, default_value: str) -> str:
                """安全地转换浮点数并记录调试信息"""
                try:
                    raw_value = product.get(field_name, default_value)
                    logger.info(f"🔍 字段 {field_name}: 原始值={raw_value}, 类型={type(raw_value)}")

                    if raw_value is None:
                        logger.warning(f"⚠️ 字段 {field_name} 为 None，使用默认值 {default_value}")
                        return str(int(float(default_value)))

                    if raw_value == '':
                        logger.warning(f"⚠️ 字段 {field_name} 为空字符串，使用默认值 {default_value}")
                        return str(int(float(default_value)))

                    # 尝试转换
                    float_value = float(raw_value)
                    result = str(int(float_value))
                    logger.info(f"✅ 字段 {field_name}: {raw_value} -> {result}")
                    return result

                except (ValueError, TypeError) as e:
                    logger.error(f"❌ 字段 {field_name} 转换失败: 值={raw_value}, 错误={e}, 使用默认值 {default_value}")
                    return str(int(float(default_value)))

            def safe_int_convert(field_name: str, value, default_value: int) -> int:
                """安全地转换整数并记录调试信息"""
                try:
                    raw_value = product.get(field_name, str(default_value))
                    logger.info(f"🔍 字段 {field_name}: 原始值={raw_value}, 类型={type(raw_value)}")

                    if raw_value is None:
                        logger.warning(f"⚠️ 字段 {field_name} 为 None，使用默认值 {default_value}")
                        return default_value

                    if raw_value == '':
                        logger.warning(f"⚠️ 字段 {field_name} 为空字符串，使用默认值 {default_value}")
                        return default_value

                    # 尝试转换
                    float_value = float(raw_value)
                    result = int(float_value) or default_value  # 如果转换结果为0，使用默认值
                    logger.info(f"✅ 字段 {field_name}: {raw_value} -> {result}")
                    return result

                except (ValueError, TypeError) as e:
                    logger.error(f"❌ 字段 {field_name} 转换失败: 值={raw_value}, 错误={e}, 使用默认值 {default_value}")
                    return default_value

            # 构建产品数据
            item_data = {
                "attributes": attributes,
                "description_category_id": DESCRIPTION_CATEGORY_ID,
                "type_id": TYPE_ID,
                "name": product.get('product_name', product.get('model_name', '')),
                "offer_id": product.get('item_code', f"item_{product['id']}"),
                "currency_code": "CNY",
                "price": safe_float_convert('price_cny', product.get('price_cny'), '100'),
                "old_price": safe_float_convert('price_before_discount', product.get('price_before_discount'), '120'),
                "premium_price": "",
                "vat": "0.0",
                "weight": safe_int_convert('gross_weight', product.get('gross_weight'), 100),
                "width": safe_int_convert('package_width', product.get('package_width'), 10),
                "height": safe_int_convert('package_height', product.get('package_height'), 10),
                "depth": safe_int_convert('package_length', product.get('package_length'), 10),
                "dimension_unit": "mm",
                "weight_unit": "g",
                "images": images,
                "primary_image": product.get('main_image_url', ''),
                "complex_attributes": complex_attributes
            }

            # 只有当SKU存在且不为空时才添加barcode
            if product.get('sku') and str(product.get('sku')).strip():
                item_data["barcode"] = str(product.get('sku')).strip()

            logger.info(f"✅ 产品数据构建成功: ID={product_id}, 型号={model_name}")
            return item_data

        except Exception as e:
            logger.error(f"构建产品数据失败: 产品ID={product.get('id', 'Unknown')}, 错误={e}")
            logger.error(f"产品数据详情: {product}")
            return {}
    
    def upload_products_to_ozon(self, products: List[Dict]) -> Optional[str]:
        """批量上传产品到OZON"""
        try:
            if not self.client_id or not self.api_key:
                raise ValueError("OZON配置不完整，请检查Client ID和API Key")

            url = f"{OZON_BASE_URL}/v3/product/import"
            headers = {
                "Client-Id": self.client_id,
                "Api-Key": self.api_key,
                "Content-Type": "application/json"
            }

            logger.info(f"🚀 开始处理 {len(products)} 个产品的上传数据")

            # 构建所有产品的数据
            items = []
            for i, product in enumerate(products, 1):
                logger.info(f"📦 处理第 {i}/{len(products)} 个产品: ID={product.get('id', 'Unknown')}")
                item_data = self.build_single_product_data(product)
                if item_data:
                    items.append(item_data)
                    logger.info(f"✅ 产品 {product.get('id', 'Unknown')} 数据构建成功")
                else:
                    logger.error(f"❌ 产品 {product.get('id', 'Unknown')} 数据构建失败")

            if not items:
                logger.error("没有有效的产品数据可上传")
                return None
            
            payload = {"items": items}
            
            logger.info(f"开始批量上传 {len(items)} 个产品到OZON")
            
            response = requests.post(url, headers=headers, json=payload, timeout=60)
            
            logger.info(f"OZON上传响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                task_id = result.get('result', {}).get('task_id')
                logger.info(f"批量上传成功，任务ID: {task_id}")
                return task_id
            else:
                logger.error(f"批量上传失败: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"批量上传异常: {e}")
            return None

    def check_import_status(self, task_id: str) -> Optional[Dict]:
        """检查商品导入任务状态"""
        try:
            if not self.client_id or not self.api_key:
                raise ValueError("OZON配置不完整")

            url = f"{OZON_BASE_URL}/v1/product/import/info"
            headers = {
                "Client-Id": self.client_id,
                "Api-Key": self.api_key,
                "Content-Type": "application/json"
            }

            payload = {"task_id": task_id}

            logger.info(f"查询OZON任务状态: {task_id}")

            response = requests.post(url, headers=headers, json=payload, timeout=30)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"任务状态查询成功: {task_id}")
                return result
            else:
                logger.error(f"任务状态查询失败: {response.text}")
                return None

        except Exception as e:
            logger.error(f"状态查询异常: {e}")
            return None

    def create_upload_task(self, product_ids: List[int], upload_config: Dict = None) -> str:
        """创建上传任务记录"""
        try:
            task_id = str(uuid.uuid4())

            query = """
            INSERT INTO ozon_upload_tasks
            (task_id, product_ids, batch_size, status, upload_config)
            VALUES (%s, %s, %s, %s, %s)
            """

            config_json = json.dumps(upload_config or {})
            product_ids_json = json.dumps(product_ids)

            result = db_manager.execute_query(
                query,
                (task_id, product_ids_json, len(product_ids), 'pending', config_json),
                fetch=False
            )

            if result:
                logger.info(f"创建上传任务成功: {task_id}")
                return task_id
            else:
                logger.error("创建上传任务失败")
                return ""

        except Exception as e:
            logger.error(f"创建上传任务异常: {e}")
            return ""

    def update_upload_task_status(self, task_id: str, status: str,
                                ozon_task_id: str = None, success_count: int = 0,
                                failed_count: int = 0, error_details: Dict = None) -> bool:
        """更新上传任务状态"""
        try:
            updates = ["status = %s", "updated_at = CURRENT_TIMESTAMP"]
            params = [status]

            if ozon_task_id:
                updates.append("ozon_task_id = %s")
                params.append(ozon_task_id)

            if success_count > 0:
                updates.append("success_count = %s")
                params.append(success_count)

            if failed_count > 0:
                updates.append("failed_count = %s")
                params.append(failed_count)

            if error_details:
                updates.append("error_details = %s")
                params.append(json.dumps(error_details))

            if status in ['success', 'failed', 'partial']:
                updates.append("completed_at = CURRENT_TIMESTAMP")

            params.append(task_id)

            query = f"UPDATE ozon_upload_tasks SET {', '.join(updates)} WHERE task_id = %s"

            result = db_manager.execute_query(query, tuple(params), fetch=False)
            return result is not None and result > 0

        except Exception as e:
            logger.error(f"更新上传任务状态失败: {e}")
            return False

    def update_product_upload_status(self, product_ids: List[int], status: str,
                                   ozon_product_id: str = None) -> int:
        """更新产品的上传状态"""
        try:
            success_count = 0

            for product_id in product_ids:
                updates = ["ozon_upload_status = %s", "last_ozon_upload = CURRENT_TIMESTAMP"]
                params = [status]

                if ozon_product_id:
                    updates.append("ozon_product_id = %s")
                    params.append(ozon_product_id)

                params.append(product_id)

                query = f"UPDATE product_info SET {', '.join(updates)} WHERE id = %s"

                result = db_manager.execute_query(query, tuple(params), fetch=False)
                if result and result > 0:
                    success_count += 1

            return success_count

        except Exception as e:
            logger.error(f"更新产品上传状态失败: {e}")
            return 0

    def get_upload_tasks(self, status: str = None, limit: int = 5) -> List[Dict]:
        """获取上传任务列表"""
        try:
            if status:
                query = """
                SELECT * FROM ozon_upload_tasks
                WHERE status = %s
                ORDER BY created_at DESC
                LIMIT %s
                """
                params = (status, limit)
            else:
                query = """
                SELECT * FROM ozon_upload_tasks
                ORDER BY created_at DESC
                LIMIT %s
                """
                params = (limit,)

            results = db_manager.execute_query(query, params)
            return results or []

        except Exception as e:
            logger.error(f"获取上传任务列表失败: {e}")
            return []

    def test_connection(self) -> bool:
        """测试OZON API连接 - 基于成功示例的实现"""
        try:
            if not self.client_id or not self.api_key:
                logger.warning("OZON Client ID或API Key未配置")
                return False

            # 使用正确的OZON API端点进行连接测试
            url = f"{OZON_BASE_URL}/v3/product/list"
            headers = {
                "Client-Id": self.client_id,
                "Api-Key": self.api_key,
                "Content-Type": "application/json"
            }

            # 使用正确的请求体格式
            payload = {
                "filter": {
                    "visibility": "ALL"
                },
                "last_id": "",
                "limit": 1
            }

            # 直接连接OZON API
            response = requests.post(url, headers=headers, json=payload, timeout=15)

            logger.info(f"OZON连接测试响应: 状态码={response.status_code}")

            # 记录详细的响应信息用于调试
            if response.status_code == 200:
                try:
                    result_data = response.json()
                    logger.info("OZON API连接测试成功 - 成功获取产品列表")
                    products = result_data.get('result', {}).get('items', [])
                    logger.info(f"获取到 {len(products)} 个产品")
                    return True
                except Exception as parse_error:
                    logger.warning(f"解析OZON API响应失败: {parse_error}")
                    return True  # 状态码200表示成功，即使解析失败
            else:
                try:
                    error_data = response.json()
                    logger.warning(f"OZON连接测试失败详情: {error_data}")

                    # 检查是否是认证相关的错误
                    if 'error' in error_data:
                        error_code = error_data.get('error', {}).get('code', '')
                        error_message = error_data.get('error', {}).get('message', '')

                        # 如果是认证错误
                        if 'UNAUTHORIZED' in error_code or 'unauthorized' in error_message.lower():
                            logger.error("OZON API认证失败: 无效的Client-Id或Api-Key")
                            return False
                        elif 'FORBIDDEN' in error_code or 'forbidden' in error_message.lower():
                            logger.error("OZON API权限不足: 请检查API权限设置")
                            return False

                except Exception as parse_error:
                    logger.warning(f"解析OZON API响应失败: {parse_error}")

            # 只有200状态码表示成功
            is_success = response.status_code == 200

            if not is_success:
                logger.warning(f"OZON API连接测试失败: HTTP {response.status_code}")

            return is_success

        except requests.exceptions.Timeout:
            logger.error("OZON API连接超时")
            return False
        except requests.exceptions.ConnectionError:
            logger.error("OZON API连接失败，请检查网络连接")
            return False
        except Exception as e:
            logger.error(f"OZON连接测试失败: {e}")
            return False

# 全局OZON服务实例
ozon_service = OzonService()
