#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
上传超时管理API路由
"""

import logging
from flask import Blueprint, request, jsonify
from datetime import datetime, timedelta
from services.upload_timeout_detector import UploadTimeoutDetector
from models.database import get_db_manager

logger = logging.getLogger(__name__)

def safe_format_time(time_value):
    """安全的时间格式化函数"""
    if not time_value:
        return None

    if hasattr(time_value, 'isoformat'):
        return time_value.isoformat()
    else:
        return str(time_value)

# 创建蓝图
upload_timeout_bp = Blueprint('upload_timeout', __name__, url_prefix='/api/upload-timeout')

@upload_timeout_bp.route('/status', methods=['GET'])
def get_timeout_status():
    """
    获取超时商品状态
    
    Query Parameters:
        limit: 每页数量，默认20
        offset: 偏移量，默认0
        status: 状态筛选 (timeout/retrying/failed)
    """
    try:
        # 获取查询参数
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        status_filter = request.args.get('status', '')
        
        detector = UploadTimeoutDetector()
        db = get_db_manager()
        
        # 构建查询条件
        where_conditions = ["upload_timeout_count > 0"]
        params = []
        
        if status_filter:
            where_conditions.append("ozon_upload_status = %s")
            params.append(status_filter)
        
        where_clause = " AND ".join(where_conditions)
        
        # 查询超时商品
        query = f"""
            SELECT 
                p.id, p.model_name, p.product_name, p.ozon_upload_status,
                p.upload_started_at, p.upload_timeout_count, p.max_retry_count,
                p.last_timeout_at, p.updated_at,
                CASE 
                    WHEN p.upload_started_at IS NOT NULL THEN 
                        TIMESTAMPDIFF(MINUTE, p.upload_started_at, NOW())
                    ELSE NULL 
                END as upload_duration_minutes,
                ul.next_retry_at
            FROM product_info p
            LEFT JOIN (
                SELECT product_id, next_retry_at,
                       ROW_NUMBER() OVER (PARTITION BY product_id ORDER BY created_at DESC) as rn
                FROM upload_logs 
                WHERE upload_status = 'retry'
            ) ul ON p.id = ul.product_id AND ul.rn = 1
            WHERE {where_clause}
            ORDER BY p.last_timeout_at DESC, p.updated_at DESC
            LIMIT %s OFFSET %s
        """
        
        params.extend([limit, offset])
        timeout_products = db.execute_query(query, params)
        
        # 获取统计信息
        stats = detector.get_timeout_statistics()
        
        # 格式化响应数据
        products = []
        for product in timeout_products or []:
            # 计算下次重试时间
            next_retry_at = safe_format_time(product['next_retry_at'])

            if not next_retry_at and product['ozon_upload_status'] == 'timeout':
                # 计算预期的下次重试时间
                retry_intervals = [5, 15, 30]
                timeout_count = product['upload_timeout_count']
                if timeout_count <= len(retry_intervals) and product['last_timeout_at']:
                    delay_minutes = retry_intervals[timeout_count - 1]
                    try:
                        if hasattr(product['last_timeout_at'], 'isoformat'):
                            next_retry_time = product['last_timeout_at'] + timedelta(minutes=delay_minutes)
                        else:
                            # 如果是字符串，尝试解析
                            from datetime import datetime
                            last_timeout = datetime.fromisoformat(str(product['last_timeout_at']).replace('Z', '+00:00'))
                            next_retry_time = last_timeout + timedelta(minutes=delay_minutes)
                        next_retry_at = next_retry_time.isoformat()
                    except Exception as e:
                        logger.warning(f"计算重试时间失败: {e}")
                        next_retry_at = None
            
            products.append({
                'id': product['id'],
                'model_name': product['model_name'],
                'product_name': product['product_name'],
                'status': product['ozon_upload_status'],
                'upload_started_at': safe_format_time(product['upload_started_at']),
                'upload_duration_minutes': product['upload_duration_minutes'],
                'timeout_count': product['upload_timeout_count'],
                'max_retry_count': product['max_retry_count'],
                'last_timeout_at': safe_format_time(product['last_timeout_at']),
                'next_retry_at': next_retry_at,
                'updated_at': safe_format_time(product['updated_at'])
            })
        
        # 检查是否还有更多数据
        has_more = len(timeout_products or []) == limit
        
        return jsonify({
            'success': True,
            'data': {
                'timeout_products': products,
                'statistics': stats,
                'pagination': {
                    'limit': limit,
                    'offset': offset,
                    'has_more': has_more,
                    'next_offset': offset + limit if has_more else None
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取超时状态失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取超时状态失败: {str(e)}'
        }), 500

@upload_timeout_bp.route('/products/<int:product_id>/retry', methods=['POST'])
def retry_product_upload(product_id):
    """
    手动重试商品上传
    
    Args:
        product_id: 商品ID
        
    Body:
        force_retry: 是否强制重试（忽略重试次数限制）
        reset_timeout_count: 是否重置超时次数
    """
    try:
        data = request.get_json() or {}
        force_retry = data.get('force_retry', False)
        reset_timeout_count = data.get('reset_timeout_count', False)
        
        detector = UploadTimeoutDetector()
        db = get_db_manager()
        
        # 获取商品信息
        product = db.execute_query(
            "SELECT * FROM product_info WHERE id = %s", 
            (product_id,)
        )
        
        if not product:
            return jsonify({
                'success': False,
                'error': '商品不存在'
            }), 404
        
        product_data = product[0]
        current_timeout_count = product_data.get('upload_timeout_count', 0)
        max_retry_count = product_data.get('max_retry_count', 3)
        
        # 检查是否可以重试
        if not force_retry and current_timeout_count >= max_retry_count:
            return jsonify({
                'success': False,
                'error': f'商品已达到最大重试次数 {max_retry_count}，请使用强制重试'
            }), 400
        
        # 重置超时次数（如果需要）
        new_timeout_count = 0 if reset_timeout_count else current_timeout_count
        
        # 重置商品状态
        db.execute_query("""
            UPDATE product_info 
            SET ozon_upload_status = 'pending',
                upload_started_at = NULL,
                upload_timeout_count = %s,
                updated_at = NOW()
            WHERE id = %s
        """, (new_timeout_count, product_id))
        
        # 记录重试日志
        db.execute_query("""
            INSERT INTO upload_logs (
                product_id, upload_status, error_message, retry_count, created_at
            ) VALUES (%s, 'retry', '手动重试', %s, NOW())
        """, (product_id, new_timeout_count))
        
        logger.info(f"商品 {product_id} 手动重试成功，重置超时次数: {reset_timeout_count}")
        
        return jsonify({
            'success': True,
            'data': {
                'product_id': product_id,
                'new_status': 'pending',
                'timeout_count': new_timeout_count,
                'message': '重试设置成功'
            }
        })
        
    except Exception as e:
        logger.error(f"重试商品上传失败: {e}")
        return jsonify({
            'success': False,
            'error': f'重试失败: {str(e)}'
        }), 500

@upload_timeout_bp.route('/batch-action', methods=['POST'])
def batch_timeout_action():
    """
    批量处理超时商品
    
    Body:
        action: 操作类型 (retry/mark_failed/reset)
        product_ids: 商品ID列表
        force: 是否强制执行
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400
        
        action = data.get('action')
        product_ids = data.get('product_ids', [])
        force = data.get('force', False)
        
        if not action or not product_ids:
            return jsonify({
                'success': False,
                'error': '缺少必要参数: action 和 product_ids'
            }), 400
        
        if action not in ['retry', 'mark_failed', 'reset']:
            return jsonify({
                'success': False,
                'error': '无效的操作类型，支持: retry, mark_failed, reset'
            }), 400
        
        db = get_db_manager()
        success_count = 0
        failed_count = 0
        results = []
        
        for product_id in product_ids:
            try:
                if action == 'retry':
                    # 重试
                    db.execute_query("""
                        UPDATE product_info 
                        SET ozon_upload_status = 'pending',
                            upload_started_at = NULL,
                            updated_at = NOW()
                        WHERE id = %s
                    """, (product_id,))
                    
                elif action == 'mark_failed':
                    # 标记为失败
                    db.execute_query("""
                        UPDATE product_info 
                        SET ozon_upload_status = 'failed',
                            upload_started_at = NULL,
                            updated_at = NOW()
                        WHERE id = %s
                    """, (product_id,))
                    
                elif action == 'reset':
                    # 重置超时计数
                    db.execute_query("""
                        UPDATE product_info 
                        SET ozon_upload_status = 'pending',
                            upload_started_at = NULL,
                            upload_timeout_count = 0,
                            last_timeout_at = NULL,
                            updated_at = NOW()
                        WHERE id = %s
                    """, (product_id,))
                
                # 记录操作日志
                db.execute_query("""
                    INSERT INTO upload_logs (
                        product_id, upload_status, error_message, created_at
                    ) VALUES (%s, %s, %s, NOW())
                """, (product_id, action, f'批量{action}操作'))
                
                success_count += 1
                results.append({
                    'product_id': product_id,
                    'success': True,
                    'message': f'{action}操作成功'
                })
                
            except Exception as e:
                failed_count += 1
                results.append({
                    'product_id': product_id,
                    'success': False,
                    'error': str(e)
                })
                logger.error(f"批量操作商品 {product_id} 失败: {e}")
        
        logger.info(f"批量{action}操作完成: 成功={success_count}, 失败={failed_count}")
        
        return jsonify({
            'success': True,
            'data': {
                'action': action,
                'total_count': len(product_ids),
                'success_count': success_count,
                'failed_count': failed_count,
                'results': results
            }
        })
        
    except Exception as e:
        logger.error(f"批量操作失败: {e}")
        return jsonify({
            'success': False,
            'error': f'批量操作失败: {str(e)}'
        }), 500

@upload_timeout_bp.route('/statistics', methods=['GET'])
def get_timeout_statistics():
    """获取超时统计信息"""
    try:
        detector = UploadTimeoutDetector()
        stats = detector.get_timeout_statistics()
        
        return jsonify({
            'success': True,
            'data': stats
        })
        
    except Exception as e:
        logger.error(f"获取统计信息失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取统计信息失败: {str(e)}'
        }), 500

@upload_timeout_bp.route('/check-now', methods=['POST'])
def check_timeouts_now():
    """立即执行一次超时检测"""
    try:
        from tasks.upload_timeout_tasks import check_upload_timeouts
        
        # 执行超时检测
        check_upload_timeouts()
        
        return jsonify({
            'success': True,
            'data': {
                'message': '超时检测执行完成',
                'executed_at': datetime.now().isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"执行超时检测失败: {e}")
        return jsonify({
            'success': False,
            'error': f'执行超时检测失败: {str(e)}'
        }), 500
