#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
国际化支持模块
提供中文和俄语双语支持
"""

import json
import os
from flask import session, request

# 支持的语言
SUPPORTED_LANGUAGES = {
    'zh': '中文',
    'ru': 'Русский'
}

# 默认语言
DEFAULT_LANGUAGE = 'ru'

class I18n:
    """国际化管理类"""
    
    def __init__(self):
        self.translations = {}
        self.load_translations()
    
    def load_translations(self):
        """加载翻译文件"""
        current_dir = os.path.dirname(__file__)
        translations_dir = os.path.join(current_dir, '..', 'translations')
        
        for lang_code in SUPPORTED_LANGUAGES.keys():
            translation_file = os.path.join(translations_dir, f'{lang_code}.json')
            if os.path.exists(translation_file):
                try:
                    with open(translation_file, 'r', encoding='utf-8') as f:
                        self.translations[lang_code] = json.load(f)
                except Exception as e:
                    print(f"加载翻译文件失败 {lang_code}: {e}")
                    self.translations[lang_code] = {}
            else:
                self.translations[lang_code] = {}
    
    def get_current_language(self):
        """获取当前语言，强制默认使用俄语"""
        # 强制默认使用俄语，除非明确设置了其他语言
        if 'language' in session and session['language'] in SUPPORTED_LANGUAGES:
            return session['language']

        # 如果没有设置或设置无效，强制使用俄语
        return DEFAULT_LANGUAGE
    
    def set_language(self, lang_code):
        """设置当前语言"""
        if lang_code in SUPPORTED_LANGUAGES:
            session['language'] = lang_code
            return True
        return False
    
    def translate(self, key, lang=None, **kwargs):
        """翻译文本"""
        if lang is None:
            lang = self.get_current_language()
        
        # 获取翻译
        translation = self.translations.get(lang, {}).get(key, key)
        
        # 如果当前语言没有翻译，尝试使用默认语言
        if translation == key and lang != DEFAULT_LANGUAGE:
            translation = self.translations.get(DEFAULT_LANGUAGE, {}).get(key, key)
        
        # 格式化参数
        if kwargs:
            try:
                translation = translation.format(**kwargs)
            except:
                pass
        
        return translation
    
    def get_language_name(self, lang_code):
        """获取语言名称"""
        return SUPPORTED_LANGUAGES.get(lang_code, lang_code)

# 全局实例
i18n = I18n()

def t(key, **kwargs):
    """翻译函数的简写"""
    return i18n.translate(key, **kwargs)

def get_current_language():
    """获取当前语言的简写函数"""
    return i18n.get_current_language()

def set_language(lang_code):
    """设置语言的简写函数"""
    return i18n.set_language(lang_code)
