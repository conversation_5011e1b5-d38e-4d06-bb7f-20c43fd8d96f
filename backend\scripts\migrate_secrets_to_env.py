#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
密钥迁移脚本：从数据库提取敏感配置到环境变量
使用方法：python migrate_secrets_to_env.py
"""

import os
import sys
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models.database import get_db_manager
from services.config_service import ConfigEncryption, is_sensitive_config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def extract_secrets_from_database():
    """从数据库提取敏感配置"""
    try:
        db_manager = get_db_manager()
        if not db_manager:
            logger.error("无法连接到数据库")
            return {}
            
        # 查询所有敏感配置
        query = """
        SELECT config_key, config_value, config_type 
        FROM system_config 
        WHERE is_sensitive = TRUE OR config_type = 'encrypted'
        """
        
        results = db_manager.execute_query(query)
        secrets = {}
        
        for row in results:
            key = row['config_key']
            value = row['config_value']
            config_type = row['config_type']
            
            try:
                # 如果是加密配置，先解密
                if config_type == 'encrypted':
                    decrypted_value = ConfigEncryption.decrypt_config(value)
                    secrets[key] = decrypted_value
                    logger.info(f"✅ 提取加密配置: {key}")
                else:
                    secrets[key] = value
                    logger.info(f"✅ 提取配置: {key}")
                    
            except Exception as e:
                logger.error(f"❌ 解密配置失败 {key}: {e}")
                
        return secrets
        
    except Exception as e:
        logger.error(f"从数据库提取配置失败: {e}")
        return {}

def update_env_file(secrets):
    """更新.env文件"""
    try:
        env_file_path = project_root / '.env'
        
        # 映射配置键到环境变量名
        key_mapping = {
            'github_token': 'GITHUB_TOKEN',
            'ozon_api_key': 'OZON_API_KEY',
            'config_encryption_key': 'CONFIG_ENCRYPTION_KEY'
        }
        
        # 读取现有.env文件内容
        env_content = []
        if env_file_path.exists():
            with open(env_file_path, 'r', encoding='utf-8') as f:
                env_content = f.readlines()
        
        # 更新环境变量
        updated_vars = set()
        for i, line in enumerate(env_content):
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                var_name = line.split('=')[0]
                
                # 检查是否需要更新
                for config_key, env_var in key_mapping.items():
                    if var_name == env_var and config_key in secrets:
                        env_content[i] = f"{env_var}={secrets[config_key]}\n"
                        updated_vars.add(env_var)
                        logger.info(f"🔄 更新环境变量: {env_var}")
                        break
        
        # 添加新的环境变量
        for config_key, env_var in key_mapping.items():
            if config_key in secrets and env_var not in updated_vars:
                env_content.append(f"{env_var}={secrets[config_key]}\n")
                logger.info(f"➕ 添加环境变量: {env_var}")
        
        # 写回.env文件
        with open(env_file_path, 'w', encoding='utf-8') as f:
            f.writelines(env_content)
            
        logger.info(f"✅ .env文件更新完成: {env_file_path}")
        return True
        
    except Exception as e:
        logger.error(f"更新.env文件失败: {e}")
        return False

def backup_database_configs():
    """备份数据库中的敏感配置"""
    try:
        backup_file = project_root / 'config_backup.txt'
        
        db_manager = get_db_manager()
        if not db_manager:
            return False
            
        query = """
        SELECT config_key, config_value, config_type, description, created_at
        FROM system_config 
        WHERE is_sensitive = TRUE OR config_type = 'encrypted'
        """
        
        results = db_manager.execute_query(query)
        
        with open(backup_file, 'w', encoding='utf-8') as f:
            f.write("# 敏感配置备份文件\n")
            f.write(f"# 备份时间: {datetime.now()}\n\n")
            
            for row in results:
                f.write(f"配置键: {row['config_key']}\n")
                f.write(f"配置类型: {row['config_type']}\n")
                f.write(f"描述: {row['description']}\n")
                f.write(f"创建时间: {row['created_at']}\n")
                f.write(f"配置值: {row['config_value'][:20]}...\n")
                f.write("-" * 50 + "\n")
        
        logger.info(f"✅ 配置备份完成: {backup_file}")
        return True
        
    except Exception as e:
        logger.error(f"备份配置失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始密钥迁移过程...")
    
    # 1. 备份现有配置
    logger.info("📦 备份数据库配置...")
    if not backup_database_configs():
        logger.error("❌ 备份失败，停止迁移")
        return False
    
    # 2. 从数据库提取密钥
    logger.info("🔍 从数据库提取敏感配置...")
    secrets = extract_secrets_from_database()
    
    if not secrets:
        logger.warning("⚠️ 未找到需要迁移的敏感配置")
        return True
    
    logger.info(f"📋 找到 {len(secrets)} 个敏感配置需要迁移")
    
    # 3. 更新.env文件
    logger.info("📝 更新.env文件...")
    if not update_env_file(secrets):
        logger.error("❌ 更新.env文件失败")
        return False
    
    logger.info("✅ 密钥迁移完成！")
    logger.info("📌 请检查.env文件中的配置是否正确")
    logger.info("📌 确认无误后，可以运行清理脚本删除数据库中的敏感配置")
    
    return True

if __name__ == '__main__':
    from datetime import datetime
    success = main()
    sys.exit(0 if success else 1)
