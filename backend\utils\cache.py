"""
缓存工具模块
提供简单的内存缓存和装饰器功能
"""

import time
import functools
import threading
from typing import Any, Dict, Optional, Callable
import logging

logger = logging.getLogger(__name__)

class SimpleCache:
    """简单的内存缓存实现"""
    
    def __init__(self, default_ttl: int = 300):
        """
        初始化缓存
        
        Args:
            default_ttl: 默认过期时间（秒）
        """
        self.cache: Dict[str, Dict[str, Any]] = {}
        self.default_ttl = default_ttl
        self._lock = threading.RLock()
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self.cache:
                return None
            
            item = self.cache[key]
            
            # 检查是否过期
            if time.time() > item['expires_at']:
                del self.cache[key]
                logger.debug(f"缓存过期并删除: {key}")
                return None
            
            logger.debug(f"缓存命中: {key}")
            return item['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存值"""
        if ttl is None:
            ttl = self.default_ttl
        
        expires_at = time.time() + ttl
        
        with self._lock:
            self.cache[key] = {
                'value': value,
                'expires_at': expires_at,
                'created_at': time.time()
            }
        
        logger.debug(f"缓存设置: {key}, TTL: {ttl}秒")
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            if key in self.cache:
                del self.cache[key]
                logger.debug(f"缓存删除: {key}")
                return True
            return False
    
    def clear(self) -> None:
        """清空所有缓存"""
        with self._lock:
            count = len(self.cache)
            self.cache.clear()
            logger.info(f"清空缓存: {count} 个条目")

    def exists(self, key: str) -> bool:
        """检查缓存键是否存在且未过期"""
        with self._lock:
            if key not in self.cache:
                return False

            item = self.cache[key]

            # 检查是否过期
            if time.time() > item['expires_at']:
                del self.cache[key]
                logger.debug(f"缓存过期并删除: {key}")
                return False

            return True
    
    def cleanup_expired(self) -> int:
        """清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        with self._lock:
            for key, item in self.cache.items():
                if current_time > item['expires_at']:
                    expired_keys.append(key)
            
            for key in expired_keys:
                del self.cache[key]
        
        if expired_keys:
            logger.info(f"清理过期缓存: {len(expired_keys)} 个条目")
        
        return len(expired_keys)
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        with self._lock:
            current_time = time.time()
            total_count = len(self.cache)
            expired_count = sum(1 for item in self.cache.values() 
                              if current_time > item['expires_at'])
            
            return {
                'total_count': total_count,
                'active_count': total_count - expired_count,
                'expired_count': expired_count,
                'memory_usage': len(str(self.cache))  # 简单的内存使用估算
            }

# 全局缓存实例
_global_cache = SimpleCache()

def get_cache() -> SimpleCache:
    """获取全局缓存实例"""
    return _global_cache

def cache_result(ttl: int = 300, key_prefix: str = ""):
    """
    缓存函数结果的装饰器
    
    Args:
        ttl: 缓存过期时间（秒）
        key_prefix: 缓存键前缀
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 生成缓存键
            cache_key = f"{key_prefix}{func.__name__}:{hash(str(args) + str(sorted(kwargs.items())))}"
            
            # 尝试从缓存获取
            cached_result = _global_cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # 执行函数并缓存结果
            try:
                result = func(*args, **kwargs)
                _global_cache.set(cache_key, result, ttl)
                return result
            except Exception as e:
                logger.error(f"函数执行失败: {func.__name__}, 错误: {e}")
                raise
        
        # 添加清除缓存的方法
        wrapper.clear_cache = lambda: _global_cache.delete(f"{key_prefix}{func.__name__}")
        
        return wrapper
    return decorator

def invalidate_cache_pattern(pattern: str) -> int:
    """
    根据模式删除缓存
    
    Args:
        pattern: 缓存键模式（简单的字符串匹配）
    
    Returns:
        删除的缓存数量
    """
    cache = get_cache()
    deleted_count = 0
    
    with cache._lock:
        keys_to_delete = [key for key in cache.cache.keys() if pattern in key]
        for key in keys_to_delete:
            cache.delete(key)
            deleted_count += 1
    
    logger.info(f"根据模式 '{pattern}' 删除缓存: {deleted_count} 个条目")
    return deleted_count

# 定期清理过期缓存的后台任务
def start_cache_cleanup_task(interval: int = 300):
    """
    启动缓存清理后台任务
    
    Args:
        interval: 清理间隔（秒）
    """
    def cleanup_task():
        while True:
            try:
                time.sleep(interval)
                _global_cache.cleanup_expired()
            except Exception as e:
                logger.error(f"缓存清理任务异常: {e}")
    
    cleanup_thread = threading.Thread(target=cleanup_task, daemon=True)
    cleanup_thread.start()
    logger.info(f"缓存清理任务已启动，间隔: {interval}秒")

# 缓存键生成器
class CacheKeyGenerator:
    """缓存键生成器"""
    
    @staticmethod
    def product_list(limit: int = 100, offset: int = 0, filters: Dict = None) -> str:
        """生成产品列表缓存键"""
        filter_str = str(sorted(filters.items())) if filters else ""
        return f"product_list:{limit}:{offset}:{hash(filter_str)}"
    
    @staticmethod
    def product_detail(product_id: int) -> str:
        """生成产品详情缓存键"""
        return f"product_detail:{product_id}"
    
    @staticmethod
    def system_status() -> str:
        """生成系统状态缓存键"""
        return "system_status"
    
    @staticmethod
    def upload_task_status(task_id: str) -> str:
        """生成上传任务状态缓存键"""
        return f"upload_task_status:{task_id}"

# 预定义的缓存配置
CACHE_CONFIG = {
    'product_list': {'ttl': 300, 'key_prefix': 'products:'},
    'system_status': {'ttl': 60, 'key_prefix': 'system:'},
    'upload_tasks': {'ttl': 30, 'key_prefix': 'tasks:'},
    'image_scan': {'ttl': 600, 'key_prefix': 'images:'},
}

def get_cache_config(cache_type: str) -> Dict[str, Any]:
    """获取缓存配置"""
    return CACHE_CONFIG.get(cache_type, {'ttl': 300, 'key_prefix': ''})

# 全局缓存实例
cache = SimpleCache()

def init_cache_system(cache_type: str = 'memory', **kwargs):
    """
    初始化缓存系统

    Args:
        cache_type: 缓存类型 ('memory' 或 'redis')
        **kwargs: 缓存配置参数

    Returns:
        缓存实例
    """
    global cache

    if cache_type.lower() == 'redis':
        try:
            # 尝试初始化Redis缓存
            logger.info("尝试初始化Redis缓存...")
            # 这里可以扩展Redis缓存实现
            logger.warning("Redis缓存暂未实现，使用内存缓存")
            cache = SimpleCache(**kwargs)
        except Exception as e:
            logger.warning(f"Redis缓存初始化失败，使用内存缓存: {e}")
            cache = SimpleCache(**kwargs)
    else:
        # 使用内存缓存
        cache = SimpleCache(**kwargs)
        logger.info("内存缓存初始化成功")

    return cache

# 导出常用函数
get = cache.get
set = cache.set
delete = cache.delete
clear = cache.clear
exists = cache.exists
