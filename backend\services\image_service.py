#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片处理和匹配服务
基于tempfile/upload_and_map_images.py，提供图片扫描、匹配和处理功能
"""

import os
import re
import time
import datetime
import logging
from typing import List, Dict, Optional, Tuple
from difflib import SequenceMatcher
from models.database import get_db_manager
from services.config_service import config_service
from utils.error_handler import (
    FileOperationError, DatabaseError, ValidationError,
    log_function_call, safe_execute, ErrorCollector
)

logger = logging.getLogger(__name__)

# 支持的图片格式
IMAGE_EXTS = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}

class ImageService:
    """图片处理和匹配服务类"""
    
    def __init__(self):
        self.local_folder_path = config_service.get_config('local_folder_path', 'psoutput')
    
    def reload_config(self):
        """重新加载配置"""
        self.local_folder_path = config_service.get_config('local_folder_path', 'psoutput')
    
    @log_function_call
    def scan_local_images(self, folder_path: str = None) -> List[Dict]:
        """
        扫描本地图片文件

        Args:
            folder_path: 文件夹路径，如果不指定则使用配置中的路径

        Returns:
            图片文件信息列表

        Raises:
            FileOperationError: 文件夹操作失败时
        """
        if not folder_path:
            folder_path = self.local_folder_path

        if not folder_path or not folder_path.strip():
            raise ValidationError("文件夹路径不能为空")

        folder_path = folder_path.strip()

        if not os.path.exists(folder_path):
            logger.warning(f"文件夹不存在: {folder_path}")
            raise FileOperationError(f"文件夹不存在: {folder_path}", file_path=folder_path)

        image_files = []
        error_collector = ErrorCollector()

        try:
            for root, _, files in os.walk(folder_path):
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        file_ext = os.path.splitext(file)[1].lower()

                        # 为了测试，暂时允许所有文件类型，但标记是否为真实图片
                        is_real_image = file_ext in IMAGE_EXTS

                        # 获取文件信息
                        file_stat = os.stat(file_path)
                        file_info = {
                            'filename': file,
                            'filepath': file_path,
                            'relative_path': os.path.relpath(file_path, folder_path),
                            'size': file_stat.st_size,
                            'modified_time': datetime.datetime.fromtimestamp(file_stat.st_mtime),
                            'modified_time_str': datetime.datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S'),
                            'extension': file_ext,
                            'is_real_image': is_real_image,
                            'extracted_model': safe_execute(
                                self.extract_model_name_from_filename,
                                file,
                                default=''
                            )
                        }
                        image_files.append(file_info)

                    except Exception as e:
                        error_collector.add_warning(f"处理文件失败: {file}", {'error': str(e)})
                        continue

            logger.info(f"扫描到 {len(image_files)} 个图片文件")

            if error_collector.has_warnings():
                logger.warning(f"扫描过程中有 {error_collector.get_summary()['warning_count']} 个警告")

            return image_files

        except Exception as e:
            logger.error(f"扫描本地图片失败: {e}")
            raise FileOperationError(f"扫描文件夹失败: {str(e)}", file_path=folder_path)
    
    def extract_model_name_from_filename(self, filename: str) -> str:
        """从文件名提取设备型号（新版本：更准确的解析）"""
        try:
            # 去除文件扩展名
            base_name = os.path.splitext(filename)[0]

            # 移除"_数字"部分和"LCD"字样
            clean_name = re.sub(r'_\d+$', '', base_name).replace("LCD", "").strip()

            # 保持原始格式，只做基本清理
            device_name = clean_name

            # 标准化空格和下划线为统一格式
            device_name = re.sub(r'[_\s]+', ' ', device_name).strip()

            logger.info(f"🔧 文件名解析: '{filename}' -> '{device_name}'")

            return device_name

        except Exception as e:
            logger.error(f"提取型号名称失败 {filename}: {e}")
            return filename
    
    def find_matching_products(self, model_name: str) -> List[Dict]:
        """
        在product_info表中查找匹配的产品
        
        Args:
            model_name: 型号名称
        
        Returns:
            匹配的产品列表，包含匹配置信度
        """
        try:
            clean_model_name = model_name.strip()
            if not clean_model_name:
                return []

            logger.info(f"开始查找匹配产品，搜索型号: '{clean_model_name}' (长度: {len(clean_model_name)})")

            # 获取数据库管理器
            db_manager = get_db_manager()

            # 先查询数据库中包含搜索关键词的记录，用于调试
            debug_query = "SELECT id, model_name, item_code FROM product_info WHERE model_name LIKE %s OR item_code LIKE %s ORDER BY id DESC LIMIT 5"
            debug_pattern = f"%{clean_model_name}%"
            debug_results = db_manager.execute_query(debug_query, (debug_pattern, debug_pattern))
            logger.info(f"数据库中包含 '{clean_model_name}' 的产品记录:")
            for row in debug_results:
                db_model_name = row['model_name']
                logger.info(f"  ID: {row['id']}")
                logger.info(f"  model_name: '{db_model_name}' (长度: {len(db_model_name)})")
                logger.info(f"  item_code: '{row['item_code']}'")
                logger.info(f"  字符对比: 搜索='{clean_model_name}' vs 数据库='{db_model_name}'")
                logger.info(f"  是否相等: {clean_model_name == db_model_name}")
                logger.info(f"  ASCII码: 搜索={[ord(c) for c in clean_model_name]} vs 数据库={[ord(c) for c in db_model_name]}")
                logger.info("  ---")

            # 特别查询 Blackview A96
            if "Blackview A96" in clean_model_name:
                logger.info("🔍 开始特别调试 Blackview A96...")

                # 测试1: 直接查询ID=147
                special_query1 = "SELECT id, model_name FROM product_info WHERE id = 147"
                special_result1 = db_manager.execute_query(special_query1)
                if special_result1:
                    special_model = special_result1[0]['model_name']
                    logger.info(f"测试1 - ID=147: model_name='{special_model}' (长度: {len(special_model)})")
                    logger.info(f"测试1 - 字符对比: '{clean_model_name}' == '{special_model}' ? {clean_model_name == special_model}")
                else:
                    logger.error("测试1 - ID=147 查询失败")

                # 测试2: 精确匹配查询
                special_query2 = "SELECT id, model_name FROM product_info WHERE model_name = %s"
                special_result2 = db_manager.execute_query(special_query2, (clean_model_name,))
                logger.info(f"测试2 - 精确匹配查询结果: {len(special_result2) if special_result2 else 0} 条")

                # 测试3: TRIM匹配查询
                special_query3 = "SELECT id, model_name FROM product_info WHERE TRIM(model_name) = %s"
                special_result3 = db_manager.execute_query(special_query3, (clean_model_name,))
                logger.info(f"测试3 - TRIM匹配查询结果: {len(special_result3) if special_result3 else 0} 条")

                # 测试4: 模糊匹配查询
                special_query4 = "SELECT id, model_name FROM product_info WHERE model_name LIKE %s"
                special_pattern = f"%{clean_model_name}%"
                special_result4 = db_manager.execute_query(special_query4, (special_pattern,))
                logger.info(f"测试4 - 模糊匹配查询结果: {len(special_result4) if special_result4 else 0} 条")

            # 多种匹配策略
            matching_strategies = [
                # 精确匹配
                ("SELECT id, model_name, main_image_url, updated_at FROM product_info WHERE model_name = %s", (clean_model_name,), 'exact'),
                # 模糊匹配
                ("SELECT id, model_name, main_image_url, updated_at FROM product_info WHERE model_name LIKE %s", (f'%{clean_model_name}%',), 'fuzzy'),
                # item_code匹配
                ("SELECT id, model_name, main_image_url, updated_at FROM product_info WHERE item_code LIKE %s", (f'%{clean_model_name}%',), 'item_code'),
                # keywords匹配
                ("SELECT id, model_name, main_image_url, updated_at FROM product_info WHERE keywords LIKE %s", (f'%{clean_model_name}%',), 'keyword'),
            ]
            
            all_matches = []
            seen_ids = set()
            
            for query, params, match_method in matching_strategies:
                try:
                    logger.info(f"执行匹配查询 [{match_method}]: {query}")
                    logger.info(f"查询参数: {params}")

                    # 执行查询
                    results = db_manager.execute_query(query, params)

                    # 详细日志
                    if results is None:
                        logger.error(f"  [{match_method}] 查询返回 None")
                        continue
                    elif len(results) == 0:
                        logger.info(f"  [{match_method}] 查询返回空列表")
                        continue
                    else:
                        logger.info(f"  [{match_method}] 查询返回 {len(results)} 条结果")
                        for result_row in results:
                            logger.info(f"    找到匹配: ID={result_row['id']}, model_name='{result_row['model_name']}'")

                    for row in results:
                        product_id = row['id']
                        if product_id not in seen_ids:
                            # 计算匹配置信度
                            confidence = self.calculate_match_confidence(clean_model_name, row['model_name'], match_method)
                            
                            match_info = {
                                'id': product_id,
                                'model_name': row['model_name'],
                                'main_image_url': row['main_image_url'],
                                'updated_at': row['updated_at'],
                                'match_method': match_method,
                                'match_confidence': confidence
                            }
                            all_matches.append(match_info)
                            seen_ids.add(product_id)
                            
                except Exception as e:
                    logger.debug(f"匹配查询失败 {match_method}: {e}")
                    continue
            
            # 按置信度排序
            all_matches.sort(key=lambda x: x['match_confidence'], reverse=True)

            # 严格过滤，确保一对一精确匹配
            MIN_CONFIDENCE_THRESHOLD = 0.9  # 提高到0.9，确保精确匹配
            filtered_matches = [match for match in all_matches if match['match_confidence'] >= MIN_CONFIDENCE_THRESHOLD]

            logger.info(f"匹配结果: 总计{len(all_matches)}个, 过滤后{len(filtered_matches)}个 (置信度≥{MIN_CONFIDENCE_THRESHOLD})")

            return filtered_matches
            
        except Exception as e:
            logger.error(f"查找匹配产品失败 {model_name}: {e}")
            return []
    
    def calculate_match_confidence(self, search_name: str, product_name: str, match_method: str) -> float:
        """计算匹配置信度"""
        try:
            # 基础置信度根据匹配方法
            base_confidence = {
                'exact': 1.0,
                'fuzzy': 0.8,
                'item_code': 0.7,
                'keyword': 0.6
            }.get(match_method, 0.5)
            
            # 使用字符串相似度调整置信度
            similarity = SequenceMatcher(None, search_name.lower(), product_name.lower()).ratio()
            
            # 综合计算
            final_confidence = base_confidence * similarity
            
            return round(final_confidence, 2)
            
        except Exception as e:
            logger.error(f"计算匹配置信度失败: {e}")
            return 0.0
    
    def save_image_product_mapping(self, image_filename: str, extracted_model: str,
                                 product_id: int, keyword: str = None,
                                 match_confidence: float = 0.0, match_method: str = 'manual') -> bool:
        """保存图片产品映射关系"""
        try:
            # 获取数据库管理器
            db_manager = get_db_manager()

            query = """
            INSERT INTO image_product_mapping 
            (image_filename, extracted_model_name, product_id, keyword, match_confidence, match_method, is_verified)
            VALUES (%s, %s, %s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE
            product_id = VALUES(product_id),
            keyword = VALUES(keyword),
            match_confidence = VALUES(match_confidence),
            match_method = VALUES(match_method),
            is_verified = VALUES(is_verified),
            updated_at = CURRENT_TIMESTAMP
            """
            
            is_verified = match_method == 'manual' or match_confidence >= 0.9
            
            result = db_manager.execute_query(
                query, 
                (image_filename, extracted_model, product_id, keyword, match_confidence, match_method, is_verified),
                fetch=False
            )
            
            return result is not None and result > 0
            
        except Exception as e:
            logger.error(f"保存图片产品映射失败: {e}")
            return False
    
    def get_image_mappings(self, image_filename: str = None, min_confidence: float = 0.9) -> List[Dict]:
        """获取图片映射关系（过滤低置信度匹配，只返回最佳匹配）"""
        try:
            # 获取数据库管理器
            db_manager = get_db_manager()

            if image_filename:
                query = """
                SELECT ipm.*, pi.model_name as product_model_name, pi.main_image_url
                FROM image_product_mapping ipm
                LEFT JOIN product_info pi ON ipm.product_id = pi.id
                WHERE ipm.image_filename = %s AND ipm.match_confidence >= %s AND pi.id IS NOT NULL
                ORDER BY ipm.match_confidence DESC
                LIMIT 1
                """
                params = (image_filename, min_confidence)
            else:
                query = """
                SELECT ipm.*, pi.model_name as product_model_name, pi.main_image_url
                FROM image_product_mapping ipm
                LEFT JOIN product_info pi ON ipm.product_id = pi.id
                WHERE ipm.match_confidence >= %s AND pi.id IS NOT NULL
                ORDER BY ipm.updated_at DESC
                """
                params = (min_confidence,)

            results = db_manager.execute_query(query, params)

            # 修正数据结构：确保id字段指向正确的product_id
            if results:
                logger.info(f"🔧 修正数据结构前: {len(results)} 条记录")
                for result in results:
                    original_id = result['id']
                    product_id = result['product_id']
                    logger.info(f"  修正前: id={original_id}, product_id={product_id}")

                    # 保存原始的mapping ID
                    result['mapping_id'] = result['id']
                    # 将id字段设置为正确的product_id
                    result['id'] = result['product_id']

                    logger.info(f"  修正后: id={result['id']}, mapping_id={result['mapping_id']}")

            return results or []
            
        except Exception as e:
            logger.error(f"获取图片映射关系失败: {e}")
            return []
    
    def auto_match_images(self, image_files: List[Dict], include_ozon_status: bool = False) -> Dict[str, List[Dict]]:
        """自动匹配图片与产品（优化版：批量处理）"""
        if not image_files:
            return {}

        logger.info(f"🚀 开始批量匹配 {len(image_files)} 个图片")
        if include_ozon_status:
            logger.info("🔗 包含OZON上传状态信息")

        import time as time_module
        start_time = time_module.time()

        try:
            logger.info(f"🔍 自动匹配入口：准备匹配 {len(image_files)} 个图片")
            for i, img in enumerate(image_files[:3]):  # 只显示前3个
                logger.info(f"  图片{i+1}: {img.get('filename')} -> {img.get('extracted_model')}")

            # 使用优化的批量匹配方法
            match_results = self._auto_match_images_optimized(image_files, include_ozon_status)

            # 统计结果
            total_images = len(image_files)
            matched_count = sum(1 for matches in match_results.values() if matches)
            high_confidence_count = sum(1 for matches in match_results.values()
                                      if matches and matches[0]['match_confidence'] >= 0.8)

            # 如果包含OZON状态，统计OZON相关信息（按唯一产品ID去重统计）
            ozon_stats = {}
            if include_ozon_status:
                ozon_uploaded_count = 0
                ozon_pending_count = 0
                ozon_failed_count = 0

                # 收集所有匹配到的唯一产品ID及其状态
                unique_products = {}
                for matches in match_results.values():
                    if matches:
                        best_match = matches[0]
                        product_id = best_match.get('id')
                        if product_id and product_id not in unique_products:
                            unique_products[product_id] = best_match.get('ozon_upload_status', 'none')

                # 按唯一产品统计各种状态
                for product_id, status in unique_products.items():
                    if status == 'uploaded':
                        ozon_uploaded_count += 1
                    elif status == 'pending':
                        ozon_pending_count += 1
                    elif status == 'failed':
                        ozon_failed_count += 1

                ozon_stats = {
                    'ozon_uploaded_count': ozon_uploaded_count,
                    'ozon_pending_count': ozon_pending_count,
                    'ozon_failed_count': ozon_failed_count
                }

            elapsed_time = time_module.time() - start_time
            logger.info(f"✅ 批量匹配完成: {matched_count}/{total_images} 个图片找到匹配, "
                       f"高置信度匹配: {high_confidence_count}, 耗时: {elapsed_time:.2f}秒")

            if include_ozon_status and ozon_stats:
                logger.info(f"🔗 OZON状态统计: 已上传={ozon_stats['ozon_uploaded_count']}, "
                           f"待处理={ozon_stats['ozon_pending_count']}, 失败={ozon_stats['ozon_failed_count']}")

            # 如果需要返回统计信息，可以在这里添加
            result = {
                'match_results': match_results,
                'statistics': {
                    'total_images': total_images,
                    'matched_count': matched_count,
                    'high_confidence_count': high_confidence_count,
                    'unmatched_count': total_images - matched_count,
                    **ozon_stats
                }
            }

            return result

        except Exception as e:
            logger.error(f"批量匹配失败: {e}")
            # 降级到原有的逐个匹配方式
            logger.info("降级到逐个匹配模式")
            return self._auto_match_images_legacy(image_files, include_ozon_status)

    def _auto_match_images_optimized(self, image_files: List[Dict], include_ozon_status: bool = False) -> Dict[str, List[Dict]]:
        """优化版批量匹配：一次性加载所有产品数据到内存"""
        logger.info(f"🚀 开始优化版批量匹配，图片数量: {len(image_files)}")

        # 一次性加载所有产品数据
        all_products = self._load_all_products_for_matching(include_ozon_status)
        logger.info(f"📦 已加载 {len(all_products)} 个产品到内存进行匹配")

        # 强制调试：检查特定产品是否被加载
        logger.info(f"🔍🔍🔍 开始检查IIIF150产品...")
        iiif150_products = [p for p in all_products if 'iiif150' in p.get('model_name', '').lower()]
        logger.info(f"🔍🔍🔍 IIIF150产品数量: {len(iiif150_products)}")
        if iiif150_products:
            logger.info(f"🔍 发现IIIF150产品: {[p.get('model_name') for p in iiif150_products]}")
        else:
            logger.info(f"⚠️ 未发现IIIF150产品在加载的产品列表中")
            # 显示前10个产品作为参考
            sample_products = [p.get('model_name') for p in all_products[:10]]
            logger.info(f"🔍 前10个产品示例: {sample_products}")

        if include_ozon_status:
            # 正确统计已上传到OZON的产品数量（is_uploaded_to_ozon为1的产品）
            ozon_uploaded_count = sum(1 for p in all_products if p.get('is_uploaded_to_ozon') == 1)
            logger.info(f"🔗 其中 {ozon_uploaded_count} 个产品已上传到OZON")

        match_results = {}
        auto_saved_count = 0
        used_product_ids = set()  # 跟踪已使用的产品ID，确保一对一匹配

        for image_file in image_files:
            filename = image_file['filename']
            extracted_model = image_file['extracted_model']

            # 调试信息：记录提取的模型名称
            if 'WP30' in filename or 'C17' in filename:
                logger.info(f"🔍 调试提取: 文件名='{filename}', 提取模型='{extracted_model}'")

            if not extracted_model:
                match_results[filename] = []
                continue

            # 在内存中查找匹配，排除已使用的产品
            available_products = [p for p in all_products if p['id'] not in used_product_ids]
            matches = self._find_matches_in_memory(extracted_model, available_products, include_ozon_status)

            # 如果找到匹配，标记产品为已使用
            if matches:
                best_match = matches[0]
                used_product_ids.add(best_match['id'])

                # 保存最佳匹配（如果置信度足够高）
                if best_match['match_confidence'] >= 0.8:
                    if self.save_image_product_mapping(
                        filename,
                        extracted_model,
                        best_match['id'],
                        None,  # keyword
                        best_match['match_confidence'],
                        best_match['match_method']
                    ):
                        auto_saved_count += 1

            match_results[filename] = matches

        if auto_saved_count > 0:
            logger.info(f"💾 自动保存了 {auto_saved_count} 个高置信度匹配")

        return match_results

    def _auto_match_images_legacy(self, image_files: List[Dict], include_ozon_status: bool = False) -> Dict[str, List[Dict]]:
        """传统的逐个匹配方式（降级方案）"""
        match_results = {}
        used_product_ids = set()  # 跟踪已使用的产品ID，确保一对一匹配

        for image_file in image_files:
            filename = image_file['filename']
            extracted_model = image_file['extracted_model']

            if not extracted_model:
                match_results[filename] = []
                continue

            # 查找匹配的产品
            matches = self.find_matching_products(extracted_model)

            # 过滤掉已使用的产品ID
            filtered_matches = []
            for match in matches:
                if match['id'] not in used_product_ids:
                    filtered_matches.append(match)
                    used_product_ids.add(match['id'])
                    break  # 只取第一个未使用的匹配

            # 如果需要OZON状态，为每个匹配结果添加OZON信息
            if include_ozon_status and filtered_matches:
                filtered_matches = self._enhance_matches_with_ozon_status(filtered_matches)

            # 保存最佳匹配（如果置信度足够高）
            if filtered_matches and filtered_matches[0]['match_confidence'] >= 0.8:
                best_match = filtered_matches[0]
                self.save_image_product_mapping(
                    filename,
                    extracted_model,
                    best_match['id'],
                    None,  # keyword
                    best_match['match_confidence'],
                    best_match['match_method']
                )

            match_results[filename] = filtered_matches

        return {'match_results': match_results}

    def force_rematch_images(self, image_files: List[Dict], include_ozon_status: bool = False) -> Dict:
        """强制重新匹配图片，忽略历史记录，确保一对一匹配"""
        logger.info(f"🔄🔄🔄 强制重新匹配模式启动！{len(image_files)} 个图片 🔄🔄🔄")

        # 获取所有产品数据到内存
        all_products = self._load_all_products_to_memory(include_ozon_status)
        logger.info(f"📦 已加载 {len(all_products)} 个产品到内存进行匹配")

        if include_ozon_status:
            ozon_uploaded_count = sum(1 for p in all_products if p.get('is_uploaded_to_ozon') == 1)
            logger.info(f"🔗 其中 {ozon_uploaded_count} 个产品已上传到OZON")

        match_results = {}
        used_products = set()  # 跟踪已使用的产品ID，确保一对一匹配

        for image_file in image_files:
            filename = image_file['filename']
            extracted_model = image_file['extracted_model']

            if not extracted_model:
                match_results[filename] = []
                continue

            # 在内存中查找匹配，排除已使用的产品
            available_products = [p for p in all_products if p['id'] not in used_products]
            matches = self._find_matches_in_memory(extracted_model, available_products, include_ozon_status)

            if matches:
                # 只取最佳匹配，确保一对一
                best_match = matches[0]
                used_products.add(best_match['id'])
                match_results[filename] = [best_match]

                logger.info(f"✅ {filename} -> 产品ID:{best_match['id']}, 置信度:{best_match['match_confidence']:.3f}")
            else:
                match_results[filename] = []
                logger.info(f"❌ {filename} -> 无匹配")

        logger.info(f"🎯 强制重新匹配完成，使用了 {len(used_products)} 个不同产品，确保一对一匹配")

        return {'match_results': match_results}

    def _enhance_matches_with_ozon_status(self, matches: List[Dict]) -> List[Dict]:
        """为匹配结果添加OZON状态信息（降级方案使用）"""
        try:
            if not matches:
                return matches

            # 获取所有匹配产品的ID (使用product_id而不是mapping表的id)
            product_ids = [match['product_id'] for match in matches]

            # 批量查询OZON状态
            db_manager = get_db_manager()
            placeholders = ','.join(['%s'] * len(product_ids))
            query = f"""
                SELECT
                    pi.id,
                    pi.ozon_upload_status,
                    pi.ozon_product_id,
                    pi.last_ozon_upload,
                    op.ozon_product_id as ozon_exists,
                    op.is_archived as ozon_archived,
                    op.name as ozon_name,
                    CASE
                        WHEN op.ozon_product_id IS NOT NULL
                             AND (op.is_archived = 0 OR op.is_archived IS NULL)
                        THEN 1
                        ELSE 0
                    END as is_uploaded_to_ozon
                FROM product_info pi
                LEFT JOIN ozon_products op ON pi.ozon_product_id = op.ozon_product_id
                WHERE pi.id IN ({placeholders})
            """

            ozon_data = db_manager.execute_query(query, product_ids)

            # 创建ID到OZON数据的映射
            ozon_map = {row['id']: row for row in ozon_data} if ozon_data else {}

            # 为每个匹配结果添加OZON状态
            enhanced_matches = []
            for match in matches:
                product_id = match['product_id']  # 使用正确的product_id
                ozon_info = ozon_map.get(product_id, {})

                enhanced_match = {
                    **match,
                    'product_id': product_id,  # 确保前端能获取到product_id
                    'ozon_upload_status': ozon_info.get('ozon_upload_status', 'none'),
                    'ozon_product_id': ozon_info.get('ozon_product_id'),
                    'is_uploaded_to_ozon': bool(ozon_info.get('is_uploaded_to_ozon', 0)),
                    'ozon_name': ozon_info.get('ozon_name'),
                    'last_ozon_upload': ozon_info.get('last_ozon_upload'),
                    'ozon_archived': bool(ozon_info.get('ozon_archived', 0))
                }

                enhanced_matches.append(enhanced_match)

            # 重新排序：优先显示已上传到OZON的商品
            enhanced_matches.sort(key=lambda x: (x['is_uploaded_to_ozon'], x['match_confidence']), reverse=True)

            return enhanced_matches

        except Exception as e:
            logger.error(f"增强匹配结果失败: {e}")
            return matches

    def _load_all_products_for_matching(self, include_ozon_status: bool = False) -> List[Dict]:
        """一次性加载所有产品数据到内存进行匹配"""
        try:
            # 获取数据库管理器
            db_manager = get_db_manager()

            if include_ozon_status:
                # 联合查询product_info和ozon_products表，获取OZON状态
                query = """
                    SELECT
                        pi.id,
                        pi.model_name,
                        pi.item_code,
                        pi.keywords,
                        pi.main_image_url,
                        pi.updated_at,
                        pi.ozon_upload_status,
                        pi.ozon_product_id,
                        pi.last_ozon_upload,
                        op.ozon_product_id as ozon_exists,
                        op.is_archived as ozon_archived,
                        op.name as ozon_name,
                        CASE
                            WHEN op.ozon_product_id IS NOT NULL
                                 AND (op.is_archived = 0 OR op.is_archived IS NULL)
                            THEN 1
                            ELSE 0
                        END as is_uploaded_to_ozon
                    FROM product_info pi
                    LEFT JOIN ozon_products op ON pi.ozon_product_id = op.ozon_product_id
                    WHERE pi.model_name IS NOT NULL AND pi.model_name != ''
                    ORDER BY is_uploaded_to_ozon DESC, pi.updated_at DESC
                """
            else:
                # 保持原有查询逻辑
                query = """
                    SELECT id, model_name, item_code, keywords, main_image_url, updated_at
                    FROM product_info
                    WHERE model_name IS NOT NULL AND model_name != ''
                    ORDER BY updated_at DESC
                """

            products = db_manager.execute_query(query)

            # 调试：检查特定产品是否在查询结果中
            cubot_products = [p for p in (products or []) if 'cubot' in p.get('model_name', '').lower()]
            logger.info(f"🔍 数据库查询返回 {len(products or [])} 个产品")
            logger.info(f"🔍 其中Cubot产品: {len(cubot_products)} 个")
            if cubot_products:
                logger.info(f"🔍 Cubot产品详情: {[p.get('model_name') for p in cubot_products]}")

            # 特别检查 Cubot P80
            cubot_p80 = [p for p in (products or []) if p.get('model_name') == 'Cubot P80']
            if cubot_p80:
                logger.info(f"✅ 找到 Cubot P80 产品: {cubot_p80[0]}")
            else:
                logger.warning(f"❌ 未找到 Cubot P80 产品")

            return products or []

        except Exception as e:
            logger.error(f"加载产品数据失败: {e}")
            return []

    def _find_matches_in_memory(self, model_name: str, products: List[Dict], include_ozon_status: bool = False) -> List[Dict]:
        """在内存中进行匹配，避免重复数据库查询"""
        try:
            clean_model = self._clean_model_name(model_name)
            matches = []

            # 调试：检查是否有 Cubot P80 产品
            cubot_p80_products = [p for p in products if p.get('model_name') == 'Cubot P80']
            if model_name.lower() == 'cubot p80':
                logger.info(f"🔍 搜索 Cubot P80，产品列表中有 {len(cubot_p80_products)} 个匹配")
                if cubot_p80_products:
                    logger.info(f"🔍 找到的 Cubot P80 产品: {cubot_p80_products[0]}")

            for product in products:
                confidence = self._calculate_similarity_optimized(clean_model, product)

                # 调试信息：记录匹配尝试
                if product.get('model_name') in ['Oukitel Wp30/30 Pro', 'Oukitel C17/17Pro', 'Cubot P80']:
                    logger.info(f"🔍 调试匹配: '{model_name}' vs '{product.get('model_name')}', 置信度: {confidence:.3f}")

                # 降低匹配阈值，允许更多匹配可能性
                if confidence >= 0.8:  # 降低到0.8，允许更多匹配
                    # 根据置信度选择合适的匹配方法
                    if confidence >= 0.95:
                        method = 'exact'
                    elif confidence >= 0.8:
                        method = 'fuzzy'
                    else:
                        method = 'keyword'

                    match_info = {
                        'id': product['id'],
                        'model_name': product['model_name'],
                        'main_image_url': product['main_image_url'],
                        'updated_at': product['updated_at'],
                        'match_confidence': confidence,
                        'match_method': method
                    }

                    # 如果需要包含OZON状态信息
                    if include_ozon_status:
                        # 判断是否真正上传到OZON：只要在ozon_products表中存在且未归档
                        is_uploaded = (
                            product.get('ozon_exists') is not None and
                            not bool(product.get('ozon_archived', 0))
                        )

                        match_info.update({
                            'ozon_upload_status': 'uploaded' if is_uploaded else product.get('ozon_upload_status', 'none'),
                            'ozon_product_id': product.get('ozon_product_id'),
                            'is_uploaded_to_ozon': is_uploaded,
                            'ozon_name': product.get('ozon_name'),
                            'last_ozon_upload': product.get('last_ozon_upload'),
                            'ozon_archived': bool(product.get('ozon_archived', 0))
                        })

                    matches.append(match_info)

            # 按置信度排序，如果包含OZON状态，优先显示已上传的商品
            if include_ozon_status:
                matches.sort(key=lambda x: (x['is_uploaded_to_ozon'], x['match_confidence']), reverse=True)
            else:
                matches.sort(key=lambda x: x['match_confidence'], reverse=True)

            # 只返回前10个最佳匹配
            return matches[:10]

        except Exception as e:
            logger.error(f"内存匹配失败: {e}")
            return []

    def _clean_model_name(self, model_name: str) -> str:
        """清理模型名称"""
        if not model_name:
            return ""

        # 转换为小写并去除多余空格
        clean_name = model_name.lower().strip()

        # 移除常见的无用字符
        import re
        clean_name = re.sub(r'[^\w\s-]', '', clean_name)
        clean_name = re.sub(r'\s+', ' ', clean_name)

        return clean_name

    def _calculate_similarity_optimized(self, search_name: str, product: Dict) -> float:
        """优化的相似度计算，增强对命名差异的处理"""
        try:
            product_model = self._clean_model_name(product.get('model_name', ''))
            product_item_code = self._clean_model_name(product.get('item_code', ''))
            product_keywords = self._clean_model_name(product.get('keywords', ''))

            # 精确匹配
            if search_name == product_model:
                return 1.0

            # 增强匹配：处理常见的命名差异
            # 1. 移除所有空格和特殊字符进行比较
            search_compact = ''.join(search_name.split()).replace('-', '')
            model_compact = ''.join(product_model.split()).replace('-', '')

            if search_compact == model_compact:
                return 1.0

            # 2. 处理型号变体匹配（如 F150 匹配 IIIF150）
            original_model = product.get('model_name', '')

            # 处理常见的型号前缀变体
            search_normalized = search_name.lower()
            model_normalized = original_model.lower()

            # F150 <-> IIIF150 变体匹配
            if 'f150' in search_normalized and 'iiif150' in model_normalized:
                # 检查其他部分是否匹配
                search_parts = search_normalized.replace('f150', 'PLACEHOLDER').split()
                model_parts = model_normalized.replace('iiif150', 'PLACEHOLDER').split()

                # 如果其他部分基本匹配，给予高分
                common_parts = set(search_parts) & set(model_parts)
                if len(common_parts) >= len(search_parts) * 0.8:  # 80%的部分匹配
                    return 0.95

            # 反向匹配：IIIF150 -> F150
            if 'iiif150' in search_normalized and 'f150' in model_normalized:
                search_parts = search_normalized.replace('iiif150', 'PLACEHOLDER').split()
                model_parts = model_normalized.replace('f150', 'PLACEHOLDER').split()

                common_parts = set(search_parts) & set(model_parts)
                if len(common_parts) >= len(search_parts) * 0.8:
                    return 0.95

            # 3. 处理组合型号匹配（如 C17/17Pro 匹配 C17 -17Pro）
            if '/' in original_model:
                # 分割组合型号
                model_parts = [part.strip() for part in original_model.split('/')]
                search_lower = search_name.lower()

                # 检查是否匹配任一部分或组合
                for part in model_parts:
                    part_clean = self._clean_model_name(part)
                    part_compact = ''.join(part_clean.split()).replace('-', '')

                    if search_compact.lower() == part_compact.lower():
                        return 0.98  # 单个型号精确匹配

                # 检查是否是多型号表示（如 "c17 17pro" 匹配 "C17/17Pro"）
                search_words = search_name.lower().replace('-', ' ').split()
                model_words = []
                for part in model_parts:
                    model_words.extend(self._clean_model_name(part).split())

                # 如果搜索词包含了组合型号的主要部分
                if len(search_words) >= 2 and len(model_words) >= 2:
                    match_count = 0
                    for search_word in search_words:
                        for model_word in model_words:
                            if search_word in model_word.lower() or model_word.lower() in search_word:
                                match_count += 1
                                break

                    if match_count >= len(search_words) * 0.8:  # 80%的词匹配
                        return 0.96

            # 3. 检查核心部分匹配（处理版本号差异）
            search_core = search_compact.lower()
            model_core = model_compact.lower()

            # 特殊处理：如果搜索名是产品名的前缀（如 wp30 vs wp3030pro）
            if search_core in model_core and len(search_core) >= 4:
                # 计算匹配度：搜索名长度 / 产品名长度，但给予高分
                ratio = len(search_core) / len(model_core)
                if ratio >= 0.6:  # 如果搜索名占产品名60%以上
                    return 0.95  # 给予高匹配度

            # 反向检查：产品名是搜索名的前缀
            if model_core in search_core and len(model_core) >= 4:
                ratio = len(model_core) / len(search_core)
                if ratio >= 0.6:
                    return 0.95

            # 计算各字段的相似度
            from difflib import SequenceMatcher

            model_similarity = SequenceMatcher(None, search_name, product_model).ratio()
            item_code_similarity = SequenceMatcher(None, search_name, product_item_code).ratio()
            keywords_similarity = SequenceMatcher(None, search_name, product_keywords).ratio()

            # 包含匹配加分
            contains_bonus = 0.0
            if search_name in product_model or product_model in search_name:
                contains_bonus += 0.2
            if search_name in product_item_code or product_item_code in search_name:
                contains_bonus += 0.15
            if search_name in product_keywords or product_keywords in search_name:
                contains_bonus += 0.1

            # 综合计算最终相似度
            final_similarity = max(
                model_similarity * 0.6 + contains_bonus,
                item_code_similarity * 0.5 + contains_bonus,
                keywords_similarity * 0.4 + contains_bonus
            )

            return min(final_similarity, 1.0)  # 确保不超过1.0

        except Exception as e:
            logger.error(f"计算相似度失败: {e}")
            return 0.0
    
    def need_update_image(self, image_file: Dict, product_info: Dict) -> Tuple[bool, str]:
        """判断是否需要更新图片"""
        try:
            # 如果数据库中没有图片URL，需要上传
            if not product_info.get('main_image_url'):
                return True, "数据库中没有图片URL"
            
            # 如果数据库中没有更新时间，需要上传
            if not product_info.get('updated_at'):
                return True, "数据库中没有更新时间"
            
            # 解析数据库中的更新时间
            if isinstance(product_info['updated_at'], str):
                db_update_time = datetime.datetime.strptime(product_info['updated_at'], '%Y-%m-%d %H:%M:%S')
            else:
                db_update_time = product_info['updated_at']
            
            # 本地文件的修改时间
            file_mtime = image_file['modified_time']
            
            # 如果本地文件比数据库记录新，需要更新
            if file_mtime > db_update_time:
                return True, f"本地文件更新 (文件: {image_file['modified_time_str']}, 数据库: {db_update_time.strftime('%Y-%m-%d %H:%M:%S')})"
            else:
                return False, f"文件无需更新 (文件: {image_file['modified_time_str']}, 数据库: {db_update_time.strftime('%Y-%m-%d %H:%M:%S')})"
        
        except Exception as e:
            return True, f"时间比较失败，强制更新: {e}"

    def update_product_image_url(self, product_id: int, image_url: str) -> bool:
        """更新产品的图片URL"""
        try:
            logger.info(f"🔄 开始更新产品图片URL: product_id={product_id}, image_url={image_url}")

            # 获取数据库管理器
            db_manager = get_db_manager()

            query = """
            UPDATE product_info
            SET main_image_url = %s,
                image_upload_status = 'uploaded',
                last_image_update = CURRENT_TIMESTAMP
            WHERE id = %s
            """

            logger.info(f"🔄 执行SQL更新: {query}")
            logger.info(f"🔄 参数: image_url='{image_url}', product_id={product_id}")

            result = db_manager.execute_query(query, (image_url, product_id), fetch=False)

            logger.info(f"🔄 数据库更新结果: {result}, 类型: {type(result)}")

            if result is not None and result > 0:
                logger.info(f"✅ 产品图片URL更新成功: product_id={product_id}")
                return True
            else:
                logger.warning(f"⚠️ 数据库更新返回值异常: {result}")
                return False

        except Exception as e:
            logger.error(f"❌ 更新产品图片URL失败: product_id={product_id}, error={e}")
            return False

# 全局图片服务实例
image_service = ImageService()
