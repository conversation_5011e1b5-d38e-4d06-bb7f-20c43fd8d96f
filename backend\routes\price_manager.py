#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
商品价格管理API
提供商品价格设置、批量更新、最低价格管理等功能
"""

import logging
from flask import Blueprint, request, jsonify
from models.database import get_db_manager
from services.ozon_api_service import OzonApiService

logger = logging.getLogger(__name__)

# 创建蓝图
price_manager_bp = Blueprint('price_manager', __name__, url_prefix='/price-manager')

@price_manager_bp.route('/api/set-min-price', methods=['POST'])
def api_set_min_price():
    """
    设置商品价格管理API

    Request Body:
    {
        "offer_id": "Oukitel-Wp32",
        "new_price": "225",           // 新的销售价格（可选）
        "min_price": "120",           // 最低价格（必需）
        "currency_code": "CNY",
        "apply_to_ozon": true
    }
    """
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求体不能为空'
            }), 400

        # 获取参数
        offer_id = data.get('offer_id', '').strip()
        new_price = data.get('new_price', '').strip()  # 新增：新价格参数
        min_price = data.get('min_price', '').strip()
        currency_code = data.get('currency_code', 'CNY').strip()
        apply_to_ozon = data.get('apply_to_ozon', False)

        logger.info(f"🔧 价格管理请求: offer_id={offer_id}, new_price={new_price}, min_price={min_price}, currency={currency_code}, apply_to_ozon={apply_to_ozon}")

        # 参数验证
        if not offer_id:
            logger.error("❌ 参数验证失败: offer_id为空")
            return jsonify({
                'success': False,
                'error': 'offer_id参数不能为空'
            }), 400

        if not min_price:
            logger.error("❌ 参数验证失败: min_price为空")
            return jsonify({
                'success': False,
                'error': 'min_price参数不能为空'
            }), 400

        # 验证最低价格
        try:
            min_price_float = float(min_price)
            if min_price_float <= 0:
                raise ValueError("最低价格必须大于0")
        except ValueError as e:
            logger.error(f"❌ 最低价格验证失败: {e}")
            return jsonify({
                'success': False,
                'error': f'min_price参数无效: {e}'
            }), 400

        # 验证新价格（如果提供）
        new_price_float = None
        if new_price:
            try:
                new_price_float = float(new_price)
                if new_price_float <= 0:
                    raise ValueError("新价格必须大于0")
                if new_price_float < min_price_float:
                    raise ValueError("新价格不能低于最低价格")
            except ValueError as e:
                logger.error(f"❌ 新价格验证失败: {e}")
                return jsonify({
                    'success': False,
                    'error': f'new_price参数无效: {e}'
                }), 400

        logger.info(f"✅ 参数验证通过: new_price={new_price_float}, min_price={min_price_float}")

        # 获取数据库管理器
        db_manager = get_db_manager()

        # 查找商品
        logger.info(f"🔍 查找商品: {offer_id}")
        product_query = """
            SELECT id, offer_id, ozon_product_id, name, price, old_price, currency_code
            FROM ozon_products
            WHERE offer_id = %s
        """
        products = db_manager.execute_query(product_query, (offer_id,))

        if not products:
            logger.error(f"❌ 商品未找到: {offer_id}")
            return jsonify({
                'success': False,
                'error': f'未找到商品: {offer_id}'
            }), 404

        product = products[0]
        product_id = product['id']
        ozon_product_id = product['ozon_product_id']
        current_price = float(product['price']) if product['price'] else 0
        current_old_price = float(product['old_price']) if product['old_price'] else 0

        logger.info(f"✅ 商品信息: id={product_id}, ozon_id={ozon_product_id}, current_price={current_price}")

        # 确定最终的新价格（如果没有提供新价格，则保持当前价格）
        final_new_price = new_price_float if new_price_float is not None else current_price
        price_changed = new_price_float is not None and new_price_float != current_price

        logger.info(f"💰 价格设置: 当前价格={current_price}, 新价格={final_new_price}, 最低价格={min_price_float}")

        result = {
            'success': True,
            'product_info': {
                'offer_id': offer_id,
                'ozon_product_id': ozon_product_id,
                'name': product['name'],
                'current_price': current_price,
                'current_old_price': current_old_price,
                'current_currency': product['currency_code']
            },
            'price_update': {
                'new_price': final_new_price,        # 新增：新的销售价格
                'min_price': min_price_float,
                'currency_code': currency_code,
                'price_changed': price_changed,      # 新增：价格是否发生变化
                'local_updated': False,
                'ozon_updated': False,
                'local_error': None,                 # 详细的本地错误信息
                'ozon_error': None                   # 详细的OZON错误信息
            }
        }

        # 更新本地数据库
        try:
            # 检查是否存在价格配置表，如果没有则创建
            create_config_table_query = """
                CREATE TABLE IF NOT EXISTS product_price_config (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    product_id INT NOT NULL,
                    offer_id VARCHAR(100) NOT NULL,
                    min_price DECIMAL(10,2),
                    max_price DECIMAL(10,2),
                    currency_code VARCHAR(10) DEFAULT 'CNY',
                    auto_adjust_enabled BOOLEAN DEFAULT FALSE,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
                    UNIQUE KEY unique_product (product_id),
                    INDEX idx_offer_id (offer_id)
                )
            """
            db_manager.execute_query(create_config_table_query, fetch=False)

            # 如果提供了新价格且与当前价格不同，需要同时更新ozon_products表
            if price_changed:
                logger.info(f"🔄 更新商品价格: {offer_id} -> {current_price} => {final_new_price}")
                update_price_query = """
                    UPDATE ozon_products
                    SET price = %s, updated_at = CURRENT_TIMESTAMP
                    WHERE id = %s
                """
                db_manager.execute_query(update_price_query, (final_new_price, product_id), fetch=False)
                logger.info(f"✅ ozon_products表价格更新成功: {offer_id} -> price={final_new_price}")

            # 插入或更新价格配置
            upsert_config_query = """
                INSERT INTO product_price_config (product_id, offer_id, min_price, currency_code)
                VALUES (%s, %s, %s, %s)
                ON DUPLICATE KEY UPDATE
                min_price = VALUES(min_price),
                currency_code = VALUES(currency_code),
                updated_at = CURRENT_TIMESTAMP
            """
            db_manager.execute_query(upsert_config_query, (product_id, offer_id, min_price_float, currency_code), fetch=False)

            result['price_update']['local_updated'] = True
            logger.info(f"✅ 本地价格配置更新成功: {offer_id} -> min_price={min_price_float} {currency_code}")

        except Exception as e:
            logger.error(f"❌ 本地价格配置更新失败: {e}")
            result['price_update']['local_error'] = str(e)

        # 如果需要同步到OZON
        if apply_to_ozon:
            try:
                ozon_service = OzonApiService()

                # 构建OZON价格更新请求
                # 使用最终确定的新价格进行OZON同步
                price_data = {
                    "prices": [{
                        "product_id": int(ozon_product_id),
                        "offer_id": offer_id,
                        "price": str(final_new_price),  # 使用最终的新价格
                        "old_price": str(current_old_price) if current_old_price > 0 else "0",
                        "min_price": str(min_price_float),
                        "currency_code": currency_code,
                        "min_price_for_auto_actions_enabled": True,
                        "auto_action_enabled": "ENABLED"  # 启用自动活动
                    }]
                }

                logger.info(f"🌐 向OZON发送价格更新请求: {price_data}")

                # 调用OZON价格更新API
                response = ozon_service.call_ozon_api(
                    endpoint='/v1/product/import/prices',
                    data=price_data,
                    method='POST'
                )

                if response and response.get('result'):
                    result_item = response['result'][0] if response['result'] else {}
                    if result_item.get('updated'):
                        result['price_update']['ozon_updated'] = True
                        result['price_update']['ozon_response'] = result_item
                        logger.info(f"✅ OZON价格更新成功: {response}")
                    else:
                        result['price_update']['ozon_updated'] = False
                        result['price_update']['ozon_error'] = result_item.get('errors', '更新失败')
                        logger.error(f"❌ OZON价格更新失败: {result_item}")
                else:
                    result['price_update']['ozon_updated'] = False
                    result['price_update']['ozon_error'] = '无响应数据'
                    logger.error("❌ OZON API无响应数据")

            except Exception as e:
                logger.error(f"❌ OZON价格更新失败: {e}")
                result['price_update']['ozon_updated'] = False
                result['price_update']['ozon_error'] = str(e)

        logger.info(f"🎉 价格设置完成: local_updated={result['price_update']['local_updated']}, ozon_updated={result['price_update']['ozon_updated']}, price_changed={result['price_update']['price_changed']}")
        return jsonify(result)

    except Exception as e:
        logger.error(f"❌ 设置最低价格异常: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {e}',
            'price_update': {
                'local_updated': False,
                'ozon_updated': False,
                'local_error': str(e),
                'ozon_error': None
            }
        }), 500


@price_manager_bp.route('/api/get-price-config/<offer_id>', methods=['GET'])
def api_get_price_config(offer_id):
    """
    获取商品价格配置
    """
    try:
        logger.info(f"获取商品价格配置: {offer_id}")
        
        db_manager = get_db_manager()
        
        # 查询商品和价格配置
        query = """
            SELECT 
                op.id, op.offer_id, op.ozon_product_id, op.name, 
                op.price, op.old_price, op.currency_code,
                ppc.min_price, ppc.max_price, ppc.auto_adjust_enabled,
                ppc.updated_at as config_updated_at
            FROM ozon_products op
            LEFT JOIN product_price_config ppc ON op.id = ppc.product_id
            WHERE op.offer_id = %s
        """
        
        results = db_manager.execute_query(query, (offer_id,))
        
        if not results:
            return jsonify({
                'success': False,
                'error': f'未找到商品: {offer_id}'
            }), 404
            
        product = results[0]
        
        return jsonify({
            'success': True,
            'data': {
                'product_info': {
                    'offer_id': product['offer_id'],
                    'ozon_product_id': product['ozon_product_id'],
                    'name': product['name'],
                    'current_price': float(product['price']) if product['price'] else 0,
                    'current_old_price': float(product['old_price']) if product['old_price'] else 0,
                    'currency_code': product['currency_code']
                },
                'price_config': {
                    'min_price': float(product['min_price']) if product['min_price'] else None,
                    'max_price': float(product['max_price']) if product['max_price'] else None,
                    'auto_adjust_enabled': bool(product['auto_adjust_enabled']) if product['auto_adjust_enabled'] is not None else False,
                    'last_updated': product['config_updated_at'].isoformat() if product['config_updated_at'] else None
                }
            }
        })
        
    except Exception as e:
        logger.error(f"获取价格配置异常: {e}")
        return jsonify({
            'success': False,
            'error': f'服务器内部错误: {e}'
        }), 500
