"""
OZON商品同步系统管理工具
提供系统管理、配置和维护功能
"""

import sys
import os
import argparse
from datetime import datetime, timedelta

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models.database import get_db_manager
from services.ozon_api_service import OzonApiService
from services.ozon_sync_scheduler import ozon_sync_scheduler

class SystemManager:
    """系统管理器"""
    
    def __init__(self):
        self.db = get_db_manager()
        self.ozon_api_service = OzonApiService()
    
    def start_scheduler(self):
        """启动调度器"""
        try:
            if ozon_sync_scheduler.is_running:
                print("⚠️ 调度器已在运行中")
                return False
            
            ozon_sync_scheduler.start_scheduler()
            print("✅ 调度器启动成功")
            
            status = ozon_sync_scheduler.get_scheduler_status()
            print(f"   定时任务数量: {len(status['next_jobs'])}")
            print(f"   全量同步间隔: {status['full_sync_interval_hours']} 小时")
            print(f"   增量同步间隔: {status['incremental_sync_interval_hours']} 小时")
            return True
            
        except Exception as e:
            print(f"❌ 调度器启动失败: {e}")
            return False
    
    def stop_scheduler(self):
        """停止调度器"""
        try:
            if not ozon_sync_scheduler.is_running:
                print("⚠️ 调度器未在运行")
                return False
            
            ozon_sync_scheduler.stop_scheduler()
            print("✅ 调度器停止成功")
            return True
            
        except Exception as e:
            print(f"❌ 调度器停止失败: {e}")
            return False
    
    def trigger_sync(self, sync_type='incremental', max_pages=5):
        """手动触发同步"""
        try:
            print(f"🔄 开始执行{sync_type}同步...")
            
            result = ozon_sync_scheduler.trigger_manual_sync(
                sync_type=sync_type, 
                max_pages=max_pages
            )
            
            if result['success']:
                print("✅ 同步完成")
                stats = result['stats']
                print(f"   处理商品数: {stats.get('total_list_products', 0)}")
                print(f"   保存列表数: {stats.get('saved_list_count', 0)}")
                print(f"   保存详情数: {stats.get('saved_detail_count', 0)}")
                if 'updated_count' in stats:
                    print(f"   更新商品数: {stats.get('updated_count', 0)}")
                return True
            else:
                print(f"❌ 同步失败: {result['error']}")
                return False
                
        except Exception as e:
            print(f"❌ 同步异常: {e}")
            return False
    
    def cleanup_data(self, days_old=30):
        """清理旧数据"""
        try:
            print(f"🧹 开始清理 {days_old} 天前的数据...")
            
            self.ozon_api_service.cleanup_old_sync_data(days_old=days_old)
            print("✅ 数据清理完成")
            return True
            
        except Exception as e:
            print(f"❌ 数据清理失败: {e}")
            return False
    
    def show_status(self):
        """显示系统状态"""
        print("=" * 60)
        print("OZON商品同步系统状态")
        print("=" * 60)
        
        # 调度器状态
        try:
            scheduler_status = ozon_sync_scheduler.get_scheduler_status()
            print(f"📅 调度器状态: {'运行中' if scheduler_status['is_running'] else '已停止'}")
            if scheduler_status['is_running']:
                print(f"   定时任务数量: {len(scheduler_status['next_jobs'])}")
        except Exception as e:
            print(f"📅 调度器状态: 检查失败 - {e}")
        
        # 数据统计
        try:
            stats = self.ozon_api_service.get_sync_statistics()
            products = stats.get('products', {})
            logs = stats.get('sync_logs', {})
            
            print(f"📦 商品数据:")
            print(f"   总商品数: {products.get('total', 0)}")
            print(f"   已同步: {products.get('synced', 0)}")
            print(f"   已关联: {products.get('linked', 0)}")
            print(f"   最后同步: {products.get('last_sync_time', '无')}")
            
            print(f"📊 同步日志:")
            print(f"   总同步次数: {logs.get('total_syncs', 0)}")
            print(f"   成功次数: {logs.get('successful_syncs', 0)}")
            print(f"   失败次数: {logs.get('failed_syncs', 0)}")
            print(f"   平均耗时: {logs.get('avg_duration_seconds', 0):.2f}秒")
            
        except Exception as e:
            print(f"📊 数据统计: 获取失败 - {e}")
        
        # 最近错误
        try:
            recent_errors = self.db.execute_query("""
                SELECT sync_type, error_message, started_at
                FROM ozon_api_sync_log
                WHERE sync_status = 'failed' 
                AND started_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
                ORDER BY started_at DESC
                LIMIT 3
            """)
            
            if recent_errors:
                print(f"🚨 最近错误 ({len(recent_errors)} 个):")
                for error in recent_errors:
                    print(f"   {error['started_at']} - {error['sync_type']}: {error['error_message'][:50]}...")
            else:
                print("🚨 最近错误: 无")
                
        except Exception as e:
            print(f"🚨 错误检查: 失败 - {e}")
        
        print("=" * 60)
    
    def reset_failed_products(self):
        """重置失败的商品状态"""
        try:
            print("🔄 重置失败商品状态...")
            
            result = self.db.execute_query("""
                UPDATE ozon_products 
                SET api_sync_status = 'pending', sync_retry_count = 0, sync_error_message = NULL
                WHERE api_sync_status = 'failed'
            """)
            
            print(f"✅ 重置了 {result} 个失败商品的状态")
            return True
            
        except Exception as e:
            print(f"❌ 重置失败: {e}")
            return False
    
    def show_sync_logs(self, limit=10):
        """显示同步日志"""
        try:
            logs = self.db.execute_query("""
                SELECT sync_task_id, api_endpoint, sync_type, sync_status,
                       total_processed, duration_seconds, started_at, error_message
                FROM ozon_api_sync_log
                ORDER BY started_at DESC
                LIMIT %s
            """, (limit,))
            
            print(f"📋 最近 {len(logs)} 条同步日志:")
            print("-" * 100)
            print(f"{'时间':<20} {'类型':<15} {'状态':<10} {'处理数':<8} {'耗时':<8} {'错误信息':<30}")
            print("-" * 100)
            
            for log in logs:
                time_str = log['started_at'].strftime('%m-%d %H:%M:%S')
                sync_type = log['sync_type'] or 'N/A'
                status = log['sync_status']
                processed = log['total_processed'] or 0
                duration = f"{log['duration_seconds'] or 0:.1f}s"
                error = (log['error_message'] or '')[:25] + '...' if log['error_message'] and len(log['error_message']) > 25 else (log['error_message'] or '')
                
                print(f"{time_str:<20} {sync_type:<15} {status:<10} {processed:<8} {duration:<8} {error:<30}")
            
        except Exception as e:
            print(f"❌ 获取日志失败: {e}")
    
    def backup_data(self):
        """备份重要数据"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 导出商品数据
            products = self.db.execute_query("SELECT * FROM ozon_products")
            
            import json
            backup_data = {
                'timestamp': timestamp,
                'products_count': len(products),
                'products': products
            }
            
            filename = f"ozon_backup_{timestamp}.json"
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(backup_data, f, ensure_ascii=False, indent=2, default=str)
            
            print(f"✅ 数据备份完成: {filename}")
            print(f"   备份商品数: {len(products)}")
            return True
            
        except Exception as e:
            print(f"❌ 数据备份失败: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='OZON商品同步系统管理工具')
    parser.add_argument('action', choices=[
        'status', 'start', 'stop', 'sync', 'cleanup', 'reset', 'logs', 'backup'
    ], help='要执行的操作')
    
    parser.add_argument('--sync-type', choices=['full', 'incremental'], 
                       default='incremental', help='同步类型')
    parser.add_argument('--max-pages', type=int, default=5, help='最大分页数')
    parser.add_argument('--days-old', type=int, default=30, help='清理天数')
    parser.add_argument('--limit', type=int, default=10, help='日志显示数量')
    
    args = parser.parse_args()
    
    manager = SystemManager()
    
    if args.action == 'status':
        manager.show_status()
    elif args.action == 'start':
        manager.start_scheduler()
    elif args.action == 'stop':
        manager.stop_scheduler()
    elif args.action == 'sync':
        manager.trigger_sync(args.sync_type, args.max_pages)
    elif args.action == 'cleanup':
        manager.cleanup_data(args.days_old)
    elif args.action == 'reset':
        manager.reset_failed_products()
    elif args.action == 'logs':
        manager.show_sync_logs(args.limit)
    elif args.action == 'backup':
        manager.backup_data()

if __name__ == "__main__":
    main()
