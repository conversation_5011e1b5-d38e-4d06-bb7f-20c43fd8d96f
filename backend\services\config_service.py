#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理服务
扩展现有的Config类，支持数据库存储的动态配置
"""

import os
import json
import logging
import hashlib
from typing import Any, Optional, Dict, List
from cryptography.fernet import Fernet
from models.database import get_db_manager
from config import Config

logger = logging.getLogger(__name__)

class ConfigEncryption:
    """配置加密工具类"""

    @staticmethod
    def get_encryption_key() -> bytes:
        """获取加密密钥"""
        key = os.environ.get('CONFIG_ENCRYPTION_KEY')
        if not key:
            # 尝试从文件中读取持久化密钥
            key_file = os.path.join(os.path.dirname(__file__), '..', '.encryption_key')
            try:
                if os.path.exists(key_file):
                    with open(key_file, 'r') as f:
                        key = f.read().strip()
                        logger.info("使用持久化的加密密钥")
                else:
                    # 生成新密钥并保存到文件
                    key = Fernet.generate_key().decode()
                    with open(key_file, 'w') as f:
                        f.write(key)
                    logger.info("生成并保存新的加密密钥")
            except Exception as e:
                # 如果文件操作失败，使用临时密钥
                key = Fernet.generate_key().decode()
                logger.warning(f"无法读取/保存加密密钥文件: {e}，使用临时密钥")
        return key.encode()

    @staticmethod
    def encrypt_config(value: str) -> str:
        """加密配置值"""
        try:
            cipher = Fernet(ConfigEncryption.get_encryption_key())
            return cipher.encrypt(value.encode()).decode()
        except Exception as e:
            logger.error(f"配置加密失败: {e}")
            raise

    @staticmethod
    def decrypt_config(encrypted_value: str) -> str:
        """解密配置值"""
        try:
            cipher = Fernet(ConfigEncryption.get_encryption_key())
            return cipher.decrypt(encrypted_value.encode()).decode()
        except Exception as e:
            logger.error(f"配置解密失败: {e}")
            raise

    @staticmethod
    def generate_hash(value: str) -> str:
        """生成MD5哈希用于验证数据完整性"""
        return hashlib.md5(value.encode()).hexdigest()

    @staticmethod
    def encrypt_for_audit(value: str) -> Dict[str, str]:
        """为审计日志加密敏感数据，返回加密值和哈希"""
        if not value:
            return {'encrypted': None, 'hash': None}

        try:
            encrypted = ConfigEncryption.encrypt_config(value)
            hash_value = ConfigEncryption.generate_hash(value)
            return {
                'encrypted': encrypted,
                'hash': hash_value
            }
        except Exception as e:
            logger.error(f"审计日志加密失败: {e}")
            # 如果加密失败，至少提供哈希
            return {
                'encrypted': None,
                'hash': ConfigEncryption.generate_hash(value)
            }

    @staticmethod
    def decrypt_for_audit(encrypted_value: str) -> str:
        """解密审计日志中的敏感数据"""
        if not encrypted_value:
            return None

        try:
            return ConfigEncryption.decrypt_config(encrypted_value)
        except Exception as e:
            logger.error(f"审计日志解密失败: {e}")
            return "[解密失败]"

class ExtendedConfig(Config):
    """扩展配置类，支持数据库存储的动态配置"""
    
    @staticmethod
    def _parse_config_value(value: str, config_type: str) -> Any:
        """解析配置值"""
        if config_type == 'json':
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value
        elif config_type == 'boolean':
            return value.lower() in ('true', '1', 'yes', 'on')
        elif config_type == 'integer':
            try:
                return int(value)
            except ValueError:
                return 0
        elif config_type == 'encrypted':
            try:
                return ConfigEncryption.decrypt_config(value)
            except:
                return value  # 如果解密失败，返回原值
        else:
            return value

    @staticmethod
    def _get_config_from_env(key: str) -> Any:
        """直接从环境变量获取配置"""
        import os

        # 映射配置键到环境变量名
        env_mapping = {
            'github_token': 'GITHUB_TOKEN',
            'github_repo': 'GITHUB_REPO',
            'github_branch': 'GITHUB_BRANCH',
            'github_folder': 'GITHUB_FOLDER',
            'ozon_client_id': 'OZON_CLIENT_ID',
            'ozon_api_key': 'OZON_API_KEY',
            'config_encryption_key': 'CONFIG_ENCRYPTION_KEY'
        }

        if key in env_mapping:
            env_var = env_mapping[key]
            value = os.environ.get(env_var)
            if value:  # 只有非空值才返回
                return value

        return None

    @staticmethod
    def _get_config_from_file(key: str, default: Any = None) -> Any:
        """从配置文件获取配置"""
        from config import config
        import os

        # 获取当前环境的配置
        env = os.environ.get('FLASK_ENV', 'development')
        current_config = config.get(env, config['default'])

        # 映射配置键到配置文件属性
        config_mapping = {
            'github_token': 'GITHUB_TOKEN',
            'github_repo': 'GITHUB_REPO',
            'github_branch': 'GITHUB_BRANCH',
            'ozon_client_id': 'OZON_CLIENT_ID',
            'ozon_api_key': 'OZON_API_KEY',
            'github_folder': 'GITHUB_FOLDER'
        }

        if key in config_mapping:
            attr_name = config_mapping[key]
            return getattr(current_config, attr_name, default)

        return default

    @staticmethod
    def get_config(key: str, default: Any = None) -> Any:
        """
        获取配置的优先级顺序：
        1. 环境变量（敏感配置优先）
        2. 数据库存储（业务配置）
        3. 配置文件默认值
        """
        try:
            # 对于敏感配置，优先从环境变量获取
            if is_sensitive_config(key):
                env_value = ExtendedConfig._get_config_from_env(key)
                if env_value is not None:
                    logger.debug(f"从环境变量获取敏感配置: {key}")
                    return env_value

            # 尝试从数据库获取（主要用于业务配置）
            db_manager = get_db_manager()
            if db_manager is not None:
                query = "SELECT config_value, config_type FROM system_config WHERE config_key = %s"
                result = db_manager.execute_query(query, (key,))
                if result:
                    value = result[0]['config_value']
                    config_type = result[0]['config_type']
                    parsed_value = ExtendedConfig._parse_config_value(value, config_type)
                    logger.debug(f"从数据库获取配置: {key}")
                    return parsed_value

            # 最后从配置文件获取默认值
            file_value = ExtendedConfig._get_config_from_file(key, default)
            if file_value != default:
                logger.debug(f"从配置文件获取配置: {key}")
            return file_value

        except Exception as e:
            logger.error(f"获取配置失败 {key}: {e}")
            # 出错时尝试从环境变量或配置文件获取
            env_value = ExtendedConfig._get_config_from_env(key)
            if env_value is not None:
                return env_value
            return ExtendedConfig._get_config_from_file(key, default)
    
    @staticmethod
    def set_config(key: str, value: Any, config_type: str = 'string',
                   description: str = '', is_sensitive: bool = False) -> bool:
        """设置配置到数据库，如果数据库不可用则跳过"""
        try:
            db_manager = get_db_manager()
            if db_manager is None:
                logger.warning(f"数据库不可用，跳过配置设置: {key}")
                return True  # 返回成功，因为配置已在文件中
            # 处理不同类型的值
            if config_type == 'json':
                config_value = json.dumps(value, ensure_ascii=False)
            elif config_type == 'boolean':
                config_value = 'true' if value else 'false'
            elif config_type == 'integer':
                config_value = str(int(value))
            elif config_type == 'encrypted' or is_sensitive:
                config_value = ConfigEncryption.encrypt_config(str(value))
                config_type = 'encrypted'
            else:
                config_value = str(value)
            
            query = """
            INSERT INTO system_config (config_key, config_value, config_type, description, is_sensitive)
            VALUES (%s, %s, %s, %s, %s)
            ON DUPLICATE KEY UPDATE 
            config_value = VALUES(config_value),
            config_type = VALUES(config_type),
            description = VALUES(description),
            is_sensitive = VALUES(is_sensitive),
            updated_at = CURRENT_TIMESTAMP
            """
            
            result = get_db_manager().execute_query(
                query, 
                (key, config_value, config_type, description, is_sensitive), 
                fetch=False
            )
            
            # 记录配置变更日志 - 传递原始值，让log_config_change函数处理加密
            log_config_change('system', key, 'update', None, str(value) if is_sensitive else config_value)
            
            return result is not None
            
        except Exception as e:
            logger.error(f"设置配置失败 {key}: {e}")
            return False
    
    @staticmethod
    def get_all_configs(include_sensitive: bool = True) -> Dict[str, Any]:
        """获取所有配置"""
        try:
            query = "SELECT config_key, config_value, config_type, description, is_sensitive FROM system_config"

            db_manager = get_db_manager()
            if not db_manager:
                return {}
            results = db_manager.execute_query(query)
            configs = {}

            for row in results:
                key = row['config_key']
                value = row['config_value']
                config_type = row['config_type']

                # 对于敏感配置，如果include_sensitive为True，则解密返回实际值
                # 如果为False，则返回占位符
                if row['is_sensitive']:
                    if include_sensitive:
                        # 解密敏感配置并返回实际值
                        actual_value = ExtendedConfig._parse_config_value(value, config_type)
                        configs[key] = {
                            'value': actual_value,
                            'type': config_type,
                            'description': row['description'],
                            'is_sensitive': True
                        }
                    else:
                        configs[key] = {
                            'value': '***',
                            'type': config_type,
                            'description': row['description'],
                            'is_sensitive': True
                        }
                else:
                    configs[key] = {
                        'value': ExtendedConfig._parse_config_value(value, config_type),
                        'type': config_type,
                        'description': row['description'],
                        'is_sensitive': row['is_sensitive']
                    }

            return configs

        except Exception as e:
            logger.error(f"获取所有配置失败: {e}")
            return {}
    
    @staticmethod
    def delete_config(key: str) -> bool:
        """删除配置"""
        try:
            # 先获取旧值用于日志
            old_value = ExtendedConfig.get_config(key)
            
            query = "DELETE FROM system_config WHERE config_key = %s"
            result = get_db_manager().execute_query(query, (key,), fetch=False)
            
            if result and result > 0:
                log_config_change('system', key, 'delete', old_value, None)
                return True
            return False
            
        except Exception as e:
            logger.error(f"删除配置失败 {key}: {e}")
            return False

def log_config_change(user_id: str, config_key: str, action: str,
                     old_value: Any = None, new_value: Any = None) -> None:
    """记录配置变更日志 - 使用加密存储敏感数据"""
    try:
        # 处理敏感信息 - 使用加密而不是***
        if is_sensitive_config(config_key):
            # 对敏感数据进行加密存储
            if old_value:
                old_encrypted = ConfigEncryption.encrypt_for_audit(str(old_value))
                old_value_to_store = old_encrypted['encrypted']
                old_hash = old_encrypted['hash']
            else:
                old_value_to_store = None
                old_hash = None

            if new_value:
                new_encrypted = ConfigEncryption.encrypt_for_audit(str(new_value))
                new_value_to_store = new_encrypted['encrypted']
                new_hash = new_encrypted['hash']
            else:
                new_value_to_store = None
                new_hash = None
        else:
            # 非敏感数据直接存储
            old_value_to_store = str(old_value) if old_value else None
            new_value_to_store = str(new_value) if new_value else None
            old_hash = None
            new_hash = None

        # 扩展的审计日志查询，包含哈希字段
        query = """
        INSERT INTO config_audit_log (user_id, config_key, action, old_value, new_value, old_value_hash, new_value_hash, is_encrypted)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """

        db_manager = get_db_manager()
        if db_manager:
            db_manager.execute_query(
                query,
                (user_id, config_key, action, old_value_to_store, new_value_to_store,
                 old_hash, new_hash, is_sensitive_config(config_key)),
                fetch=False
            )

    except Exception as e:
        logger.error(f"记录配置变更日志失败: {e}")

def is_sensitive_config(config_key: str) -> bool:
    """
    判断是否为敏感配置
    敏感配置应该从环境变量读取，而不是数据库存储
    """
    sensitive_keys = ['token', 'key', 'password', 'secret', 'api_key', 'encryption']

    # 明确的敏感配置列表
    sensitive_configs = {
        'github_token',
        'ozon_api_key',
        'config_encryption_key',
        'database_password'
    }

    # 检查是否在明确列表中
    if config_key.lower() in sensitive_configs:
        return True

    # 检查是否包含敏感关键词
    return any(key in config_key.lower() for key in sensitive_keys)

def validate_github_config(token: str, repo: str, branch: str) -> bool:
    """验证GitHub配置"""
    try:
        import requests

        response = requests.get(
            f"https://api.github.com/repos/{repo}",
            headers={"Authorization": f"Bearer {token}"},
            timeout=10
        )
        return response.status_code == 200
    except Exception as e:
        logger.error(f"GitHub配置验证失败: {e}")
        return False

def validate_ozon_config(client_id: str, api_key: str) -> bool:
    """验证OZON API配置 - 基于成功示例的实现"""
    try:
        import requests

        # 使用正确的OZON API端点进行连接测试
        url = "https://api-seller.ozon.ru/v3/product/list"
        headers = {
            "Client-Id": client_id,
            "Api-Key": api_key,
            "Content-Type": "application/json"
        }

        # 使用正确的请求体格式
        payload = {
            "filter": {
                "visibility": "ALL"
            },
            "last_id": "",
            "limit": 1
        }

        logger.info(f"OZON API验证 - URL: {url}")
        logger.info(f"OZON API验证 - Client-Id: {client_id}")

        # 直接连接OZON API
        response = requests.post(url, headers=headers, json=payload, timeout=15)

        logger.info(f"OZON API验证响应: 状态码={response.status_code}")

        # 记录响应内容用于调试
        if response.status_code == 200:
            try:
                result_data = response.json()
                logger.info("OZON API配置验证成功 - 成功获取产品列表")
                logger.info(f"响应数据: {result_data.get('result', {})}")
                return True
            except Exception as parse_error:
                logger.warning(f"解析OZON API响应失败: {parse_error}")
                return True  # 状态码200表示成功，即使解析失败
        else:
            try:
                error_data = response.json()
                logger.warning(f"OZON API验证失败详情: {error_data}")

                # 检查是否是认证相关的错误
                if 'error' in error_data:
                    error_code = error_data.get('error', {}).get('code', '')
                    error_message = error_data.get('error', {}).get('message', '')

                    # 如果是认证错误
                    if 'UNAUTHORIZED' in error_code or 'unauthorized' in error_message.lower():
                        logger.error("OZON API认证失败: 无效的Client-Id或Api-Key")
                        return False
                    elif 'FORBIDDEN' in error_code or 'forbidden' in error_message.lower():
                        logger.error("OZON API权限不足: 请检查API权限设置")
                        return False

            except Exception as parse_error:
                logger.warning(f"解析OZON API响应失败: {parse_error}, 原始响应: {response.text[:200]}")

        # 只有200状态码表示成功
        is_success = response.status_code == 200

        if not is_success:
            logger.warning(f"OZON API配置验证失败: HTTP {response.status_code}")

        return is_success

    except requests.exceptions.Timeout:
        logger.error("OZON API连接超时")
        return False
    except requests.exceptions.ConnectionError:
        logger.error("OZON API连接失败，请检查网络连接")
        return False
    except Exception as e:
        logger.error(f"OZON配置验证失败: {e}")
        return False

def validate_database_config() -> bool:
    """验证数据库配置"""
    try:
        db_manager = get_db_manager()
        if db_manager:
            with db_manager.get_connection() as conn:
                return True
    except Exception as e:
        logger.error(f"数据库配置验证失败: {e}")
        return False

# 配置服务实例
config_service = ExtendedConfig()
