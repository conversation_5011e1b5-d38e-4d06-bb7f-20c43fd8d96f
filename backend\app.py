#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON商品分析Web仪表板 - Flask应用主文件
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import os
import sys
import requests
import logging
from datetime import datetime
import gzip
import functools
import time

# 添加项目路径到sys.path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入配置
from config import Config

# 配置字典 (兼容旧版)
config = {
    'development': Config,
    'production': Config,
    'testing': Config
}

# 导入蓝图
from routes.dashboard import dashboard_bp
from routes.image_manager import image_manager_bp
from routes.ozon_uploader import ozon_uploader_bp
from routes.upload_timeout import upload_timeout_bp

# 调试product_editor导入
try:
    from routes.product_editor import product_editor_bp
    print("product_editor_bp 导入成功")
except Exception as e:
    print(f"product_editor_bp 导入失败: {e}")
    product_editor_bp = None
from routes.config_manager import config_manager_bp
from routes.ozon_sync import ozon_sync_bp
from routes.price_manager import price_manager_bp
from utils.logging_config import LoggingConfig
from utils.i18n import set_language

# 设置模板和静态文件路径
template_dir = os.path.join(os.path.dirname(current_dir), 'ozon_web_dashboard', 'templates')
static_dir = os.path.join(os.path.dirname(current_dir), 'ozon_web_dashboard', 'static')

app = Flask(__name__,
           template_folder=template_dir,
           static_folder=static_dir)

# 配置CORS - 从环境变量获取允许的源
import os
frontend_url = os.environ.get('FRONTEND_URL', 'http://localhost:3000')
allowed_origins = [frontend_url]
if frontend_url != 'http://127.0.0.1:3000':
    allowed_origins.append('http://127.0.0.1:3000')

CORS(app, origins=allowed_origins,
     supports_credentials=True,
     allow_headers=['Content-Type', 'Authorization', 'Accept', 'X-Requested-With'],
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'])

# 添加响应压缩中间件
def gzip_response(f):
    """响应压缩装饰器"""
    @functools.wraps(f)
    def decorated_function(*args, **kwargs):
        response = f(*args, **kwargs)

        # 检查是否支持gzip压缩
        accept_encoding = request.headers.get('Accept-Encoding', '')
        if 'gzip' not in accept_encoding:
            return response

        # 只压缩JSON响应
        if hasattr(response, 'content_type') and 'application/json' in response.content_type:
            try:
                # 压缩响应数据
                compressed_data = gzip.compress(response.get_data())
                response.set_data(compressed_data)
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = len(compressed_data)
            except Exception as e:
                app.logger.warning(f"响应压缩失败: {e}")

        return response
    return decorated_function

# 为所有API路由添加压缩
@app.after_request
def compress_response(response):
    """自动压缩API响应"""
    # 只压缩大于1KB的JSON响应
    if (response.content_type and 'application/json' in response.content_type and
        len(response.get_data()) > 1024):

        accept_encoding = request.headers.get('Accept-Encoding', '')
        if 'gzip' in accept_encoding:
            try:
                compressed_data = gzip.compress(response.get_data())
                response.set_data(compressed_data)
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = len(compressed_data)
                response.headers['Vary'] = 'Accept-Encoding'
            except Exception as e:
                app.logger.warning(f"响应压缩失败: {e}")

    return response

# 添加性能监控
@app.before_request
def before_request():
    """请求开始时记录时间"""
    request.start_time = time.time()

@app.after_request
def after_request(response):
    """请求结束时记录性能数据"""
    if hasattr(request, 'start_time'):
        duration = time.time() - request.start_time

        # 记录慢查询（超过1秒）
        if duration > 1.0:
            app.logger.warning(f"慢查询警告: {request.method} {request.path} - {duration:.3f}s")

        # 添加性能头部
        response.headers['X-Response-Time'] = f"{duration:.3f}s"

    return response

# 配置
env = os.environ.get('FLASK_ENV', 'development')
app.config.from_object(config[env])

# 配置日志
def setup_logging(app):
    """配置应用日志 - 使用优化的日志管理系统"""
    # 创建日志配置管理器
    logging_config = LoggingConfig(current_dir)

    # 设置完整的日志配置
    log_setup = logging_config.setup_complete_logging(app)

    return log_setup

# 设置日志
setup_logging(app)

# **重要**: 确保控制台日志可见
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
console_handler.setFormatter(formatter)

# 确保根日志器有控制台处理器
root_logger = logging.getLogger()
if not any(isinstance(h, logging.StreamHandler) for h in root_logger.handlers):
    root_logger.addHandler(console_handler)
    root_logger.setLevel(logging.INFO)

# 注册蓝图
app.register_blueprint(dashboard_bp)
app.register_blueprint(image_manager_bp, url_prefix='/image-manager')
app.register_blueprint(ozon_uploader_bp, url_prefix='/ozon-uploader')

# 注册product_editor蓝图
if product_editor_bp:
    app.register_blueprint(product_editor_bp, url_prefix='/product-editor')
    print("product_editor_bp 蓝图注册成功")


else:
    print("product_editor_bp 蓝图注册失败 - 蓝图为None")
app.register_blueprint(config_manager_bp, url_prefix='/config-manager')
app.register_blueprint(ozon_sync_bp, url_prefix='/ozon-sync')
app.register_blueprint(price_manager_bp, url_prefix='/price-manager')
app.register_blueprint(upload_timeout_bp)

# 国际化API端点
@app.route('/api/translations/<lang>')
def get_translations(lang):
    """获取指定语言的翻译数据"""
    try:
        import json
        translation_file = os.path.join(current_dir, 'translations', f'{lang}.json')
        if os.path.exists(translation_file):
            with open(translation_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
            return jsonify({
                'success': True,
                'language': lang,
                'translations': translations
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Translation file for language "{lang}" not found'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/translations/current')
def get_current_translations():
    """获取当前语言的翻译数据"""
    try:
        import json
        from utils.i18n import get_current_language

        # 获取当前会话中的语言设置
        lang = get_current_language()
        translation_file = os.path.join(current_dir, 'translations', f'{lang}.json')
        if os.path.exists(translation_file):
            with open(translation_file, 'r', encoding='utf-8') as f:
                translations = json.load(f)
            return jsonify({
                'success': True,
                'language': lang,
                'translations': translations
            })
        else:
            return jsonify({
                'success': False,
                'error': f'Translation file for language "{lang}" not found'
            }), 404
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/performance')
def api_system_performance():
    """获取系统性能统计"""
    try:
        from models.database import get_db_manager
        from utils.cache import get_cache

        # 获取数据库性能统计
        db_stats = get_db_manager().get_performance_stats()

        # 获取缓存统计
        cache = get_cache()
        cache_stats = cache.get_stats()

        # 获取系统信息
        import psutil
        system_stats = {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'disk_usage': psutil.disk_usage('/').percent if os.name != 'nt' else psutil.disk_usage('C:').percent
        }

        return jsonify({
            'success': True,
            'data': {
                'database': db_stats,
                'cache': cache_stats,
                'system': system_stats,
                'timestamp': datetime.now().isoformat()
            }
        })

    except ImportError:
        # psutil 不可用时的降级版本
        from models.database import get_db_manager
        from utils.cache import get_cache

        db_stats = get_db_manager().get_performance_stats()
        cache = get_cache()
        cache_stats = cache.get_stats()

        return jsonify({
            'success': True,
            'data': {
                'database': db_stats,
                'cache': cache_stats,
                'system': {'note': 'psutil not available'},
                'timestamp': datetime.now().isoformat()
            }
        })
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/cache/clear', methods=['POST'])
def api_clear_cache():
    """清理缓存"""
    try:
        from utils.cache import get_cache, invalidate_cache_pattern

        data = request.get_json() or {}
        pattern = data.get('pattern', '')

        cache = get_cache()

        if pattern:
            # 清理匹配模式的缓存
            deleted_count = invalidate_cache_pattern(pattern)
            message = f"清理了 {deleted_count} 个匹配模式 '{pattern}' 的缓存项"
        else:
            # 清理所有缓存
            cache.clear()
            message = "已清理所有缓存"

        return jsonify({
            'success': True,
            'message': message
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/system/cache/cleanup', methods=['POST'])
def api_cleanup_expired_cache():
    """清理过期缓存"""
    try:
        from utils.cache import get_cache

        cache = get_cache()
        deleted_count = cache.cleanup_expired()

        return jsonify({
            'success': True,
            'message': f"清理了 {deleted_count} 个过期缓存项"
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

# 语言切换路由
@app.route('/set-language/<lang_code>')
def set_app_language(lang_code):
    """设置应用语言 (GET方式)"""
    if set_language(lang_code):
        return jsonify({'success': True, 'language': lang_code})
    else:
        return jsonify({'success': False, 'error': 'Unsupported language'}), 400

@app.route('/set-language', methods=['POST'])
def set_app_language_post():
    """设置应用语言 (POST方式)"""
    try:
        data = request.get_json()
        if not data or 'language' not in data:
            return jsonify({'success': False, 'error': 'Language parameter required'}), 400

        lang_code = data['language']
        if set_language(lang_code):
            return jsonify({'success': True, 'language': lang_code})
        else:
            return jsonify({'success': False, 'error': 'Unsupported language'}), 400
    except Exception as e:
        return jsonify({'success': False, 'error': str(e)}), 500

# 全局变量
exchange_rate = 13.5  # 默认汇率
last_rate_update = None

def fetch_exchange_rate():
    """获取实时汇率"""
    global exchange_rate, last_rate_update
    
    try:
        # 尝试从多个API获取汇率
        apis = [
            'https://api.exchangerate-api.com/v4/latest/CNY',
            'https://api.fixer.io/latest?base=CNY&symbols=RUB',
        ]
        
        for api_url in apis:
            try:
                response = requests.get(api_url, timeout=5)
                if response.status_code == 200:
                    data = response.json()
                    if 'rates' in data and 'RUB' in data['rates']:
                        exchange_rate = float(data['rates']['RUB'])
                        last_rate_update = datetime.now()
                        print(f"汇率更新成功: 1 CNY = {exchange_rate:.4f} RUB")
                        return True
            except Exception as e:
                print(f"API {api_url} 失败: {e}")
                continue

        print("所有汇率API都失败，使用默认汇率")
        return False
        
    except Exception as e:
        print(f"获取汇率失败: {e}")
        return False



@app.route('/api/exchange-rate')
def api_exchange_rate():
    """获取汇率API"""
    global exchange_rate, last_rate_update

    # 如果汇率超过1小时未更新，尝试重新获取
    if last_rate_update is None or (datetime.now() - last_rate_update).seconds > 3600:
        fetch_exchange_rate()

    return jsonify({
        'rate': exchange_rate,
        'last_update': last_rate_update.isoformat() if last_rate_update else None,
        'is_real_time': exchange_rate != 13.5
    })

@app.route('/api/log-error', methods=['POST'])
def api_log_error():
    """前端错误日志记录API"""
    try:
        error_data = request.get_json()

        # 记录错误到日志文件
        app.logger.error(f"前端错误: {error_data.get('message', 'Unknown error')}")
        app.logger.error(f"错误堆栈: {error_data.get('stack', 'No stack trace')}")
        app.logger.error(f"用户代理: {error_data.get('userAgent', 'Unknown')}")
        app.logger.error(f"页面URL: {error_data.get('url', 'Unknown')}")
        app.logger.error(f"时间戳: {error_data.get('timestamp', 'Unknown')}")

        # 可以在这里添加更多的错误处理逻辑
        # 比如发送到错误监控服务、数据库记录等

        return jsonify({
            'success': True,
            'message': '错误已记录'
        })

    except Exception as e:
        app.logger.error(f"记录前端错误失败: {e}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/debug/routes')
def debug_routes():
    """调试路由 - 显示所有注册的路由"""
    routes = []
    for rule in app.url_map.iter_rules():
        routes.append({
            'endpoint': rule.endpoint,
            'methods': list(rule.methods),
            'rule': rule.rule
        })
    return jsonify(routes)

# 临时添加审计日志API到主应用
@app.route('/config-manager/api/audit-logs', methods=['GET'])
def api_get_audit_logs():
    """获取配置审计日志API"""
    try:
        from services.config_service import ConfigEncryption
        from models.database import get_db_manager

        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        include_encrypted = request.args.get('include_encrypted', 'false').lower() == 'true'

        # 计算偏移量
        offset = (page - 1) * limit

        # 查询总数
        count_query = "SELECT COUNT(*) as total FROM config_audit_log"

        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        count_result = db_manager.execute_query(count_query)
        total = count_result[0]['total'] if count_result else 0

        # 查询数据
        data_query = """
        SELECT id, user_id, config_key, action, old_value, new_value,
               old_value_hash, new_value_hash, is_encrypted, timestamp
        FROM config_audit_log
        ORDER BY timestamp DESC
        LIMIT %s OFFSET %s
        """

        results = db_manager.execute_query(data_query, [limit, offset])

        # 处理结果
        logs = []
        for record in results:
            log_entry = {
                'id': record['id'],
                'user_id': record['user_id'],
                'config_key': record['config_key'],
                'action': record['action'],
                'timestamp': record['timestamp'].isoformat() if record['timestamp'] else None,
                'is_encrypted': bool(record['is_encrypted'])
            }

            # 处理敏感数据
            if record['is_encrypted'] and include_encrypted:
                # 管理员权限：解密显示
                try:
                    log_entry['old_value'] = ConfigEncryption.decrypt_for_audit(record['old_value']) if record['old_value'] else None
                    log_entry['new_value'] = ConfigEncryption.decrypt_for_audit(record['new_value']) if record['new_value'] else None
                    log_entry['old_value_hash'] = record['old_value_hash']
                    log_entry['new_value_hash'] = record['new_value_hash']
                except Exception as e:
                    app.logger.error(f"解密审计日志失败: {e}")
                    log_entry['old_value'] = "[解密失败]"
                    log_entry['new_value'] = "[解密失败]"
            elif record['is_encrypted']:
                # 普通权限：显示加密状态
                log_entry['old_value'] = "[已加密]" if record['old_value'] else None
                log_entry['new_value'] = "[已加密]" if record['new_value'] else None
                log_entry['old_value_hash'] = record['old_value_hash']
                log_entry['new_value_hash'] = record['new_value_hash']
            else:
                # 非敏感数据：直接显示
                log_entry['old_value'] = record['old_value']
                log_entry['new_value'] = record['new_value']
                log_entry['old_value_hash'] = record['old_value_hash']
                log_entry['new_value_hash'] = record['new_value_hash']

            logs.append(log_entry)

        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'pages': (total + limit - 1) // limit
                }
            }
        })

    except Exception as e:
        app.logger.error(f"获取审计日志失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取审计日志失败: {str(e)}'
        }), 500

if __name__ == '__main__':
    import sys
    print("启动OZON商品分析Web仪表板...", flush=True)
    sys.stdout.flush()

    try:
        print("� 初始化数据库连接...", flush=True)
        from models.database import get_db_manager
        db = get_db_manager()
        print("数据库管理器初始化完成", flush=True)
    except Exception as e:
        print(f"数据库连接失败，使用模拟模式: {e}", flush=True)

    print("初始化汇率...", flush=True)
    fetch_exchange_rate()

    # 启动OZON同步调度器
    print("启动OZON同步调度器...", flush=True)
    try:
        from services.ozon_sync_scheduler import ozon_sync_scheduler
        ozon_sync_scheduler.start_scheduler()
        print("OZON同步调度器启动成功", flush=True)
    except Exception as e:
        print(f"OZON同步调度器启动失败: {e}", flush=True)

    print("启动Flask应用服务器...", flush=True)
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True,
        use_reloader=True  # 临时禁用自动重载，避免watchdog兼容性问题
    )
    
