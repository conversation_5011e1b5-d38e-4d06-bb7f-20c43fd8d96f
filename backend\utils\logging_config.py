"""
日志配置工具模块
提供统一的日志配置和管理功能
"""

import os
import logging
from logging.handlers import RotatingFileHandler
from datetime import datetime
import glob


class LoggingConfig:
    """日志配置管理类"""
    
    def __init__(self, base_dir):
        self.base_dir = base_dir
        self.logs_dir = os.path.join(base_dir, 'logs')
        self.max_bytes = 10 * 1024 * 1024  # 10MB
        self.backup_count = 5  # 保留最近5个日志文件
        
        # 确保日志目录存在
        if not os.path.exists(self.logs_dir):
            os.makedirs(self.logs_dir)
    
    def setup_app_logging(self, app):
        """配置Flask应用日志"""
        # 检查是否已经配置过日志（避免重复配置）
        if hasattr(app.logger, '_logging_configured'):
            return None

        # 清理Flask应用日志器的现有处理器
        app.logger.handlers.clear()

        # **重要**: 保持传播开启，让消息能到达根日志器和控制台
        app.logger.propagate = True

        # 配置应用日志文件处理器
        app_log_handler = RotatingFileHandler(
            os.path.join(self.logs_dir, 'app.log'),
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )

        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        app_log_handler.setFormatter(formatter)

        # 根据环境设置日志级别
        if app.debug:
            app_log_handler.setLevel(logging.DEBUG)
            app.logger.setLevel(logging.DEBUG)
        else:
            app_log_handler.setLevel(logging.INFO)
            app.logger.setLevel(logging.INFO)

        app.logger.addHandler(app_log_handler)

        # **重要修复**: 始终配置控制台输出，确保实时日志可见
        root_logger = logging.getLogger()

        # 检查根日志器是否已经有控制台处理器，避免重复添加
        has_console_handler = any(
            isinstance(handler, logging.StreamHandler) and handler.stream.name == '<stdout>'
            for handler in root_logger.handlers
        )

        if not has_console_handler:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(formatter)

            # 设置控制台日志级别
            if app.debug:
                console_handler.setLevel(logging.DEBUG)
            else:
                console_handler.setLevel(logging.INFO)

            # 添加到根日志器，确保所有日志都能显示在控制台
            root_logger.addHandler(console_handler)
            root_logger.setLevel(logging.INFO)

        # 标记已配置
        app.logger._logging_configured = True

        return app_log_handler
    
    def setup_database_logging(self):
        """配置数据库日志"""
        db_log_handler = RotatingFileHandler(
            os.path.join(self.logs_dir, 'database.log'),
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        db_log_handler.setFormatter(formatter)
        db_log_handler.setLevel(logging.INFO)
        
        # 为数据库相关的日志创建单独的logger
        db_logger = logging.getLogger('database')
        db_logger.setLevel(logging.INFO)
        db_logger.propagate = True  # **修复**: 启用传播，让消息显示在控制台

        # 避免重复添加处理器
        if not db_logger.handlers:
            db_logger.addHandler(db_log_handler)

        return db_logger
    
    def setup_service_logging(self, service_name):
        """为特定服务配置日志"""
        service_log_handler = RotatingFileHandler(
            os.path.join(self.logs_dir, f'{service_name}.log'),
            maxBytes=self.max_bytes,
            backupCount=self.backup_count,
            encoding='utf-8'
        )
        
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        service_log_handler.setFormatter(formatter)
        service_log_handler.setLevel(logging.INFO)
        
        # 创建服务专用logger
        service_logger = logging.getLogger(service_name)
        service_logger.setLevel(logging.INFO)
        service_logger.propagate = True  # **修复**: 启用传播，让消息显示在控制台

        # 避免重复添加处理器
        if not service_logger.handlers:
            service_logger.addHandler(service_log_handler)

        return service_logger
    
    def configure_third_party_loggers(self):
        """配置第三方库的日志级别"""
        # 配置第三方库日志级别，但保持werkzeug的传播以显示HTTP请求
        third_party_loggers = ['werkzeug', 'urllib3', 'requests', 'PIL', 'matplotlib']

        for logger_name in third_party_loggers:
            logger = logging.getLogger(logger_name)
            if logger_name == 'werkzeug':
                logger.setLevel(logging.INFO)
                logger.propagate = True  # 保持werkzeug日志传播，显示HTTP请求
            else:
                logger.setLevel(logging.WARNING)
                logger.propagate = False  # 其他第三方库禁用传播
    
    def cleanup_old_logs(self, days_to_keep=30):
        """清理超过指定天数的旧日志文件"""
        try:
            current_time = datetime.now()
            log_files = glob.glob(os.path.join(self.logs_dir, '*.log*'))
            
            cleaned_count = 0
            for log_file in log_files:
                # 获取文件修改时间
                file_time = datetime.fromtimestamp(os.path.getmtime(log_file))
                days_old = (current_time - file_time).days
                
                # 删除超过指定天数的文件
                if days_old > days_to_keep:
                    try:
                        os.remove(log_file)
                        cleaned_count += 1
                        print(f"已清理旧日志文件: {log_file}")
                    except OSError as e:
                        print(f"清理日志文件失败 {log_file}: {e}")
            
            if cleaned_count > 0:
                print(f"日志清理完成，共清理 {cleaned_count} 个文件")
            
        except Exception as e:
            print(f"日志清理过程中发生错误: {e}")
    
    def get_log_info(self):
        """获取日志文件信息"""
        log_info = {
            'logs_directory': self.logs_dir,
            'max_file_size': f"{self.max_bytes / (1024 * 1024):.1f}MB",
            'backup_count': self.backup_count,
            'log_files': []
        }
        
        try:
            log_files = glob.glob(os.path.join(self.logs_dir, '*.log*'))
            for log_file in sorted(log_files):
                file_stat = os.stat(log_file)
                log_info['log_files'].append({
                    'name': os.path.basename(log_file),
                    'size': f"{file_stat.st_size / (1024 * 1024):.2f}MB",
                    'modified': datetime.fromtimestamp(file_stat.st_mtime).strftime('%Y-%m-%d %H:%M:%S')
                })
        except Exception as e:
            log_info['error'] = str(e)
        
        return log_info
    
    def reset_all_logging(self):
        """重置所有日志配置，清除重复处理器"""
        # 获取所有现有的日志器
        loggers_to_reset = [logging.getLogger(name) for name in logging.Logger.manager.loggerDict]
        loggers_to_reset.append(logging.getLogger())  # 添加根日志器

        for logger in loggers_to_reset:
            # 清除所有处理器
            logger.handlers.clear()
            # 禁用传播（除了根日志器）
            if logger != logging.getLogger():
                logger.propagate = False

    def setup_complete_logging(self, app):
        """完整的日志配置设置"""
        # 检查是否已经完全配置过（避免重复初始化）
        if hasattr(app, '_complete_logging_configured'):
            return {
                'app_handler': None,
                'db_logger': None,
                'logs_dir': self.logs_dir
            }

        # 配置应用日志
        app_handler = self.setup_app_logging(app)

        # 配置数据库日志
        db_logger = self.setup_database_logging()

        # 配置第三方库日志
        self.configure_third_party_loggers()

        # 只在首次配置时记录日志配置信息
        if app_handler:  # 只有在实际配置了处理器时才记录
            app.logger.info('OZON商品分析Web仪表板日志系统初始化完成')
            app.logger.info(f'日志文件位置: {self.logs_dir}')
            app.logger.info(f'日志轮询配置: 最大{self.max_bytes/(1024*1024):.0f}MB, 保留{self.backup_count}个文件')

            # 执行日志清理
            self.cleanup_old_logs()

        # 标记完整配置已完成
        app._complete_logging_configured = True

        return {
            'app_handler': app_handler,
            'db_logger': db_logger,
            'logs_dir': self.logs_dir
        }


def get_logger(name):
    """获取指定名称的logger"""
    return logging.getLogger(name)


def log_function_call(func):
    """装饰器：记录函数调用"""
    def wrapper(*args, **kwargs):
        logger = logging.getLogger(func.__module__)
        logger.debug(f"调用函数: {func.__name__}")
        try:
            result = func(*args, **kwargs)
            logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            logger.error(f"函数 {func.__name__} 执行失败: {e}")
            raise
    return wrapper
