#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目清理脚本 - 清理后端临时文件和缓存
使用方法：python scripts/clean_project.py
"""

import os
import shutil
import logging
from pathlib import Path

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def clean_python_cache():
    """清理Python缓存文件"""
    logger.info("🧹 清理Python缓存文件...")
    
    # 查找并删除所有.pyc文件
    pyc_count = 0
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.pyc'):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    pyc_count += 1
                    logger.debug(f"删除: {file_path}")
                except Exception as e:
                    logger.error(f"删除失败 {file_path}: {e}")
    
    # 删除空的__pycache__目录
    pycache_count = 0
    for root, dirs, files in os.walk('.', topdown=False):
        for dir_name in dirs:
            if dir_name == '__pycache__':
                dir_path = os.path.join(root, dir_name)
                try:
                    if not os.listdir(dir_path):  # 只删除空目录
                        os.rmdir(dir_path)
                        pycache_count += 1
                        logger.debug(f"删除空目录: {dir_path}")
                except Exception as e:
                    logger.error(f"删除目录失败 {dir_path}: {e}")
    
    logger.info(f"✅ 清理完成: {pyc_count} 个.pyc文件, {pycache_count} 个__pycache__目录")

def clean_log_files():
    """清理日志文件"""
    logger.info("🧹 清理日志文件...")
    
    log_files = [
        'logs/app.log',
        'logs/database.log'
    ]
    
    cleaned_count = 0
    for log_file in log_files:
        if os.path.exists(log_file):
            try:
                os.remove(log_file)
                cleaned_count += 1
                logger.info(f"删除: {log_file}")
            except Exception as e:
                logger.error(f"删除失败 {log_file}: {e}")
    
    logger.info(f"✅ 清理完成: {cleaned_count} 个日志文件")

def clean_temp_files():
    """清理临时文件"""
    logger.info("🧹 清理临时文件...")
    
    temp_patterns = [
        '*.tmp',
        '*.temp',
        '*.bak',
        '*.backup',
        '*.old'
    ]
    
    cleaned_count = 0
    for root, dirs, files in os.walk('.'):
        for file in files:
            for pattern in temp_patterns:
                if file.endswith(pattern.replace('*', '')):
                    file_path = os.path.join(root, file)
                    try:
                        os.remove(file_path)
                        cleaned_count += 1
                        logger.info(f"删除临时文件: {file_path}")
                    except Exception as e:
                        logger.error(f"删除失败 {file_path}: {e}")
    
    logger.info(f"✅ 清理完成: {cleaned_count} 个临时文件")

def show_project_size():
    """显示项目大小"""
    logger.info("📊 计算项目大小...")
    
    total_size = 0
    file_count = 0
    
    for root, dirs, files in os.walk('.'):
        for file in files:
            file_path = os.path.join(root, file)
            try:
                size = os.path.getsize(file_path)
                total_size += size
                file_count += 1
            except Exception:
                pass
    
    # 转换为人类可读的格式
    if total_size < 1024:
        size_str = f"{total_size} B"
    elif total_size < 1024 * 1024:
        size_str = f"{total_size / 1024:.1f} KB"
    elif total_size < 1024 * 1024 * 1024:
        size_str = f"{total_size / (1024 * 1024):.1f} MB"
    else:
        size_str = f"{total_size / (1024 * 1024 * 1024):.1f} GB"
    
    logger.info(f"📊 项目统计: {file_count} 个文件, 总大小: {size_str}")

def main():
    """主函数"""
    logger.info("🚀 开始清理后端项目...")
    logger.info("=" * 50)
    
    # 确保在正确的目录
    if not os.path.exists('app.py'):
        logger.error("❌ 请在后端根目录运行此脚本")
        return False
    
    try:
        # 执行清理操作
        clean_python_cache()
        clean_log_files()
        clean_temp_files()
        
        # 显示项目统计
        show_project_size()
        
        logger.info("=" * 50)
        logger.info("🎉 后端项目清理完成！")
        logger.info("💡 提示: 现在可以安全地提交到git仓库了")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ 清理过程中出现错误: {e}")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
