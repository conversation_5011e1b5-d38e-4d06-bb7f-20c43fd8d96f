# OZON Dashboard 环境变量配置文件模板
# 复制此文件为 .env 并填入实际值

# ===========================================
# 基础应用配置
# ===========================================
FLASK_ENV=development
SECRET_KEY=ozon-dashboard-secret-key-2024

# ===========================================
# 数据库配置
# ===========================================
DATABASE_HOST=mysql2.sqlpub.com
DATABASE_PORT=3307
DATABASE_USER=zd_models
DATABASE_PASSWORD=your_database_password_here
DATABASE_NAME=model_database

# ===========================================
# 敏感API密钥配置 (必须填写)
# ===========================================

# GitHub 配置 - 用于图片上传到GitHub图床
GITHUB_TOKEN=your_github_personal_access_token_here
GITHUB_REPO=righBai/images
GITHUB_BRANCH=main
GITHUB_FOLDER=images

# OZON API 配置 - 用于商品上传到OZON平台
OZON_CLIENT_ID=2962343
OZON_API_KEY=your_ozon_api_key_here

# ===========================================
# 配置加密密钥 (可选，系统会自动生成)
# ===========================================
CONFIG_ENCRYPTION_KEY=your_encryption_key_here

# ===========================================
# 其他配置
# ===========================================
# 日志级别
LOG_LEVEL=INFO

# 前端URL配置 - 用于CORS设置
FRONTEND_URL=http://localhost:3000

# Redis配置 (如果使用)
# REDIS_URL=redis://localhost:6379/0

# 文件上传配置
MAX_CONTENT_LENGTH=16777216
