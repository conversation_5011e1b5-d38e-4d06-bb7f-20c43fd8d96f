"""
OZON商品同步调度服务
提供定时同步、增量同步、数据清理等功能
"""

import threading
import time
import schedule
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional
from services.ozon_api_service import OzonApiService
from services.ozon_analytics_service import ozon_analytics_service
from models.database import get_db_manager

logger = logging.getLogger(__name__)

class OzonSyncScheduler:
    """OZON同步调度器"""
    
    def __init__(self):
        self.db = get_db_manager()
        self.ozon_api_service = OzonApiService()
        self.is_running = False
        self.scheduler_thread = None
        self.sync_lock = threading.Lock()
        
        # 配置参数
        self.full_sync_interval_hours = 24  # 全量同步间隔（小时）
        self.incremental_sync_interval_hours = 6  # 增量同步间隔（小时）
        self.cleanup_interval_days = 7  # 日志清理间隔（天）
        self.max_log_age_days = 30  # 日志保留天数
        
    def start_scheduler(self):
        """启动调度器"""
        if self.is_running:
            logger.warning("同步调度器已在运行中")
            return
            
        logger.info("启动OZON同步调度器")
        
        # 配置定时任务
        schedule.clear()
        
        # 每天凌晨2点执行全量同步
        schedule.every().day.at("02:00").do(self._scheduled_full_sync)
        
        # 每6小时执行增量同步
        schedule.every(self.incremental_sync_interval_hours).hours.do(self._scheduled_incremental_sync)
        
        # 每周日凌晨1点清理旧日志
        schedule.every().sunday.at("01:00").do(self._scheduled_cleanup)

        # 每天凌晨5点同步昨天的OZON数据分析数据
        #schedule.every().day.at("05:00").do(self._scheduled_analytics_sync)
        
        # 启动调度线程
        self.is_running = True
        self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
        self.scheduler_thread.start()
        
        logger.info("OZON同步调度器启动成功")
        
    def stop_scheduler(self):
        """停止调度器"""
        if not self.is_running:
            return
            
        logger.info("停止OZON同步调度器")
        self.is_running = False
        
        if self.scheduler_thread:
            self.scheduler_thread.join(timeout=5)
            
        schedule.clear()
        logger.info("OZON同步调度器已停止")
        
    def _run_scheduler(self):
        """调度器主循环"""
        while self.is_running:
            try:
                schedule.run_pending()
                time.sleep(60)  # 每分钟检查一次
            except Exception as e:
                logger.error(f"调度器运行异常: {e}")
                time.sleep(300)  # 出错后等待5分钟再继续
                
    def _scheduled_full_sync(self):
        """定时全量同步"""
        logger.info("开始定时全量同步")
        
        with self.sync_lock:
            try:
                # 检查是否有正在运行的同步任务
                if self._is_sync_running():
                    logger.warning("已有同步任务在运行，跳过本次全量同步")
                    return
                    
                # 执行全量同步
                stats = self.ozon_api_service.sync_all_products(max_pages=20)
                
                logger.info(f"定时全量同步完成: {stats}")
                
                # 记录同步结果
                self._record_sync_result('scheduled_full', stats)
                
            except Exception as e:
                logger.error(f"定时全量同步失败: {e}")
                self._record_sync_error('scheduled_full', str(e))
                
    def _scheduled_incremental_sync(self):
        """定时增量同步"""
        logger.info("开始定时增量同步")
        
        with self.sync_lock:
            try:
                # 检查是否有正在运行的同步任务
                if self._is_sync_running():
                    logger.warning("已有同步任务在运行，跳过本次增量同步")
                    return
                    
                # 获取最后同步时间
                last_sync_time = self._get_last_sync_time()
                
                # 如果距离上次同步不足设定间隔，跳过
                if last_sync_time:
                    time_diff = datetime.now() - last_sync_time
                    if time_diff.total_seconds() < self.incremental_sync_interval_hours * 3600:
                        logger.info(f"距离上次同步仅{time_diff}，跳过本次增量同步")
                        return
                
                # 执行增量同步（较少页数）
                stats = self.ozon_api_service.sync_all_products(max_pages=5)
                
                logger.info(f"定时增量同步完成: {stats}")
                
                # 记录同步结果
                self._record_sync_result('scheduled_incremental', stats)
                
            except Exception as e:
                logger.error(f"定时增量同步失败: {e}")
                self._record_sync_error('scheduled_incremental', str(e))

    def _scheduled_analytics_sync(self):
        """定时OZON数据分析同步"""
        logger.info("开始定时OZON数据分析同步")

        with self.sync_lock:
            try:
                # 计算昨天的日期
                yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

                # 定义要同步的指标（包含hits_tocart）
                metrics = [
                    'hits_view_search', 'hits_view_pdp', 'hits_view',
                    'hits_tocart_search', 'hits_tocart_pdp', 'hits_tocart', 'conv_tocart',
                    'revenue', 'ordered_units',
                    'delivered_units', 'returns', 'cancellations'
                ]

                logger.info(f"同步昨天的数据分析数据: {yesterday}")

                # 调用数据分析服务同步昨天的数据
                result = ozon_analytics_service.sync_daily_analytics_data(
                    target_date=yesterday,
                    metrics=metrics,
                    dimension='sku'
                )

                if result.get('success'):
                    inserted = result.get('inserted_count', 0)
                    skipped = result.get('skipped_count', 0)
                    errors = result.get('error_count', 0)

                    logger.info(f"定时数据分析同步完成: 插入 {inserted} 条, 跳过 {skipped} 条, 错误 {errors} 条")

                    # 记录同步结果
                    self._record_sync_result('scheduled_analytics', {
                        'target_date': yesterday,
                        'inserted_count': inserted,
                        'skipped_count': skipped,
                        'error_count': errors,
                        'total_processed': inserted + skipped + errors
                    })
                else:
                    error_msg = result.get('error', 'Unknown error')
                    logger.error(f"定时数据分析同步失败: {error_msg}")
                    self._record_sync_error('scheduled_analytics', error_msg)

            except Exception as e:
                logger.error(f"定时数据分析同步异常: {e}")
                self._record_sync_error('scheduled_analytics', str(e))
                
    def _scheduled_cleanup(self):
        """定时清理旧数据"""
        logger.info("开始定时数据清理")
        
        try:
            # 清理旧的同步日志
            cutoff_date = datetime.now() - timedelta(days=self.max_log_age_days)
            
            deleted_count = self.db.execute_query("""
                DELETE FROM ozon_api_sync_log 
                WHERE started_at < %s
            """, (cutoff_date,))
            
            logger.info(f"清理了 {deleted_count} 条旧同步日志")
            
            # 清理失败的同步记录（超过7天的失败记录）
            failed_cutoff = datetime.now() - timedelta(days=7)
            
            failed_deleted = self.db.execute_query("""
                UPDATE ozon_products 
                SET api_sync_status = 'pending', sync_retry_count = 0
                WHERE api_sync_status = 'failed' AND updated_at < %s
            """, (failed_cutoff,))
            
            logger.info(f"重置了 {failed_deleted} 个失败的同步记录")
            
        except Exception as e:
            logger.error(f"定时数据清理失败: {e}")
            
    def _is_sync_running(self) -> bool:
        """检查是否有同步任务在运行"""
        try:
            running_tasks = self.db.execute_query("""
                SELECT COUNT(*) as count FROM ozon_api_sync_log 
                WHERE sync_status = 'running' 
                AND started_at > DATE_SUB(NOW(), INTERVAL 2 HOUR)
            """)
            
            return running_tasks and running_tasks[0]['count'] > 0
            
        except Exception as e:
            logger.error(f"检查同步状态失败: {e}")
            return False
            
    def _get_last_sync_time(self) -> Optional[datetime]:
        """获取最后同步时间"""
        try:
            result = self.db.execute_query("""
                SELECT MAX(completed_at) as last_sync 
                FROM ozon_api_sync_log 
                WHERE sync_status = 'success'
            """)
            
            if result and result[0]['last_sync']:
                return result[0]['last_sync']
                
        except Exception as e:
            logger.error(f"获取最后同步时间失败: {e}")
            
        return None
        
    def _record_sync_result(self, sync_type: str, stats: Dict):
        """记录同步结果"""
        try:
            self.db.execute_query("""
                INSERT INTO ozon_api_sync_log (
                    sync_task_id, api_endpoint, sync_type, sync_status,
                    total_requested, total_processed, total_failed,
                    started_at, completed_at, duration_seconds
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, NOW(), NOW(), 0
                )
            """, (
                f"scheduled_{int(time.time())}",
                "scheduler",
                sync_type,
                "success",
                stats.get('total_list_products', 0),
                stats.get('saved_list_count', 0) + stats.get('saved_detail_count', 0),
                stats.get('errors', 0)
            ))
            
        except Exception as e:
            logger.error(f"记录同步结果失败: {e}")
            
    def _record_sync_error(self, sync_type: str, error_message: str):
        """记录同步错误"""
        try:
            self.db.execute_query("""
                INSERT INTO ozon_api_sync_log (
                    sync_task_id, api_endpoint, sync_type, sync_status,
                    error_message, started_at, completed_at, duration_seconds
                ) VALUES (
                    %s, %s, %s, %s, %s, NOW(), NOW(), 0
                )
            """, (
                f"scheduled_error_{int(time.time())}",
                "scheduler",
                sync_type,
                "failed",
                error_message
            ))
            
        except Exception as e:
            logger.error(f"记录同步错误失败: {e}")
            
    def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        return {
            'is_running': self.is_running,
            'full_sync_interval_hours': self.full_sync_interval_hours,
            'incremental_sync_interval_hours': self.incremental_sync_interval_hours,
            'cleanup_interval_days': self.cleanup_interval_days,
            'max_log_age_days': self.max_log_age_days,
            'next_jobs': [str(job) for job in schedule.jobs] if self.is_running else []
        }
        
    def trigger_manual_sync(self, sync_type: str = 'full', max_pages: int = 10) -> Dict:
        """手动触发同步"""
        logger.info(f"手动触发同步: {sync_type}")
        
        with self.sync_lock:
            try:
                # 检查是否有正在运行的同步任务
                if self._is_sync_running():
                    return {
                        'success': False,
                        'error': '已有同步任务在运行中，请稍后再试'
                    }
                
                # 执行同步
                stats = self.ozon_api_service.sync_all_products(max_pages=max_pages)
                
                # 记录同步结果
                self._record_sync_result(f'manual_{sync_type}', stats)
                
                return {
                    'success': True,
                    'stats': stats,
                    'message': f'手动{sync_type}同步完成'
                }
                
            except Exception as e:
                error_msg = f"手动同步失败: {e}"
                logger.error(error_msg)
                self._record_sync_error(f'manual_{sync_type}', str(e))
                
                return {
                    'success': False,
                    'error': error_msg
                }

    def trigger_analytics_sync(self, target_date: Optional[str] = None) -> Dict:
        """手动触发数据分析同步"""
        if not target_date:
            # 默认同步昨天的数据
            target_date = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')

        logger.info(f"手动触发数据分析同步: {target_date}")

        with self.sync_lock:
            try:
                # 定义要同步的指标（包含hits_tocart）
                metrics = [
                    'hits_view_search', 'hits_view_pdp', 'hits_view',
                    'hits_tocart_search', 'hits_tocart_pdp', 'hits_tocart', 'conv_tocart',
                    'revenue', 'ordered_units',
                    'delivered_units', 'returns', 'cancellations'
                ]

                # 调用数据分析服务
                result = ozon_analytics_service.sync_daily_analytics_data(
                    target_date=target_date,
                    metrics=metrics,
                    dimension='sku'
                )

                if result.get('success'):
                    inserted = result.get('inserted_count', 0)
                    skipped = result.get('skipped_count', 0)
                    errors = result.get('error_count', 0)

                    # 记录同步结果
                    self._record_sync_result('manual_analytics', {
                        'target_date': target_date,
                        'inserted_count': inserted,
                        'skipped_count': skipped,
                        'error_count': errors,
                        'total_processed': inserted + skipped + errors
                    })

                    return {
                        'success': True,
                        'target_date': target_date,
                        'inserted_count': inserted,
                        'skipped_count': skipped,
                        'error_count': errors,
                        'message': f'数据分析同步完成: {target_date}'
                    }
                else:
                    error_msg = result.get('error', 'Unknown error')
                    self._record_sync_error('manual_analytics', error_msg)

                    return {
                        'success': False,
                        'error': error_msg
                    }

            except Exception as e:
                error_msg = f"手动数据分析同步失败: {e}"
                logger.error(error_msg)
                self._record_sync_error('manual_analytics', str(e))

                return {
                    'success': False,
                    'error': error_msg
                }


# 全局调度器实例
ozon_sync_scheduler = OzonSyncScheduler()
