"""
OZON API调用服务
用于实际调用OZON API接口，获取真实的商品数据
"""

import requests
import json
import time
import uuid
from datetime import datetime
from typing import List, Dict, Optional, Tuple
from models.database import get_db_manager
from services.config_service import config_service
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OzonApiService:
    def __init__(self):
        self.db = get_db_manager()
        self.base_url = "https://api-seller.ozon.ru"
        self.headers = {
            "Content-Type": "application/json",
            "Accept": "application/json"
        }
        
        # 从platform_api_data表获取API配置
        self._load_api_config()
    
    def _load_api_config(self):
        """加载OZON API配置 - 优先从环境变量读取"""
        try:
            import os

            # 优先从环境变量读取敏感配置
            # 注意：敏感配置只从环境变量读取，不再从config_service获取
            self.client_id = os.environ.get('OZON_CLIENT_ID') or config_service.get_config('ozon_client_id', '2962343')
            self.api_key = os.environ.get('OZON_API_KEY') or ''

            if not self.api_key:
                logger.warning("OZON API Key未配置，请设置OZON_API_KEY环境变量")
                return

            # 更新请求头
            self.headers.update({
                "Api-Key": self.api_key,
                "Client-Id": self.client_id
            })

            logger.info(f"OZON API配置加载成功，Client-Id: {self.client_id}")

            # 从platform_api_data表获取base_url
            if self.db:
                apis = self.db.execute_query("""
                    SELECT base_url FROM platform_api_data
                    WHERE platform_name = 'OZON' LIMIT 1
                """)

                if apis and apis[0]['base_url']:
                    self.base_url = apis[0]['base_url']

        except Exception as e:
            logger.error(f"加载API配置失败: {e}")
    
    def _create_sync_log(self, api_endpoint: str, sync_type: str, request_params: dict) -> str:
        """创建同步日志记录"""
        sync_task_id = str(uuid.uuid4())
        
        try:
            self.db.execute_query("""
                INSERT INTO ozon_api_sync_log 
                (sync_task_id, api_endpoint, sync_type, request_params, sync_status, started_at)
                VALUES (%s, %s, %s, %s, 'running', NOW())
            """, (sync_task_id, api_endpoint, sync_type, json.dumps(request_params)))
            
            logger.info(f"创建同步日志: {sync_task_id}")
            return sync_task_id
            
        except Exception as e:
            logger.error(f"创建同步日志失败: {e}")
            return sync_task_id
    
    def _update_sync_log(self, sync_task_id: str, status: str, total_processed: int = 0, 
                        total_failed: int = 0, error_message: str = None):
        """更新同步日志"""
        try:
            self.db.execute_query("""
                UPDATE ozon_api_sync_log 
                SET sync_status = %s, total_processed = %s, total_failed = %s, 
                    error_message = %s, completed_at = NOW(),
                    duration_seconds = TIMESTAMPDIFF(SECOND, started_at, NOW())
                WHERE sync_task_id = %s
            """, (status, total_processed, total_failed, error_message, sync_task_id))
            
        except Exception as e:
            logger.error(f"更新同步日志失败: {e}")
    
    def get_product_list(self, limit: int = 100, last_id: str = "", 
                        visibility: str = "ALL") -> Tuple[List[Dict], Optional[str]]:
        """
        调用 /v3/product/list API 获取商品列表
        
        Args:
            limit: 每页数量 (最大1000)
            last_id: 分页游标
            visibility: 可见性筛选 (ALL, VISIBLE, INVISIBLE等)
            
        Returns:
            Tuple[商品列表, 下一页游标]
        """
        api_endpoint = "/v3/product/list"
        request_data = {
            "limit": limit,
            "last_id": last_id,
            "filter": {
                "visibility": visibility
            }
        }
        
        sync_task_id = self._create_sync_log(api_endpoint, "list", request_data)
        
        try:
            url = f"{self.base_url}{api_endpoint}"
            logger.info(f"调用OZON API: {url}")
            logger.info(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")
            
            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'result' in data and 'items' in data['result']:
                    items = data['result']['items']
                    next_last_id = data['result'].get('last_id', '')
                    
                    logger.info(f"成功获取 {len(items)} 个商品")
                    self._update_sync_log(sync_task_id, "success", len(items), 0)
                    
                    return items, next_last_id if next_last_id != last_id else None
                else:
                    logger.warning(f"API返回数据格式异常: {data}")
                    self._update_sync_log(sync_task_id, "failed", 0, 0, "返回数据格式异常")
                    return [], None
            else:
                error_msg = f"API调用失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
                return [], None
                
        except Exception as e:
            error_msg = f"API调用异常: {str(e)}"
            logger.error(error_msg)
            self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
            return []

    def update_product_stocks(self, stocks_data: List[Dict]) -> Dict:
        """
        调用 /v2/products/stocks API 更新商品库存

        Args:
            stocks_data: 库存数据列表，格式：
            [
                {
                    "offer_id": "PH11042",  # 可选
                    "product_id": 313455276,  # 必需
                    "stock": 100,  # 必需
                    "warehouse_id": 22142605386000  # 必需
                }
            ]

        Returns:
            Dict: 更新结果
        """
        api_endpoint = "/v2/products/stocks"
        request_data = {
            "stocks": stocks_data
        }

        sync_task_id = self._create_sync_log(api_endpoint, "stock_update", request_data)

        try:
            # 验证数据格式
            if not stocks_data or len(stocks_data) > 100:
                error_msg = "库存数据为空或超过100个商品限制"
                logger.error(error_msg)
                self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
                return {"success": False, "error": error_msg}

            # 验证必需字段
            for stock_item in stocks_data:
                if not stock_item.get("product_id"):
                    error_msg = "product_id是必需字段"
                    logger.error(error_msg)
                    self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
                    return {"success": False, "error": error_msg}

                if "stock" not in stock_item:
                    error_msg = "stock是必需字段"
                    logger.error(error_msg)
                    self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
                    return {"success": False, "error": error_msg}

                if not stock_item.get("warehouse_id"):
                    error_msg = "warehouse_id是必需字段"
                    logger.error(error_msg)
                    self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
                    return {"success": False, "error": error_msg}

            url = f"{self.base_url}{api_endpoint}"
            logger.info(f"调用OZON库存更新API: {url}")
            logger.info(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")

            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"库存更新成功: {len(stocks_data)} 个商品")
                self._update_sync_log(sync_task_id, "success", len(stocks_data), 0)

                return {
                    "success": True,
                    "data": data,
                    "updated_count": len(stocks_data)
                }
            else:
                error_msg = f"库存更新失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                self._update_sync_log(sync_task_id, "failed", 0, len(stocks_data), error_msg)

                return {
                    "success": False,
                    "error": error_msg,
                    "status_code": response.status_code
                }

        except Exception as e:
            error_msg = f"库存更新API调用异常: {str(e)}"
            logger.error(error_msg)
            self._update_sync_log(sync_task_id, "failed", 0, len(stocks_data), error_msg)

            return {
                "success": False,
                "error": error_msg
            }, None
    
    def get_product_info(self, product_ids: List[str] = None, offer_ids: List[str] = None, 
                        skus: List[str] = None) -> List[Dict]:
        """
        调用 /v3/product/info/list API 获取商品详细信息
        
        Args:
            product_ids: OZON商品ID列表
            offer_ids: 商家商品ID列表  
            skus: SKU列表
            
        Returns:
            商品详细信息列表
        """
        api_endpoint = "/v3/product/info/list"
        request_data = {}
        
        if product_ids:
            request_data["product_id"] = [str(pid) for pid in product_ids]
        if offer_ids:
            request_data["offer_id"] = offer_ids
        if skus:
            request_data["sku"] = skus
            
        if not request_data:
            logger.error("必须提供至少一种商品标识符")
            return []
        
        sync_task_id = self._create_sync_log(api_endpoint, "detail", request_data)
        
        try:
            url = f"{self.base_url}{api_endpoint}"
            logger.info(f"调用OZON API: {url}")
            logger.info(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")
            
            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)
            
            if response.status_code == 200:
                data = response.json()
                
                if 'items' in data:
                    items = data['items']
                    logger.info(f"成功获取 {len(items)} 个商品详情")
                    self._update_sync_log(sync_task_id, "success", len(items), 0)
                    return items
                else:
                    logger.warning(f"API返回数据格式异常: {data}")
                    self._update_sync_log(sync_task_id, "failed", 0, 0, "返回数据格式异常")
                    return []
            else:
                error_msg = f"API调用失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
                return []
                
        except Exception as e:
            error_msg = f"API调用异常: {str(e)}"
            logger.error(error_msg)
            self._update_sync_log(sync_task_id, "failed", 0, 0, error_msg)
            return []
    
    def save_product_list_data(self, products: List[Dict]) -> int:
        """
        保存商品列表数据到ozon_products表
        
        Args:
            products: 从/v3/product/list获取的商品列表
            
        Returns:
            保存成功的商品数量
        """
        saved_count = 0
        
        for product in products:
            try:
                # 提取关键字段
                ozon_product_id = product.get('product_id')
                offer_id = product.get('offer_id')
                
                if not ozon_product_id or not offer_id:
                    logger.warning(f"商品缺少必要字段: {product}")
                    continue
                
                # 检查是否已存在
                existing = self.db.execute_query(
                    "SELECT id FROM ozon_products WHERE ozon_product_id = %s", 
                    (ozon_product_id,)
                )
                
                if existing:
                    # 更新现有记录
                    self.db.execute_query("""
                        UPDATE ozon_products SET
                            offer_id = %s,
                            is_archived = %s,
                            is_discounted = %s,
                            has_fbo_stocks = %s,
                            has_fbs_stocks = %s,
                            last_list_sync_at = NOW(),
                            api_sync_status = 'list_synced',
                            updated_at = NOW()
                        WHERE ozon_product_id = %s
                    """, (
                        offer_id,
                        product.get('archived', False),
                        product.get('is_discounted', False),
                        product.get('has_fbo_stocks', False),
                        product.get('has_fbs_stocks', False),
                        ozon_product_id
                    ))
                else:
                    # 插入新记录
                    self.db.execute_query("""
                        INSERT INTO ozon_products (
                            ozon_product_id, offer_id, is_archived, is_discounted,
                            has_fbo_stocks, has_fbs_stocks, last_list_sync_at,
                            api_sync_status, created_at, updated_at
                        ) VALUES (%s, %s, %s, %s, %s, %s, NOW(), 'list_synced', NOW(), NOW())
                    """, (
                        ozon_product_id,
                        offer_id,
                        product.get('archived', False),
                        product.get('is_discounted', False),
                        product.get('has_fbo_stocks', False),
                        product.get('has_fbs_stocks', False)
                    ))
                
                saved_count += 1
                
            except Exception as e:
                logger.error(f"保存商品数据失败: {e}, 商品: {product}")
        
        logger.info(f"成功保存 {saved_count} 个商品的列表数据")
        return saved_count

    def _find_local_product_match(self, ozon_product_id: str, offer_id: str, product_name: str) -> List[Dict]:
        """
        智能匹配本地商品

        Args:
            ozon_product_id: OZON商品ID
            offer_id: OZON offer_id
            product_name: OZON商品名称

        Returns:
            匹配的本地商品列表
        """
        # 1. 首先通过已有的ozon_product_id匹配
        local_product = self.db.execute_query("""
            SELECT id FROM product_info
            WHERE ozon_product_id = %s
            LIMIT 1
        """, (str(ozon_product_id),))

        if local_product:
            return local_product

        # 2. 通过item_code精确匹配offer_id
        try:
            local_product = self.db.execute_query("""
                SELECT id FROM product_info
                WHERE item_code = %s
                LIMIT 1
            """, (offer_id,))

            if local_product:
                logger.info(f"通过item_code匹配到本地商品: offer_id={offer_id}")
                return local_product
        except Exception as e:
            logger.debug(f"item_code匹配查询失败: {e}")

        # 3. 通过model_name精确匹配（处理不同的命名格式）
        if offer_id:
            # 尝试多种命名格式
            name_variants = [
                offer_id.replace('-', ' '),  # Ulefone-Armor6 -> Ulefone Armor6
                offer_id.replace('-', '/'),  # Oukitel-Wp30/30-Pro -> Oukitel/Wp30/30/Pro
                offer_id.replace('/', '/').replace('-', ' '),  # 混合处理
            ]

            for variant in name_variants:
                try:
                    local_product = self.db.execute_query("""
                        SELECT id FROM product_info
                        WHERE model_name = %s
                        LIMIT 1
                    """, (variant,))

                    if local_product:
                        logger.info(f"通过model_name精确匹配到本地商品: {offer_id} -> {variant}")
                        return local_product
                except Exception as e:
                    logger.debug(f"model_name精确匹配查询失败: {e}")

        # 4. 通过model_name模糊匹配
        if offer_id:
            try:
                # 提取关键词进行模糊匹配
                keywords = offer_id.replace('-', ' ').replace('/', ' ').split()
                if len(keywords) >= 2:
                    # 使用前两个关键词进行匹配
                    search_pattern = f"%{keywords[0]}%{keywords[1]}%"
                    local_product = self.db.execute_query("""
                        SELECT id FROM product_info
                        WHERE model_name LIKE %s
                        LIMIT 1
                    """, (search_pattern,))

                    if local_product:
                        logger.info(f"通过model_name模糊匹配到本地商品: {offer_id} -> {search_pattern}")
                        return local_product
            except Exception as e:
                logger.debug(f"model_name模糊匹配查询失败: {e}")

        logger.debug(f"未找到匹配的本地商品: offer_id={offer_id}")
        return []

    def _auto_link_local_product(self, local_product_id: int, ozon_product_id: str, offer_id: str) -> bool:
        """
        自动关联本地商品和OZON商品

        Args:
            local_product_id: 本地商品ID
            ozon_product_id: OZON商品ID
            offer_id: OZON offer_id

        Returns:
            是否关联成功
        """
        try:
            # 更新product_info表，设置ozon_product_id和上传状态
            result = self.db.execute_query("""
                UPDATE product_info
                SET ozon_product_id = %s,
                    ozon_upload_status = 'uploaded',
                    updated_at = NOW()
                WHERE id = %s AND (ozon_product_id IS NULL OR ozon_product_id = '')
            """, (ozon_product_id, local_product_id))

            if result:
                logger.info(f"✅ 自动关联成功: 本地商品ID={local_product_id} <-> OZON商品ID={ozon_product_id} (offer_id={offer_id})")
                return True
            else:
                logger.debug(f"本地商品已有OZON关联，跳过: local_id={local_product_id}")
                return False

        except Exception as e:
            logger.error(f"自动关联失败: {e}")
            return False

    def _batch_link_products(self) -> int:
        """
        批量关联本地商品和OZON商品

        Returns:
            成功关联的商品数量
        """
        linked_count = 0

        try:
            # 获取所有有local_product_id但本地商品没有ozon_product_id的情况
            unlinked_pairs = self.db.execute_query("""
                SELECT o.ozon_product_id, o.offer_id, o.name, o.local_product_id,
                       p.id as product_info_id, p.model_name, p.ozon_product_id as current_ozon_id
                FROM ozon_products o
                LEFT JOIN product_info p ON o.local_product_id = p.id
                WHERE o.offer_id IS NOT NULL
                AND o.local_product_id IS NOT NULL
                AND o.local_product_id != 0
                AND (p.ozon_product_id IS NULL OR p.ozon_product_id = '')
                ORDER BY o.updated_at DESC
            """)

            logger.info(f"找到 {len(unlinked_pairs)} 个需要反向关联的商品对")

            for pair in unlinked_pairs:
                ozon_product_id = pair['ozon_product_id']
                offer_id = pair['offer_id']
                local_product_id = pair['local_product_id']

                # 执行反向关联：更新product_info表
                if self._auto_link_local_product(local_product_id, ozon_product_id, offer_id):
                    linked_count += 1

            # 如果上面没有找到，尝试查找完全未关联的OZON商品
            if linked_count == 0:
                unlinked_ozon_products = self.db.execute_query("""
                    SELECT ozon_product_id, offer_id, name
                    FROM ozon_products
                    WHERE offer_id IS NOT NULL
                    AND (local_product_id IS NULL OR local_product_id = 0)
                    ORDER BY updated_at DESC
                """)

                logger.info(f"找到 {len(unlinked_ozon_products)} 个完全未关联的OZON商品")

                for ozon_product in unlinked_ozon_products:
                    ozon_product_id = ozon_product['ozon_product_id']
                    offer_id = ozon_product['offer_id']
                    product_name = ozon_product['name'] or ''

                    # 查找匹配的本地商品
                    local_product = self._find_local_product_match(ozon_product_id, offer_id, product_name)

                    if local_product:
                        local_product_id = local_product[0]['id']

                        # 执行关联
                        if self._auto_link_local_product(local_product_id, ozon_product_id, offer_id):
                            # 同时更新ozon_products表的local_product_id
                            self.db.execute_query("""
                                UPDATE ozon_products
                                SET local_product_id = %s, updated_at = NOW()
                                WHERE ozon_product_id = %s
                            """, (local_product_id, ozon_product_id))

                            linked_count += 1

            logger.info(f"✅ 批量关联完成: 成功关联 {linked_count} 个商品")

        except Exception as e:
            logger.error(f"批量关联失败: {e}")

        return linked_count

    def _batch_sync_ratings(self, batch_size: int = 20) -> int:
        """
        批量同步商品评分（优化版：真正的批量API调用）

        Args:
            batch_size: 每批处理的SKU数量

        Returns:
            成功同步评分的商品数量
        """
        synced_count = 0

        try:
            # 获取所有有SKU但没有评分或评分过期的商品
            products_without_ratings = self.db.execute_query("""
                SELECT op.sku, op.offer_id, op.name
                FROM ozon_products op
                LEFT JOIN ozon_product_ratings opr ON op.sku = opr.sku
                WHERE op.sku IS NOT NULL
                AND (opr.sku IS NULL OR opr.last_sync_at < DATE_SUB(NOW(), INTERVAL 7 DAY))
                ORDER BY op.updated_at DESC
                LIMIT 100
            """)

            logger.info(f"找到 {len(products_without_ratings)} 个需要同步评分的商品")

            if not products_without_ratings:
                return 0

            # 按批次处理
            for i in range(0, len(products_without_ratings), batch_size):
                batch = products_without_ratings[i:i + batch_size]
                skus = [str(product['sku']) for product in batch]
                offer_ids = [product['offer_id'] for product in batch]

                try:
                    logger.info(f"批量同步评分: 第 {i//batch_size + 1} 批，包含 {len(skus)} 个SKU")
                    logger.info(f"SKU列表: {skus}")

                    # 调用批量评分API
                    rating_data = self.get_product_ratings_by_skus(skus, save_to_db=True)

                    if rating_data and rating_data.get('products'):
                        batch_synced = len(rating_data.get('products', []))
                        synced_count += batch_synced
                        logger.info(f"✅ 第 {i//batch_size + 1} 批评分同步成功: {batch_synced} 个商品")

                        # 记录每个成功同步的商品
                        for j, offer_id in enumerate(offer_ids[:batch_synced]):
                            logger.info(f"✅ 评分同步成功: {offer_id}")
                    else:
                        logger.warning(f"⚠️ 第 {i//batch_size + 1} 批评分同步失败")

                    # 避免API限流 - 批量调用间隔更长
                    time.sleep(2)

                except Exception as e:
                    logger.error(f"第 {i//batch_size + 1} 批评分同步异常: {e}")

            logger.info(f"✅ 批量评分同步完成: 成功同步 {synced_count} 个商品评分")

        except Exception as e:
            logger.error(f"批量评分同步失败: {e}")

        return synced_count

    def save_product_detail_data(self, products: List[Dict]) -> int:
        """
        保存商品详细信息到ozon_products表

        Args:
            products: 从/v3/product/info/list获取的商品详情列表

        Returns:
            保存成功的商品数量
        """
        saved_count = 0

        for product in products:
            try:
                # 提取关键字段
                ozon_product_id = product.get('id')
                offer_id = product.get('offer_id')

                if not ozon_product_id or not offer_id:
                    logger.warning(f"商品详情缺少必要字段: {product}")
                    continue

                # 尝试关联本地商品 - 增强匹配逻辑
                local_product = self._find_local_product_match(ozon_product_id, offer_id, product.get('name', ''))

                local_product_id = local_product[0]['id'] if local_product else None

                # 如果找到了本地商品，自动执行关联
                if local_product_id:
                    self._auto_link_local_product(local_product_id, ozon_product_id, offer_id)

                # 解析时间字段
                ozon_created_at = None
                ozon_updated_at = None
                if product.get('created_at'):
                    try:
                        ozon_created_at = datetime.fromisoformat(product['created_at'].replace('Z', '+00:00'))
                    except:
                        pass
                if product.get('updated_at'):
                    try:
                        ozon_updated_at = datetime.fromisoformat(product['updated_at'].replace('Z', '+00:00'))
                    except:
                        pass

                # 提取SKU信息
                sku = None
                stocks = product.get('stocks', {})
                if stocks and 'stocks' in stocks and len(stocks['stocks']) > 0:
                    sku = stocks['stocks'][0].get('sku')

                # 获取商品关键词（从特征描述API）- 在商品同步时强制获取最新关键词
                keywords = self._get_product_keywords_force(ozon_product_id, offer_id, sku)

                # 更新或插入商品详细信息
                self.db.execute_query("""
                    INSERT INTO ozon_products (
                        ozon_product_id, offer_id, local_product_id, name, price, old_price,
                        marketing_price, currency_code, vat, type_id, description_category_id,
                        barcodes, is_archived, is_autoarchived, is_discounted, is_kgt, is_super,
                        is_prepayment_allowed, has_discounted_fbo_item, discounted_fbo_stocks,
                        stocks_detail, statuses_detail, visibility_details, images, primary_image,
                        color_image, images360, price_indexes, commissions, volume_weight,
                        model_info, sources, errors, sku, keywords, ozon_created_at, ozon_updated_at,
                        last_detail_sync_at, api_sync_status, created_at, updated_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        NOW(), 'detail_synced', NOW(), NOW()
                    ) ON DUPLICATE KEY UPDATE
                        local_product_id = VALUES(local_product_id),
                        name = VALUES(name),
                        price = VALUES(price),
                        old_price = VALUES(old_price),
                        marketing_price = VALUES(marketing_price),
                        currency_code = VALUES(currency_code),
                        vat = VALUES(vat),
                        type_id = VALUES(type_id),
                        description_category_id = VALUES(description_category_id),
                        barcodes = VALUES(barcodes),
                        is_archived = VALUES(is_archived),
                        is_autoarchived = VALUES(is_autoarchived),
                        is_discounted = VALUES(is_discounted),
                        is_kgt = VALUES(is_kgt),
                        is_super = VALUES(is_super),
                        is_prepayment_allowed = VALUES(is_prepayment_allowed),
                        has_discounted_fbo_item = VALUES(has_discounted_fbo_item),
                        discounted_fbo_stocks = VALUES(discounted_fbo_stocks),
                        stocks_detail = VALUES(stocks_detail),
                        statuses_detail = VALUES(statuses_detail),
                        visibility_details = VALUES(visibility_details),
                        images = VALUES(images),
                        primary_image = VALUES(primary_image),
                        color_image = VALUES(color_image),
                        images360 = VALUES(images360),
                        price_indexes = VALUES(price_indexes),
                        commissions = VALUES(commissions),
                        volume_weight = VALUES(volume_weight),
                        model_info = VALUES(model_info),
                        sources = VALUES(sources),
                        errors = VALUES(errors),
                        sku = VALUES(sku),
                        keywords = VALUES(keywords),
                        ozon_created_at = VALUES(ozon_created_at),
                        ozon_updated_at = VALUES(ozon_updated_at),
                        last_detail_sync_at = NOW(),
                        api_sync_status = 'detail_synced',
                        updated_at = NOW()
                """, (
                    ozon_product_id, offer_id, local_product_id,
                    product.get('name'),
                    float(product.get('price', 0)) if product.get('price') else None,
                    float(product.get('old_price', 0)) if product.get('old_price') else None,
                    float(product.get('marketing_price', 0)) if product.get('marketing_price') else None,
                    product.get('currency_code', 'RUB'),
                    product.get('vat'),
                    product.get('type_id'),
                    product.get('description_category_id'),
                    json.dumps(product.get('barcodes', [])) if product.get('barcodes') else None,
                    product.get('is_archived', False),
                    product.get('is_autoarchived', False),
                    product.get('is_discounted', False),
                    product.get('is_kgt', False),
                    product.get('is_super', False),
                    product.get('is_prepayment_allowed', False),
                    product.get('has_discounted_fbo_item', False),
                    product.get('discounted_fbo_stocks', 0),
                    json.dumps(product.get('stocks')) if product.get('stocks') else None,
                    json.dumps(product.get('statuses')) if product.get('statuses') else None,
                    json.dumps(product.get('visibility_details')) if product.get('visibility_details') else None,
                    json.dumps(product.get('images', [])) if product.get('images') else None,
                    json.dumps(product.get('primary_image', [])) if product.get('primary_image') else None,
                    json.dumps(product.get('color_image', [])) if product.get('color_image') else None,
                    json.dumps(product.get('images360', [])) if product.get('images360') else None,
                    json.dumps(product.get('price_indexes')) if product.get('price_indexes') else None,
                    json.dumps(product.get('commissions', [])) if product.get('commissions') else None,
                    product.get('volume_weight'),
                    json.dumps(product.get('model_info')) if product.get('model_info') else None,
                    json.dumps(product.get('sources', [])) if product.get('sources') else None,
                    json.dumps(product.get('errors', [])) if product.get('errors') else None,
                    sku,
                    keywords,
                    ozon_created_at,
                    ozon_updated_at
                ))

                saved_count += 1

            except Exception as e:
                logger.error(f"保存商品详情失败: {e}, 商品: {product}")

        logger.info(f"成功保存 {saved_count} 个商品的详细信息")
        return saved_count

    def get_product_rating_by_sku(self, sku: str, save_to_db: bool = True) -> Dict:
        """
        调用 /v1/product/rating-by-sku API 获取单个商品内容排名

        注意：建议使用 get_product_ratings_by_skus 进行批量查询以提高效率

        Args:
            sku: 商品SKU
            save_to_db: 是否保存到数据库

        Returns:
            商品评级信息字典
        """
        # 调用批量方法处理单个SKU
        result = self.get_product_ratings_by_skus([sku], save_to_db)
        return result

    def get_product_ratings_by_skus(self, skus: List[str], save_to_db: bool = True) -> Dict:
        """
        批量调用 /v1/product/rating-by-sku API 获取多个商品内容排名

        Args:
            skus: 商品SKU列表
            save_to_db: 是否保存到数据库

        Returns:
            商品评级信息字典
        """
        if not skus:
            logger.warning("SKU列表为空")
            return {}

        api_endpoint = "/v1/product/rating-by-sku"
        request_data = {
            "skus": [int(sku) for sku in skus]  # OZON API期望的是整数SKU数组
        }

        sync_task_id = self._create_sync_log(api_endpoint, "rating_batch", request_data)

        try:
            url = f"{self.base_url}{api_endpoint}"
            logger.info(f"调用OZON批量评级API: {url}")
            logger.info(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")
            logger.info(f"批量查询 {len(skus)} 个SKU的评级信息")

            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                logger.info(f"成功获取 {len(skus)} 个SKU的评级信息")

                # 批量保存到数据库
                if save_to_db:
                    saved_count = self.save_batch_product_rating_data(result)
                    logger.info(f"成功保存 {saved_count} 个SKU的评级数据")

                self._update_sync_log(sync_task_id, "success", len(skus), len(skus))
                return result
            else:
                error_msg = f"API调用失败: HTTP {response.status_code}, {response.text}"
                logger.error(error_msg)
                self._update_sync_log(sync_task_id, "failed", 0, len(skus), error_msg)
                return {}

        except Exception as e:
            error_msg = f"API调用异常: {str(e)}"
            logger.error(error_msg)
            self._update_sync_log(sync_task_id, "failed", 0, len(skus), error_msg)
            return {}

    def save_product_rating_data(self, sku: str, rating_data: Dict) -> bool:
        """
        保存商品评级数据到数据库

        Args:
            sku: 商品SKU
            rating_data: 评级数据

        Returns:
            是否保存成功
        """
        try:
            # 获取关联的商品信息
            product_info = self.db.execute_query(
                "SELECT id, ozon_product_id FROM ozon_products WHERE sku = %s",
                (sku,)
            )

            local_product_id = None
            ozon_product_id = None
            if product_info:
                local_product_id = product_info[0]['id']
                ozon_product_id = product_info[0]['ozon_product_id']

            # 提取评级信息 - 处理OZON API的实际响应格式
            products = rating_data.get('products', [])
            if not products:
                logger.warning(f"SKU {sku} 没有评级数据")
                return False

            product_rating = products[0]  # 取第一个商品的评级

            # 提取基本评级信息
            overall_rating = product_rating.get('rating')  # OZON返回的是rating字段
            rating_status = self._get_rating_status(overall_rating) if overall_rating else None

            # 从groups中提取详细评分
            groups = product_rating.get('groups', [])
            title_score = None
            description_score = None
            images_score = None
            attributes_score = None
            category_score = None

            # 解析各个评级组别
            for group in groups:
                group_key = group.get('key')
                group_rating = group.get('rating')

                if group_key == 'text':
                    description_score = group_rating
                elif group_key == 'media':
                    images_score = group_rating
                elif group_key == 'important_attributes':
                    attributes_score = group_rating
                elif group_key == 'other_attributes':
                    category_score = group_rating

            # 设置内容质量评分为总评级
            content_quality_score = overall_rating
            content_quality_status = rating_status

            # 保存或更新评级数据
            self.db.execute_query("""
                INSERT INTO ozon_product_ratings (
                    ozon_product_id, sku, local_product_id, overall_rating, rating_status,
                    content_quality_score, content_quality_status, title_score, description_score,
                    images_score, attributes_score, category_score, rating_details,
                    improvement_suggestions, quality_factors, raw_response,
                    api_sync_status, last_sync_at, created_at, updated_at
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                    'success', NOW(), NOW(), NOW()
                ) ON DUPLICATE KEY UPDATE
                    ozon_product_id = VALUES(ozon_product_id),
                    local_product_id = VALUES(local_product_id),
                    overall_rating = VALUES(overall_rating),
                    rating_status = VALUES(rating_status),
                    content_quality_score = VALUES(content_quality_score),
                    content_quality_status = VALUES(content_quality_status),
                    title_score = VALUES(title_score),
                    description_score = VALUES(description_score),
                    images_score = VALUES(images_score),
                    attributes_score = VALUES(attributes_score),
                    category_score = VALUES(category_score),
                    rating_details = VALUES(rating_details),
                    improvement_suggestions = VALUES(improvement_suggestions),
                    quality_factors = VALUES(quality_factors),
                    raw_response = VALUES(raw_response),
                    api_sync_status = 'success',
                    last_sync_at = NOW(),
                    updated_at = NOW()
            """, (
                ozon_product_id,
                sku,
                local_product_id,
                overall_rating,
                rating_status,
                content_quality_score,
                content_quality_status,
                title_score,
                description_score,
                images_score,
                attributes_score,
                category_score,
                json.dumps(groups) if groups else None,
                json.dumps([group.get('improve_attributes', []) for group in groups if group.get('improve_attributes')]) if groups else None,
                json.dumps({'groups': groups}) if groups else None,
                json.dumps(rating_data)
            ))

            logger.info(f"成功保存SKU {sku} 的评级数据")
            return True

        except Exception as e:
            logger.error(f"保存评级数据失败: {e}")
            return False

    def save_batch_product_rating_data(self, rating_data: Dict) -> int:
        """
        批量保存商品评级数据到数据库

        Args:
            rating_data: 批量评级数据

        Returns:
            成功保存的数量
        """
        saved_count = 0

        try:
            products = rating_data.get('products', [])
            if not products:
                logger.warning("批量评级数据中没有产品信息")
                return 0

            logger.info(f"开始批量保存 {len(products)} 个产品的评级数据")

            for product_rating in products:
                try:
                    sku = str(product_rating.get('sku', ''))
                    if not sku:
                        logger.warning("产品评级数据中缺少SKU")
                        continue

                    # 为每个产品构造单独的评级数据格式
                    single_rating_data = {
                        'products': [product_rating]
                    }

                    # 调用单个保存方法
                    if self.save_product_rating_data(sku, single_rating_data):
                        saved_count += 1
                        logger.debug(f"成功保存SKU {sku} 的评级数据")
                    else:
                        logger.warning(f"保存SKU {sku} 的评级数据失败")

                except Exception as e:
                    logger.error(f"保存单个产品评级数据失败: {e}")

            logger.info(f"批量保存评级数据完成: 成功保存 {saved_count}/{len(products)} 个产品")
            return saved_count

        except Exception as e:
            logger.error(f"批量保存评级数据失败: {e}")
            return 0

    def _get_rating_status(self, rating: float) -> str:
        """
        根据评级分数确定状态

        Args:
            rating: 评级分数 (0-100)

        Returns:
            评级状态字符串
        """
        if rating >= 90:
            return "excellent"
        elif rating >= 70:
            return "good"
        elif rating >= 50:
            return "needs_improvement"
        else:
            return "poor"

    def sync_all_products(self, max_pages: int = 10) -> Dict[str, int]:
        """
        完整同步所有商品数据（两步API调用）

        Args:
            max_pages: 最大分页数量

        Returns:
            同步统计信息
        """
        stats = {
            'total_list_products': 0,
            'total_detail_products': 0,
            'saved_list_count': 0,
            'saved_detail_count': 0,
            'errors': 0
        }

        logger.info("开始完整同步OZON商品数据")

        # 第一步：获取商品列表
        all_products = []
        last_id = ""
        page = 0

        while page < max_pages:
            logger.info(f"获取第 {page + 1} 页商品列表...")

            products, next_last_id = self.get_product_list(limit=100, last_id=last_id)

            if not products:
                logger.info("没有更多商品，列表获取完成")
                break

            all_products.extend(products)
            stats['total_list_products'] += len(products)

            # 保存列表数据
            saved_count = self.save_product_list_data(products)
            stats['saved_list_count'] += saved_count

            if not next_last_id:
                logger.info("已到达最后一页")
                break

            last_id = next_last_id
            page += 1

            # 避免API限流
            time.sleep(1)

        logger.info(f"商品列表同步完成，共获取 {stats['total_list_products']} 个商品")

        # 第二步：获取商品详细信息
        if all_products:
            # 提取product_id列表
            product_ids = [str(p.get('product_id')) for p in all_products if p.get('product_id')]

            # 分批获取详细信息（每批50个）
            batch_size = 50
            for i in range(0, len(product_ids), batch_size):
                batch_ids = product_ids[i:i + batch_size]
                logger.info(f"获取商品详情批次 {i//batch_size + 1}，商品数量: {len(batch_ids)}")

                detail_products = self.get_product_info(product_ids=batch_ids)

                if detail_products:
                    stats['total_detail_products'] += len(detail_products)
                    saved_count = self.save_product_detail_data(detail_products)
                    stats['saved_detail_count'] += saved_count
                else:
                    stats['errors'] += 1

                # 避免API限流
                time.sleep(2)

        # 第三步：批量关联未关联的商品
        logger.info("第三步：批量关联本地商品...")
        linked_count = self._batch_link_products()
        stats['linked_count'] = linked_count

        # 第四步：同步商品评分
        logger.info("第四步：同步商品评分...")
        rating_count = self._batch_sync_ratings()
        stats['rating_count'] = rating_count

        # 第五步：同步商品关键词
        logger.info("第五步：同步商品关键词...")
        keywords_count = self._batch_sync_keywords()
        stats['keywords_count'] = keywords_count

        logger.info(f"商品同步完成: {stats}")
        return stats

    def sync_incremental_products(self, hours_back: int = 24, max_pages: int = 5) -> Dict[str, int]:
        """
        增量同步商品数据（只同步最近更新的商品）

        Args:
            hours_back: 回溯小时数
            max_pages: 最大分页数

        Returns:
            同步统计信息
        """
        stats = {
            'total_list_products': 0,
            'total_detail_products': 0,
            'saved_list_count': 0,
            'saved_detail_count': 0,
            'updated_count': 0,
            'errors': 0
        }

        logger.info(f"开始增量同步OZON商品数据（回溯{hours_back}小时）")

        # 获取现有商品的最后更新时间
        existing_products = self.db.execute_query("""
            SELECT ozon_product_id, ozon_updated_at
            FROM ozon_products
            WHERE ozon_updated_at IS NOT NULL
        """)

        existing_product_times = {
            str(p['ozon_product_id']): p['ozon_updated_at']
            for p in existing_products
        }

        # 第一步：获取商品列表
        all_products = []
        last_id = ""
        page = 0

        while page < max_pages:
            logger.info(f"获取第 {page + 1} 页商品列表...")

            products, next_last_id = self.get_product_list(limit=100, last_id=last_id)

            if not products:
                logger.info("没有更多商品，列表获取完成")
                break

            all_products.extend(products)
            stats['total_list_products'] += len(products)

            # 保存列表数据
            saved_count = self.save_product_list_data(products)
            stats['saved_list_count'] += saved_count

            if not next_last_id:
                logger.info("已到达最后一页")
                break

            last_id = next_last_id
            page += 1
            time.sleep(1)

        # 第二步：筛选需要更新详情的商品
        products_to_update = []

        if all_products:
            product_ids = [str(p.get('product_id')) for p in all_products if p.get('product_id')]

            # 分批获取详细信息并检查是否需要更新
            batch_size = 50
            for i in range(0, len(product_ids), batch_size):
                batch_ids = product_ids[i:i + batch_size]
                logger.info(f"检查商品详情批次 {i//batch_size + 1}，商品数量: {len(batch_ids)}")

                detail_products = self.get_product_info(product_ids=batch_ids)

                if detail_products:
                    for product in detail_products:
                        product_id = str(product.get('id'))

                        # 检查是否需要更新
                        should_update = False

                        if product_id not in existing_product_times:
                            # 新商品，需要更新
                            should_update = True
                        else:
                            # 检查更新时间
                            try:
                                ozon_updated = datetime.fromisoformat(product.get('updated_at', '').replace('Z', '+00:00'))
                                local_updated = existing_product_times[product_id]

                                if ozon_updated > local_updated:
                                    should_update = True
                                    stats['updated_count'] += 1

                            except Exception as e:
                                logger.warning(f"解析时间失败: {e}")
                                should_update = True

                        if should_update:
                            products_to_update.append(product)

                time.sleep(2)

        # 第三步：更新需要更新的商品详情
        if products_to_update:
            logger.info(f"需要更新 {len(products_to_update)} 个商品的详情")

            # 分批保存详情数据
            batch_size = 20
            for i in range(0, len(products_to_update), batch_size):
                batch_products = products_to_update[i:i + batch_size]
                saved_count = self.save_product_detail_data(batch_products)
                stats['saved_detail_count'] += saved_count
                stats['total_detail_products'] += len(batch_products)
        else:
            logger.info("没有商品需要更新详情")

        logger.info(f"增量同步完成: {stats}")
        return stats

    def cleanup_old_sync_data(self, days_old: int = 30):
        """
        清理旧的同步数据

        Args:
            days_old: 清理多少天前的数据
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_old)

            # 清理旧的同步日志
            deleted_logs = self.db.execute_query("""
                DELETE FROM ozon_api_sync_log
                WHERE started_at < %s AND sync_status IN ('success', 'failed')
            """, (cutoff_date,))

            logger.info(f"清理了 {deleted_logs} 条旧同步日志")

            # 重置长时间失败的商品状态
            reset_count = self.db.execute_query("""
                UPDATE ozon_products
                SET api_sync_status = 'pending', sync_retry_count = 0, sync_error_message = NULL
                WHERE api_sync_status = 'failed'
                AND updated_at < %s
                AND sync_retry_count >= 3
            """, (cutoff_date,))

            logger.info(f"重置了 {reset_count} 个长时间失败的商品状态")

        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")

    def get_sync_statistics(self) -> Dict:
        """获取同步统计信息"""
        try:
            # 商品统计
            product_stats = self.db.execute_query("""
                SELECT
                    COUNT(*) as total_products,
                    SUM(CASE WHEN api_sync_status = 'detail_synced' THEN 1 ELSE 0 END) as synced_products,
                    SUM(CASE WHEN api_sync_status = 'failed' THEN 1 ELSE 0 END) as failed_products,
                    SUM(CASE WHEN local_product_id IS NOT NULL THEN 1 ELSE 0 END) as linked_products,
                    MAX(last_detail_sync_at) as last_sync_time
                FROM ozon_products
            """)[0]

            # 同步日志统计
            log_stats = self.db.execute_query("""
                SELECT
                    COUNT(*) as total_syncs,
                    SUM(CASE WHEN sync_status = 'success' THEN 1 ELSE 0 END) as successful_syncs,
                    SUM(CASE WHEN sync_status = 'failed' THEN 1 ELSE 0 END) as failed_syncs,
                    AVG(duration_seconds) as avg_duration
                FROM ozon_api_sync_log
                WHERE started_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
            """)[0]

            return {
                'products': {
                    'total': product_stats['total_products'],
                    'synced': product_stats['synced_products'],
                    'failed': product_stats['failed_products'],
                    'linked': product_stats['linked_products'],
                    'last_sync_time': product_stats['last_sync_time'].isoformat() if product_stats['last_sync_time'] else None
                },
                'sync_logs': {
                    'total_syncs': log_stats['total_syncs'],
                    'successful_syncs': log_stats['successful_syncs'],
                    'failed_syncs': log_stats['failed_syncs'],
                    'avg_duration_seconds': float(log_stats['avg_duration']) if log_stats['avg_duration'] else 0
                }
            }

        except Exception as e:
            logger.error(f"获取同步统计失败: {e}")
            return {}

    def call_ozon_api(self, endpoint: str, data: dict, method: str = 'POST') -> dict:
        """
        通用OZON API调用方法

        Args:
            endpoint: API端点路径 (如: '/v1/product/import/prices')
            data: 请求数据
            method: HTTP方法 (GET/POST)

        Returns:
            API响应数据
        """
        try:
            url = f"{self.base_url}{endpoint}"
            logger.info(f"调用OZON API: {url}")
            logger.info(f"请求方法: {method}")
            logger.info(f"请求数据: {json.dumps(data, ensure_ascii=False)}")

            if method.upper() == 'POST':
                response = requests.post(url, headers=self.headers, json=data, timeout=60)
            elif method.upper() == 'GET':
                response = requests.get(url, headers=self.headers, params=data, timeout=60)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            logger.info(f"响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                logger.info(f"API调用成功: {endpoint}")
                logger.info(f"响应数据: {json.dumps(result, ensure_ascii=False)}")
                return result
            else:
                error_msg = f"API调用失败: {response.status_code} - {response.text}"
                logger.error(error_msg)
                return {
                    'error': error_msg,
                    'status_code': response.status_code,
                    'response_text': response.text
                }

        except requests.exceptions.Timeout:
            error_msg = f"API调用超时: {endpoint}"
            logger.error(error_msg)
            return {'error': error_msg}

        except requests.exceptions.RequestException as e:
            error_msg = f"API请求异常: {endpoint} - {e}"
            logger.error(error_msg)
            return {'error': error_msg}

        except Exception as e:
            error_msg = f"API调用异常: {endpoint} - {e}"
            logger.error(error_msg)
            return {'error': error_msg}

    def _get_product_keywords(self, product_id: str = None, offer_id: str = None, sku: str = None) -> str:
        """
        获取商品关键词（从商品特征描述API）

        Args:
            product_id: OZON商品ID
            offer_id: 商家商品ID
            sku: SKU

        Returns:
            提取的关键词字符串，如果获取失败则返回None
        """
        try:
            # 检查是否需要获取关键词（优先更新空的keywords字段）
            if not self._should_fetch_keywords(product_id, offer_id, sku):
                return None

            # 调用商品特征描述API
            attributes_data = self.get_product_attributes(product_id, offer_id, sku)

            if not attributes_data or 'result' not in attributes_data:
                logger.debug(f"未获取到商品特征数据: product_id={product_id}, offer_id={offer_id}")
                return None

            # 从特征数据中提取关键词
            keywords = self._extract_keywords_from_attributes(attributes_data['result'])

            if keywords:
                logger.info(f"✅ 成功提取关键词: {offer_id} -> {keywords}")
                return keywords
            else:
                logger.debug(f"未从特征中找到关键词: {offer_id}")
                return None

        except Exception as e:
            logger.error(f"获取商品关键词失败: {e}, product_id={product_id}, offer_id={offer_id}")
            return None

    def _get_product_keywords_force(self, product_id: str = None, offer_id: str = None, sku: str = None) -> str:
        """
        强制获取商品关键词（从商品特征描述API），不检查现有关键词
        用于商品同步流程，确保获取OZON平台最新的关键词数据

        Args:
            product_id: OZON商品ID
            offer_id: 商家商品ID
            sku: SKU

        Returns:
            提取的关键词字符串，如果获取失败则返回None
        """
        try:
            # 直接调用商品特征描述API，不检查现有关键词
            attributes_data = self.get_product_attributes(product_id, offer_id, sku)

            if not attributes_data or 'result' not in attributes_data:
                logger.debug(f"未获取到商品特征数据: product_id={product_id}, offer_id={offer_id}")
                return None

            # 从特征数据中提取关键词
            keywords = self._extract_keywords_from_attributes(attributes_data['result'])

            if keywords:
                logger.info(f"✅ 强制获取关键词成功: {offer_id} -> {keywords}")
                return keywords
            else:
                logger.debug(f"未从OZON特征中找到关键词: {offer_id}")
                return None

        except Exception as e:
            logger.error(f"强制获取商品关键词失败: {e}, product_id={product_id}, offer_id={offer_id}")
            return None

    def _should_fetch_keywords(self, product_id: str = None, offer_id: str = None, sku: str = None) -> bool:
        """
        判断是否需要获取关键词

        Args:
            product_id: OZON商品ID
            offer_id: 商家商品ID
            sku: SKU

        Returns:
            是否需要获取关键词
        """
        try:
            # 构建查询条件
            where_conditions = []
            params = []

            if product_id:
                where_conditions.append("ozon_product_id = %s")
                params.append(product_id)
            elif offer_id:
                where_conditions.append("offer_id = %s")
                params.append(offer_id)
            elif sku:
                where_conditions.append("sku = %s")
                params.append(sku)
            else:
                return False

            where_clause = " AND ".join(where_conditions)

            # 查询现有的keywords字段
            query = f"""
            SELECT keywords FROM ozon_products
            WHERE {where_clause}
            LIMIT 1
            """

            result = self.db.execute_query(query, params)

            if not result:
                # 商品不存在，需要获取
                return True

            existing_keywords = result[0].get('keywords')

            # 如果keywords为空或None，需要获取
            return not existing_keywords or existing_keywords.strip() == ''

        except Exception as e:
            logger.error(f"检查keywords状态失败: {e}")
            return False

    def get_product_attributes(self, product_id: str = None, offer_id: str = None, sku: str = None) -> Dict:
        """
        调用 /v4/product/info/attributes API 获取商品特征描述

        Args:
            product_id: OZON商品ID
            offer_id: 商家商品ID
            sku: SKU

        Returns:
            商品特征描述数据
        """
        api_endpoint = "/v4/product/info/attributes"

        # 构建请求数据
        filter_data = {"visibility": "ALL"}

        if product_id:
            filter_data["product_id"] = [str(product_id)]
        elif offer_id:
            filter_data["offer_id"] = [offer_id]
        elif sku:
            filter_data["sku"] = [str(sku)]
        else:
            logger.error("必须提供至少一种商品标识符")
            return {}

        request_data = {
            "filter": filter_data,
            "limit": 1  # 只获取一个商品的特征
        }

        sync_task_id = self._create_sync_log(api_endpoint, "attributes", request_data)

        try:
            url = f"{self.base_url}{api_endpoint}"
            logger.debug(f"调用OZON商品特征API: {url}")
            logger.debug(f"请求参数: {json.dumps(request_data, ensure_ascii=False)}")

            response = requests.post(url, headers=self.headers, json=request_data, timeout=30)

            if response.status_code == 200:
                data = response.json()
                logger.debug(f"成功获取商品特征数据")
                self._update_sync_log(sync_task_id, "success", 1, 0)
                return data
            else:
                error_msg = f"API调用失败: {response.status_code} - {response.text}"
                logger.debug(error_msg)  # 使用debug级别，避免过多错误日志
                self._update_sync_log(sync_task_id, "failed", 0, 1, error_msg)
                return {}

        except Exception as e:
            error_msg = f"API调用异常: {str(e)}"
            logger.debug(error_msg)  # 使用debug级别
            self._update_sync_log(sync_task_id, "failed", 0, 1, error_msg)
            return {}

    def _extract_keywords_from_attributes(self, result_data: List[Dict]) -> str:
        """
        从商品特征数据中提取关键词

        Args:
            result_data: API返回的result数组

        Returns:
            提取的关键词字符串
        """
        try:
            if not result_data or len(result_data) == 0:
                return None

            product_data = result_data[0]
            attributes = product_data.get('attributes', [])

            # 查找关键词相关的特征
            # 根据OZON API文档，关键词通常在attribute id 22336
            keywords_attribute_ids = [22336, 8229]  # 可能的关键词属性ID

            for attribute in attributes:
                attr_id = attribute.get('id')
                if attr_id in keywords_attribute_ids:
                    values = attribute.get('values', [])
                    if values and len(values) > 0:
                        keyword_value = values[0].get('value', '')
                        if keyword_value and keyword_value.strip():
                            # 清理关键词格式
                            cleaned_keywords = keyword_value.replace('#', '').strip()
                            logger.debug(f"从属性ID {attr_id} 提取关键词: {cleaned_keywords}")
                            return cleaned_keywords

            # 如果没有找到专门的关键词属性，尝试从其他属性中提取
            # 例如从商品名称、型号等信息生成关键词
            product_name = product_data.get('name', '')
            if product_name:
                # 简单的关键词生成逻辑
                name_keywords = self._generate_keywords_from_name(product_name)
                if name_keywords:
                    logger.debug(f"从商品名称生成关键词: {name_keywords}")
                    return name_keywords

            return None

        except Exception as e:
            logger.error(f"提取关键词失败: {e}")
            return None

    def _generate_keywords_from_name(self, product_name: str) -> str:
        """
        从商品名称生成关键词

        Args:
            product_name: 商品名称

        Returns:
            生成的关键词
        """
        try:
            if not product_name:
                return None

            # 简单的关键词提取逻辑
            # 移除常见的无意义词汇，提取有用的关键词
            import re

            # 转换为小写并移除特殊字符
            clean_name = re.sub(r'[^\w\s-]', ' ', product_name.lower())

            # 分割单词
            words = clean_name.split()

            # 过滤掉常见的无意义词汇
            stop_words = {'for', 'with', 'and', 'or', 'the', 'a', 'an', 'in', 'on', 'at', 'to', 'от', 'для', 'с', 'и', 'или'}
            meaningful_words = [word for word in words if len(word) > 2 and word not in stop_words]

            # 取前几个有意义的词作为关键词
            if meaningful_words:
                keywords = ' '.join(meaningful_words[:5])  # 最多5个关键词
                return keywords

            return None

        except Exception as e:
            logger.error(f"从名称生成关键词失败: {e}")
            return None

    def _batch_sync_keywords(self, limit: int = 50, force_update: bool = False) -> int:
        """
        批量同步商品关键词

        Args:
            limit: 每次处理的商品数量限制
            force_update: 是否强制更新已有关键词的商品

        Returns:
            成功同步关键词的商品数量
        """
        synced_count = 0

        try:
            logger.info(f"🔄 开始批量同步商品关键词，限制: {limit} 个商品")

            # 获取需要同步关键词的商品
            if force_update:
                # 强制更新：获取所有商品
                query = """
                SELECT ozon_product_id, offer_id, sku, name
                FROM ozon_products
                WHERE ozon_product_id IS NOT NULL
                ORDER BY updated_at DESC
                LIMIT %s
                """
                params = (limit,)
            else:
                # 正常模式：只获取keywords为空的商品
                query = """
                SELECT ozon_product_id, offer_id, sku, name
                FROM ozon_products
                WHERE ozon_product_id IS NOT NULL
                AND (keywords IS NULL OR keywords = '' OR keywords = 'NULL')
                ORDER BY updated_at DESC
                LIMIT %s
                """
                params = (limit,)

            products_without_keywords = self.db.execute_query(query, params)

            if not products_without_keywords:
                logger.info("✅ 所有商品都已有关键词，无需同步")
                return 0

            logger.info(f"📋 找到 {len(products_without_keywords)} 个需要同步关键词的商品")

            # 逐个处理商品（避免API限流）
            for i, product in enumerate(products_without_keywords):
                try:
                    ozon_product_id = product['ozon_product_id']
                    offer_id = product['offer_id']
                    sku = product['sku']
                    name = product['name']

                    logger.info(f"🔍 处理商品 {i+1}/{len(products_without_keywords)}: {offer_id}")

                    # 获取关键词
                    keywords = self._get_product_keywords(ozon_product_id, offer_id, sku)

                    if keywords:
                        # 更新数据库中的关键词
                        update_query = """
                        UPDATE ozon_products
                        SET keywords = %s, updated_at = NOW()
                        WHERE ozon_product_id = %s
                        """

                        result = self.db.execute_query(update_query, (keywords, ozon_product_id), fetch=False)

                        if result:
                            synced_count += 1
                            logger.info(f"✅ 关键词同步成功: {offer_id} -> {keywords}")
                        else:
                            logger.warning(f"⚠️ 关键词更新失败: {offer_id}")
                    else:
                        logger.debug(f"🔍 未获取到关键词: {offer_id}")

                    # 避免API限流 - 每个请求间隔
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"处理商品关键词失败: {e}, 商品: {product}")

            logger.info(f"✅ 批量关键词同步完成: 成功同步 {synced_count} 个商品关键词")

        except Exception as e:
            logger.error(f"批量关键词同步失败: {e}")

        return synced_count

    def sync_keywords_for_products(self, product_ids: List[str] = None, offer_ids: List[str] = None,
                                  skus: List[str] = None, force_update: bool = False) -> Dict[str, int]:
        """
        为指定商品同步关键词

        Args:
            product_ids: OZON商品ID列表
            offer_ids: 商家商品ID列表
            skus: SKU列表
            force_update: 是否强制更新已有关键词

        Returns:
            同步统计信息
        """
        stats = {
            'total_processed': 0,
            'success_count': 0,
            'failed_count': 0,
            'skipped_count': 0
        }

        try:
            # 构建查询条件
            where_conditions = []
            params = []

            if product_ids:
                placeholders = ','.join(['%s'] * len(product_ids))
                where_conditions.append(f"ozon_product_id IN ({placeholders})")
                params.extend(product_ids)
            elif offer_ids:
                placeholders = ','.join(['%s'] * len(offer_ids))
                where_conditions.append(f"offer_id IN ({placeholders})")
                params.extend(offer_ids)
            elif skus:
                placeholders = ','.join(['%s'] * len(skus))
                where_conditions.append(f"sku IN ({placeholders})")
                params.extend(skus)
            else:
                logger.error("必须提供至少一种商品标识符")
                return stats

            # 添加关键词过滤条件
            if not force_update:
                where_conditions.append("(keywords IS NULL OR keywords = '' OR keywords = 'NULL')")

            where_clause = " AND ".join(where_conditions)

            # 查询目标商品
            query = f"""
            SELECT ozon_product_id, offer_id, sku, name, keywords
            FROM ozon_products
            WHERE {where_clause}
            """

            products = self.db.execute_query(query, params)
            stats['total_processed'] = len(products)

            logger.info(f"🔄 开始为 {len(products)} 个指定商品同步关键词")

            for product in products:
                try:
                    ozon_product_id = product['ozon_product_id']
                    offer_id = product['offer_id']
                    sku = product['sku']
                    existing_keywords = product['keywords']

                    # 检查是否需要更新
                    if not force_update and existing_keywords and existing_keywords.strip():
                        stats['skipped_count'] += 1
                        logger.debug(f"跳过已有关键词的商品: {offer_id}")
                        continue

                    # 获取关键词
                    keywords = self._get_product_keywords(ozon_product_id, offer_id, sku)

                    if keywords:
                        # 更新数据库
                        update_query = """
                        UPDATE ozon_products
                        SET keywords = %s, updated_at = NOW()
                        WHERE ozon_product_id = %s
                        """

                        result = self.db.execute_query(update_query, (keywords, ozon_product_id), fetch=False)

                        if result:
                            stats['success_count'] += 1
                            logger.info(f"✅ 关键词同步成功: {offer_id} -> {keywords}")
                        else:
                            stats['failed_count'] += 1
                            logger.warning(f"⚠️ 关键词更新失败: {offer_id}")
                    else:
                        stats['failed_count'] += 1
                        logger.debug(f"🔍 未获取到关键词: {offer_id}")

                    # 避免API限流
                    time.sleep(0.5)

                except Exception as e:
                    stats['failed_count'] += 1
                    logger.error(f"处理商品关键词失败: {e}, 商品: {product}")

            logger.info(f"✅ 指定商品关键词同步完成: {stats}")

        except Exception as e:
            logger.error(f"指定商品关键词同步失败: {e}")

        return stats
