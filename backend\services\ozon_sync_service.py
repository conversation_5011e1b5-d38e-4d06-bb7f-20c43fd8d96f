#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON商品验证和信息同步服务
基于OZON API文档实现完整的商品验证和信息同步系统
"""

import requests
import logging
import time
import json
from typing import List, Dict, Optional, Tuple
from datetime import datetime
from models.database import db_manager
from services.config_service import config_service

# 配置日志
logger = logging.getLogger('services.ozon_sync')

# OZON API配置
OZON_BASE_URL = "https://api-seller.ozon.ru"
MAX_PRODUCTS_PER_REQUEST = 1000  # OZON API限制
REQUEST_DELAY = 0.5  # 请求间隔，避免触发限制

class OzonSyncService:
    """OZON商品验证和信息同步服务"""
    
    def __init__(self):
        self.client_id = config_service.get_config('ozon_client_id', '2962343')
        self.api_key = config_service.get_config('ozon_api_key', '76451913-5cdc-4f4e-96e0-b923b835ab12')
        self.base_url = OZON_BASE_URL
        
        # 验证API凭据
        if not self.client_id or not self.api_key:
            logger.error("OZON API凭据未配置")
            raise ValueError("OZON API凭据未配置")
    
    def _get_headers(self) -> Dict[str, str]:
        """获取API请求头"""
        return {
            "Client-Id": self.client_id,
            "Api-Key": self.api_key,
            "Content-Type": "application/json"
        }
    
    def _make_request(self, url: str, payload: Dict = None, method: str = "POST") -> Optional[Dict]:
        """发送API请求"""
        try:
            headers = self._get_headers()
            
            if method.upper() == "POST":
                response = requests.post(url, headers=headers, json=payload, timeout=30)
            else:
                response = requests.get(url, headers=headers, params=payload, timeout=30)
            
            logger.info(f"API请求: {method} {url}, 状态码: {response.status_code}")
            
            if response.status_code == 200:
                return response.json()
            else:
                logger.error(f"API请求失败: {response.status_code}, 响应: {response.text}")
                return None
                
        except Exception as e:
            logger.error(f"API请求异常: {e}")
            return None
    
    def get_product_list(self, limit: int = 1000, last_id: str = "", 
                        offer_id: str = None) -> Tuple[List[Dict], str]:
        """
        获取商品列表
        
        Args:
            limit: 每页商品数量，最大1000
            last_id: 分页参数，上次查询的最后一个商品ID
            offer_id: 可选，筛选特定的offer_id
            
        Returns:
            Tuple[商品列表, 下一页的last_id]
        """
        url = f"{self.base_url}/v3/product/list"
        
        payload = {
            "filter": {
                "visibility": "ALL"
            },
            "last_id": last_id,
            "limit": min(limit, MAX_PRODUCTS_PER_REQUEST)
        }
        
        # 如果指定了offer_id，添加筛选条件
        if offer_id:
            payload["filter"]["offer_id"] = [offer_id]
        
        logger.info(f"获取商品列表: limit={limit}, last_id={last_id}, offer_id={offer_id}")
        
        response = self._make_request(url, payload)
        if not response:
            return [], ""
        
        result = response.get('result', {})
        items = result.get('items', [])
        next_last_id = result.get('last_id', "")
        
        logger.info(f"获取到 {len(items)} 个商品，下一页last_id: {next_last_id}")
        
        # 添加请求延迟
        time.sleep(REQUEST_DELAY)
        
        return items, next_last_id
    
    def get_all_products(self, offer_id: str = None) -> List[Dict]:
        """
        获取所有商品列表（自动分页）
        
        Args:
            offer_id: 可选，筛选特定的offer_id
            
        Returns:
            所有商品的列表
        """
        all_products = []
        last_id = ""
        page = 1
        
        logger.info(f"开始获取所有商品列表，筛选offer_id: {offer_id}")
        
        while True:
            logger.info(f"获取第 {page} 页商品...")
            
            products, next_last_id = self.get_product_list(
                limit=MAX_PRODUCTS_PER_REQUEST,
                last_id=last_id,
                offer_id=offer_id
            )
            
            if not products:
                logger.info("没有更多商品，分页结束")
                break
            
            all_products.extend(products)
            logger.info(f"第 {page} 页获取到 {len(products)} 个商品，累计: {len(all_products)}")
            
            # 如果没有下一页，结束循环
            if not next_last_id or next_last_id == last_id:
                logger.info("已到达最后一页")
                break
            
            last_id = next_last_id
            page += 1
        
        logger.info(f"总共获取到 {len(all_products)} 个商品")
        return all_products
    
    def get_product_info_batch(self, product_ids: List[int]) -> List[Dict]:
        """
        批量获取商品详细信息
        
        Args:
            product_ids: 商品ID列表，最多1000个
            
        Returns:
            商品详细信息列表
        """
        if not product_ids:
            return []
        
        # 确保不超过API限制
        if len(product_ids) > MAX_PRODUCTS_PER_REQUEST:
            logger.warning(f"商品ID数量 {len(product_ids)} 超过限制 {MAX_PRODUCTS_PER_REQUEST}，将截取")
            product_ids = product_ids[:MAX_PRODUCTS_PER_REQUEST]
        
        url = f"{self.base_url}/v3/product/info/list"
        payload = {
            "product_id": product_ids
        }
        
        logger.info(f"批量获取 {len(product_ids)} 个商品的详细信息")
        
        response = self._make_request(url, payload)
        if not response:
            return []
        
        result = response.get('result', {})
        items = result.get('items', [])
        
        logger.info(f"获取到 {len(items)} 个商品的详细信息")
        
        # 添加请求延迟
        time.sleep(REQUEST_DELAY)
        
        return items
    
    def get_all_product_info(self, product_ids: List[int]) -> List[Dict]:
        """
        获取所有商品的详细信息（自动分批处理）
        
        Args:
            product_ids: 所有商品ID列表
            
        Returns:
            所有商品的详细信息列表
        """
        all_product_info = []
        total_products = len(product_ids)
        
        logger.info(f"开始获取 {total_products} 个商品的详细信息")
        
        # 分批处理
        for i in range(0, total_products, MAX_PRODUCTS_PER_REQUEST):
            batch_ids = product_ids[i:i + MAX_PRODUCTS_PER_REQUEST]
            batch_num = (i // MAX_PRODUCTS_PER_REQUEST) + 1
            total_batches = (total_products + MAX_PRODUCTS_PER_REQUEST - 1) // MAX_PRODUCTS_PER_REQUEST
            
            logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_ids)} 个商品")
            
            batch_info = self.get_product_info_batch(batch_ids)
            all_product_info.extend(batch_info)
            
            logger.info(f"第 {batch_num} 批完成，累计获取 {len(all_product_info)} 个商品信息")
        
        logger.info(f"总共获取到 {len(all_product_info)} 个商品的详细信息")
        return all_product_info

    def create_sync_log(self, sync_type: str) -> int:
        """创建同步日志记录"""
        query = """
        INSERT INTO ozon_sync_logs (sync_type, start_time, status)
        VALUES (%s, %s, 'running')
        """

        # 执行插入操作
        db_manager.execute_query(
            query,
            (sync_type, datetime.now()),
            fetch=False
        )

        # 获取刚插入的记录ID
        last_id_query = "SELECT LAST_INSERT_ID() as id"
        result = db_manager.execute_query(last_id_query)

        if result and len(result) > 0:
            sync_log_id = result[0]['id']
            logger.info(f"创建同步日志记录: {sync_type}, ID: {sync_log_id}")
            return sync_log_id
        else:
            logger.error(f"创建同步日志记录失败: {sync_type}")
            return 0

    def update_sync_log(self, log_id: int, status: str, total_products: int = 0,
                       synced_products: int = 0, failed_products: int = 0,
                       error_message: str = None):
        """更新同步日志记录"""
        query = """
        UPDATE ozon_sync_logs
        SET end_time = %s, status = %s, total_products = %s,
            synced_products = %s, failed_products = %s, error_message = %s,
            updated_at = %s
        WHERE id = %s
        """

        result = db_manager.execute_query(
            query,
            (datetime.now(), status, total_products, synced_products,
             failed_products, error_message, datetime.now(), log_id),
            fetch=False
        )

        if result:
            logger.info(f"更新同步日志记录: ID={log_id}, 状态={status}")
        else:
            logger.error(f"更新同步日志记录失败: ID={log_id}")

    def sync_product_to_database(self, ozon_product: Dict, product_info: Dict = None) -> bool:
        """
        将OZON商品信息同步到本地数据库

        Args:
            ozon_product: OZON商品基本信息
            product_info: OZON商品详细信息（可选）

        Returns:
            是否同步成功
        """
        try:
            # 提取关键信息
            product_id = ozon_product.get('product_id')
            offer_id = ozon_product.get('offer_id')

            if not product_id or not offer_id:
                logger.warning(f"商品信息不完整: product_id={product_id}, offer_id={offer_id}")
                return False

            # 从详细信息中提取更多数据
            ozon_name = None
            ozon_price = None
            ozon_sku = None
            ozon_status = None
            ozon_visible = None

            if product_info:
                ozon_name = product_info.get('name', '')

                # 提取价格信息
                sources = product_info.get('sources', [])
                if sources:
                    source = sources[0]
                    ozon_sku = source.get('sku')

                # 提取状态信息
                status_info = product_info.get('status', {})
                ozon_status = status_info.get('state')

                # 提取可见性信息
                visibility = product_info.get('visibility')
                ozon_visible = visibility == 'VISIBLE' if visibility else None

                # 提取价格
                price_info = product_info.get('price')
                if price_info:
                    ozon_price = price_info.get('price')

            # 查找本地对应的商品（通过item_code匹配offer_id）
            local_product_query = """
            SELECT id FROM product_info WHERE item_code = %s
            """

            local_products = db_manager.execute_query(local_product_query, (offer_id,))

            if not local_products:
                logger.info(f"本地未找到对应商品: offer_id={offer_id}")
                return False

            local_product_id = local_products[0]['id']

            # 更新本地商品的OZON信息
            update_query = """
            UPDATE product_info
            SET ozon_product_id = %s, ozon_sku = %s, ozon_name = %s,
                ozon_price = %s, ozon_status = %s, ozon_visible = %s,
                sync_status = 'synced', last_sync_time = %s,
                sync_error_message = NULL
            WHERE id = %s
            """

            result = db_manager.execute_query(
                update_query,
                (product_id, ozon_sku, ozon_name, ozon_price, ozon_status,
                 ozon_visible, datetime.now(), local_product_id),
                fetch=False
            )

            if result:
                logger.info(f"同步成功: 本地ID={local_product_id}, OZON ID={product_id}, offer_id={offer_id}")
                return True
            else:
                logger.error(f"数据库更新失败: offer_id={offer_id}")
                return False

        except Exception as e:
            logger.error(f"同步商品到数据库失败: {e}")
            return False

    def mark_product_not_found(self, item_code: str, error_message: str = None):
        """标记商品在OZON中未找到"""
        query = """
        UPDATE product_info
        SET sync_status = 'not_found', last_sync_time = %s,
            sync_error_message = %s
        WHERE item_code = %s
        """

        result = db_manager.execute_query(
            query,
            (datetime.now(), error_message or "商品在OZON中未找到", item_code),
            fetch=False
        )

        if result:
            logger.info(f"标记商品未找到: item_code={item_code}")
        else:
            logger.error(f"标记商品未找到失败: item_code={item_code}")

    def full_sync_products(self, target_offer_id: str = None) -> Dict:
        """
        完整的商品同步流程

        Args:
            target_offer_id: 可选，只同步特定的offer_id（如Hotwav-Note12）

        Returns:
            同步结果报告
        """
        sync_log_id = self.create_sync_log('product_list')

        try:
            logger.info("=" * 60)
            logger.info("🚀 开始完整的OZON商品同步流程")
            logger.info("=" * 60)

            # 第一步：获取所有商品列表
            logger.info("📋 第一步：获取OZON商品列表...")
            all_products = self.get_all_products(offer_id=target_offer_id)

            if not all_products:
                error_msg = "未获取到任何OZON商品"
                logger.error(error_msg)
                self.update_sync_log(sync_log_id, 'failed', error_message=error_msg)
                return {
                    'success': False,
                    'error': error_msg,
                    'total_products': 0,
                    'synced_products': 0,
                    'failed_products': 0
                }

            logger.info(f"✅ 获取到 {len(all_products)} 个OZON商品")

            # 第二步：提取product_id列表
            product_ids = [p.get('product_id') for p in all_products if p.get('product_id')]
            logger.info(f"📋 提取到 {len(product_ids)} 个有效的product_id")

            # 第三步：获取详细商品信息
            logger.info("📋 第二步：获取商品详细信息...")
            all_product_info = self.get_all_product_info(product_ids)

            # 创建product_id到详细信息的映射
            product_info_map = {
                info.get('id'): info for info in all_product_info
            }

            logger.info(f"✅ 获取到 {len(all_product_info)} 个商品的详细信息")

            # 第四步：同步到数据库
            logger.info("📋 第三步：同步商品信息到数据库...")

            synced_count = 0
            failed_count = 0

            for ozon_product in all_products:
                product_id = ozon_product.get('product_id')
                offer_id = ozon_product.get('offer_id')

                logger.info(f"🔄 同步商品: offer_id={offer_id}, product_id={product_id}")

                # 获取对应的详细信息
                product_info = product_info_map.get(product_id)

                # 同步到数据库
                if self.sync_product_to_database(ozon_product, product_info):
                    synced_count += 1
                    logger.info(f"✅ 同步成功: {offer_id}")
                else:
                    failed_count += 1
                    logger.warning(f"❌ 同步失败: {offer_id}")

            # 第四步：同步商品评分
            logger.info("📋 第四步：同步商品评分...")
            rating_count = self._batch_sync_ratings()
            logger.info(f"✅ 评分同步完成: {rating_count} 个商品")

            # 第五步：标记未找到的商品
            if not target_offer_id:  # 只有在全量同步时才标记未找到的商品
                logger.info("📋 第五步：标记未找到的商品...")
                self.mark_missing_products(all_products)

            # 更新同步日志
            self.update_sync_log(
                sync_log_id,
                'completed',
                total_products=len(all_products),
                synced_products=synced_count,
                failed_products=failed_count
            )

            logger.info("=" * 60)
            logger.info("🎉 OZON商品同步完成")
            logger.info(f"📊 总商品数: {len(all_products)}")
            logger.info(f"✅ 同步成功: {synced_count}")
            logger.info(f"❌ 同步失败: {failed_count}")
            logger.info(f"⭐ 评分同步: {rating_count}")
            logger.info("=" * 60)

            return {
                'success': True,
                'total_products': len(all_products),
                'synced_products': synced_count,
                'failed_products': failed_count,
                'rating_count': rating_count,
                'sync_log_id': sync_log_id
            }

        except Exception as e:
            error_msg = f"同步流程异常: {e}"
            logger.error(error_msg)
            self.update_sync_log(sync_log_id, 'failed', error_message=error_msg)

            return {
                'success': False,
                'error': error_msg,
                'total_products': 0,
                'synced_products': 0,
                'failed_products': 0,
                'sync_log_id': sync_log_id
            }

    def mark_missing_products(self, ozon_products: List[Dict]):
        """标记在OZON中缺失的本地商品"""
        try:
            # 获取所有OZON中的offer_id
            ozon_offer_ids = {p.get('offer_id') for p in ozon_products if p.get('offer_id')}

            # 获取所有本地商品的item_code
            local_query = "SELECT item_code FROM product_info WHERE item_code IS NOT NULL"
            local_products = db_manager.execute_query(local_query)

            if not local_products:
                logger.info("本地没有商品需要检查")
                return

            local_item_codes = {p['item_code'] for p in local_products}

            # 找出在本地但不在OZON中的商品
            missing_in_ozon = local_item_codes - ozon_offer_ids

            logger.info(f"发现 {len(missing_in_ozon)} 个本地商品在OZON中缺失")

            # 标记这些商品
            for item_code in missing_in_ozon:
                self.mark_product_not_found(item_code, "商品在OZON中未找到")

        except Exception as e:
            logger.error(f"标记缺失商品失败: {e}")

    def validate_specific_product(self, item_code: str) -> Dict:
        """
        验证特定商品的上传状态

        Args:
            item_code: 商品代码（如Hotwav-Note12）

        Returns:
            验证结果
        """
        logger.info(f"🔍 验证特定商品: {item_code}")

        try:
            # 获取OZON中的商品信息
            ozon_products = self.get_all_products(offer_id=item_code)

            if not ozon_products:
                logger.warning(f"❌ 商品 {item_code} 在OZON中未找到")
                self.mark_product_not_found(item_code, "商品在OZON中未找到")

                return {
                    'success': False,
                    'found_in_ozon': False,
                    'item_code': item_code,
                    'message': '商品在OZON中未找到'
                }

            # 获取第一个匹配的商品
            ozon_product = ozon_products[0]
            product_id = ozon_product.get('product_id')

            logger.info(f"✅ 在OZON中找到商品: {item_code}, product_id: {product_id}")

            # 获取详细信息
            product_info_list = self.get_product_info_batch([product_id])
            product_info = product_info_list[0] if product_info_list else None

            # 同步到数据库
            sync_success = self.sync_product_to_database(ozon_product, product_info)

            # 准备返回结果
            result = {
                'success': True,
                'found_in_ozon': True,
                'item_code': item_code,
                'ozon_product_id': product_id,
                'sync_success': sync_success,
                'ozon_data': {
                    'product_id': product_id,
                    'offer_id': ozon_product.get('offer_id'),
                    'sku': product_info.get('sources', [{}])[0].get('sku') if product_info else None,
                    'name': product_info.get('name') if product_info else None,
                    'status': product_info.get('status', {}).get('state') if product_info else None,
                    'visible': product_info.get('visibility') == 'VISIBLE' if product_info else None
                }
            }

            if sync_success:
                result['message'] = f'商品 {item_code} 验证并同步成功'
                logger.info(f"✅ 商品 {item_code} 验证并同步成功")
            else:
                result['message'] = f'商品 {item_code} 在OZON中找到但同步失败'
                logger.warning(f"⚠️ 商品 {item_code} 在OZON中找到但同步失败")

            return result

        except Exception as e:
            error_msg = f"验证商品 {item_code} 失败: {e}"
            logger.error(error_msg)

            return {
                'success': False,
                'found_in_ozon': False,
                'item_code': item_code,
                'error': error_msg,
                'message': error_msg
            }

    def get_sync_report(self) -> Dict:
        """获取同步状态报告"""
        try:
            # 统计各种状态的商品数量
            status_query = """
            SELECT
                sync_status,
                COUNT(*) as count
            FROM product_info
            WHERE item_code IS NOT NULL
            GROUP BY sync_status
            """

            status_results = db_manager.execute_query(status_query)
            status_counts = {row['sync_status']: row['count'] for row in status_results}

            # 获取最近的同步日志
            recent_logs_query = """
            SELECT * FROM ozon_sync_logs
            ORDER BY created_at DESC
            LIMIT 10
            """

            recent_logs = db_manager.execute_query(recent_logs_query)

            # 获取已同步的商品详情
            synced_products_query = """
            SELECT item_code, ozon_product_id, ozon_name, ozon_status,
                   last_sync_time, sync_status
            FROM product_info
            WHERE sync_status = 'synced'
            ORDER BY last_sync_time DESC
            """

            synced_products = db_manager.execute_query(synced_products_query)

            return {
                'success': True,
                'status_summary': {
                    'pending': status_counts.get('pending', 0),
                    'synced': status_counts.get('synced', 0),
                    'failed': status_counts.get('failed', 0),
                    'not_found': status_counts.get('not_found', 0)
                },
                'recent_sync_logs': recent_logs,
                'synced_products': synced_products,
                'total_products': sum(status_counts.values())
            }

        except Exception as e:
            logger.error(f"获取同步报告失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

    def _batch_sync_ratings(self) -> int:
        """
        批量同步商品评分

        Returns:
            成功同步评分的商品数量
        """
        synced_count = 0

        try:
            # 获取所有有SKU但没有评分或评分过期的商品
            products_without_ratings = self.db.execute_query("""
                SELECT op.sku, op.offer_id, op.name
                FROM ozon_products op
                LEFT JOIN ozon_product_ratings opr ON op.sku = opr.sku
                WHERE op.sku IS NOT NULL
                AND (opr.sku IS NULL OR opr.last_sync_at < DATE_SUB(NOW(), INTERVAL 7 DAY))
                ORDER BY op.updated_at DESC
                LIMIT 20
            """)

            logger.info(f"找到 {len(products_without_ratings)} 个需要同步评分的商品")

            if not products_without_ratings:
                logger.info("没有需要同步评分的商品")
                return 0

            # 导入评分API服务
            from services.ozon_api_service import OzonApiService
            ozon_api_service = OzonApiService()

            for product in products_without_ratings:
                sku = product['sku']
                offer_id = product['offer_id']

                try:
                    logger.info(f"同步评分: {offer_id} (SKU: {sku})")

                    # 调用评分API
                    rating_data = ozon_api_service.get_product_rating_by_sku(sku, save_to_db=True)

                    if rating_data:
                        synced_count += 1
                        logger.info(f"✅ 评分同步成功: {offer_id}")
                    else:
                        logger.warning(f"⚠️  评分同步失败: {offer_id}")

                    # 避免API限流
                    import time
                    time.sleep(1)

                except Exception as e:
                    logger.error(f"评分同步异常: {offer_id} - {e}")

            logger.info(f"✅ 批量评分同步完成: 成功同步 {synced_count} 个商品评分")

        except Exception as e:
            logger.error(f"批量评分同步失败: {e}")

        return synced_count


# 全局OZON同步服务实例
ozon_sync_service = OzonSyncService()
