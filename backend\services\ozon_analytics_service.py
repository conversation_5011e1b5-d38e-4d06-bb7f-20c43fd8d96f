#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON数据分析服务
基于 /v1/analytics/data API 获取数据并存储到数据库
"""

import json
import logging
import requests
import uuid
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

from models.database import get_db_manager

logger = logging.getLogger(__name__)

class OzonAnalyticsService:
    """OZON数据分析服务"""
    
    def __init__(self):
        self.db_manager = get_db_manager()
        self.base_url = "https://api-seller.ozon.ru"
        self.api_path = "/v1/analytics/data"

        # 从环境变量读取OZON API配置
        self.client_id = os.getenv('OZON_CLIENT_ID')
        self.api_key = os.getenv('OZON_API_KEY')

        # 验证配置是否存在
        if not self.client_id or not self.api_key:
            logger.error("❌ OZON API配置缺失！请检查 .env 文件中的 OZON_CLIENT_ID 和 OZON_API_KEY")
            raise ValueError("OZON API配置缺失")

        self.headers = {
            'Content-Type': 'application/json',
            'Client-Id': self.client_id,
            'Api-Key': self.api_key
        }

        logger.info(f"🔧 OZON数据分析服务初始化完成")
        logger.info(f"📡 Client-ID: {self.client_id}")
        logger.info(f"🔑 API-Key: {self.api_key[:8]}...{self.api_key[-4:]}")  # 只显示部分密钥
    
    def json_serializer(self, obj):
        """JSON序列化处理函数"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")
    
    def call_ozon_analytics_api(self, date_from: str, date_to: str, 
                               metrics: List[str], dimensions: List[str],
                               filters: List[Dict] = None, 
                               sort: List[Dict] = None,
                               limit: int = 1000, offset: int = 0) -> Dict:
        """
        调用OZON数据分析API
        
        Args:
            date_from: 开始日期 (YYYY-MM-DD)
            date_to: 结束日期 (YYYY-MM-DD)
            metrics: 指标列表
            dimensions: 分组维度列表
            filters: 过滤器列表
            sort: 排序设置
            limit: 返回数据量限制
            offset: 分页偏移量
        
        Returns:
            API响应数据
        """
        try:
            url = f"{self.base_url}{self.api_path}"
            
            request_data = {
                "date_from": date_from,
                "date_to": date_to,
                "metrics": metrics,
                "dimension": dimensions,
                "filters": filters or [],
                "sort": sort or [],
                "limit": limit,
                "offset": offset
            }
            
            logger.info(f"🔄 调用OZON数据分析API")
            logger.info(f"📡 请求URL: {url}")
            logger.info(f"📊 查询参数: {json.dumps(request_data, ensure_ascii=False)}")
            
            response = requests.post(
                url,
                json=request_data,
                headers=self.headers,
                timeout=60
            )

            # 添加API调用后延迟，避免触发限流
            import time
            time.sleep(2)

            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ API调用成功")
                
                if 'result' in data and 'data' in data['result']:
                    result_data = data['result']['data']
                    totals = data['result'].get('totals', [])
                    timestamp = data.get('timestamp')
                    
                    logger.info(f"📈 返回数据条数: {len(result_data)}")
                    logger.info(f"📊 总计值: {totals}")
                    
                    return {
                        'success': True,
                        'data': result_data,
                        'totals': totals,
                        'timestamp': timestamp,
                        'query_params': request_data
                    }
                else:
                    logger.warning("⚠️ API返回数据格式异常")
                    return {
                        'success': False,
                        'error': 'API返回数据格式异常',
                        'raw_response': data
                    }
            else:
                # 如果是429限流错误，添加重试机制
                if response.status_code == 429:
                    logger.warning(f"⚠️ API限流 (429)，等待10秒后重试...")
                    time.sleep(10)

                    # 重试一次
                    retry_response = requests.post(
                        url,
                        json=request_data,
                        headers=self.headers,
                        timeout=60
                    )

                    if retry_response.status_code == 200:
                        data = retry_response.json()
                        logger.info(f"✅ 重试成功")

                        if 'result' in data and 'data' in data['result']:
                            result_data = data['result']['data']
                            totals = data['result'].get('totals', [])
                            timestamp = data.get('timestamp')

                            return {
                                'success': True,
                                'data': result_data,
                                'totals': totals,
                                'timestamp': timestamp,
                                'query_params': request_data
                            }

                error_msg = f"API请求失败: {response.status_code} - {response.text}"
                logger.error(f"❌ {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            error_msg = f"API调用异常: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }
    
    def save_daily_query_record(self, query_id: str, target_date: str,
                               query_params: Dict, api_response: Dict) -> bool:
        """
        保存单日查询记录到 ozon_analytics_queries 表

        Args:
            query_id: 查询ID
            target_date: 目标日期
            query_params: 查询参数
            api_response: API响应数据

        Returns:
            是否保存成功
        """
        try:
            if not self.db_manager:
                logger.error("❌ 数据库连接失败")
                return False

            # 提取查询参数
            metrics = query_params.get('metrics', [])
            dimensions = query_params.get('dimension', [])

            # 提取API响应信息
            api_timestamp_str = api_response.get('timestamp')
            api_timestamp = datetime.strptime(api_timestamp_str, '%Y-%m-%d %H:%M:%S') if api_timestamp_str else datetime.now()
            total_records = len(api_response.get('data', []))

            # 确定查询状态
            status = 'success' if api_response.get('success') else 'failed'
            error_message = api_response.get('error') if not api_response.get('success') else None

            # 插入查询记录
            insert_sql = """
            INSERT INTO ozon_analytics_queries (
                query_id, target_date, metrics, dimensions,
                api_timestamp, total_records, status, error_message, created_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, NOW()
            )
            """

            params = (
                query_id,
                target_date,
                json.dumps(metrics, ensure_ascii=False),
                json.dumps(dimensions, ensure_ascii=False),
                api_timestamp,
                total_records,
                status,
                error_message
            )

            result = self.db_manager.execute_query(insert_sql, params, fetch=False)

            if result:
                logger.info(f"✅ 查询记录保存成功: {query_id} ({target_date})")
                return True
            else:
                logger.error(f"❌ 查询记录保存失败: {query_id} ({target_date})")
                return False

        except Exception as e:
            logger.error(f"❌ 保存查询记录异常: {e}")
            return False

    def save_query_record(self, query_id: str, query_params: Dict,
                         api_response: Dict) -> bool:
        """
        保存查询记录到 ozon_analytics_queries 表
        
        Args:
            query_id: 查询ID
            query_params: 查询参数
            api_response: API响应数据
        
        Returns:
            是否保存成功
        """
        try:
            if not self.db_manager:
                logger.error("❌ 数据库连接失败")
                return False
            
            # 提取查询参数
            date_from = query_params.get('date_from')
            date_to = query_params.get('date_to')
            metrics = query_params.get('metrics', [])
            dimensions = query_params.get('dimension', [])
            filters = query_params.get('filters', [])
            sort_config = query_params.get('sort', [])
            limit_count = query_params.get('limit', 1000)
            offset_count = query_params.get('offset', 0)
            
            # 提取API响应信息
            api_timestamp_str = api_response.get('timestamp')
            api_timestamp = datetime.strptime(api_timestamp_str, '%Y-%m-%d %H:%M:%S') if api_timestamp_str else datetime.now()
            total_records = len(api_response.get('data', []))
            totals_data = api_response.get('totals', [])
            
            # 确定查询状态
            status = 'success' if api_response.get('success') else 'failed'
            error_message = api_response.get('error') if not api_response.get('success') else None
            
            # 插入查询记录
            insert_sql = """
            INSERT INTO ozon_analytics_queries (
                query_id, date_from, date_to, metrics, dimensions,
                filters, sort_config, limit_count, offset_count,
                api_timestamp, total_records, totals_data,
                status, error_message, created_at
            ) VALUES (
                %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW()
            )
            """
            
            params = (
                query_id,
                date_from,
                date_to,
                json.dumps(metrics, ensure_ascii=False),
                json.dumps(dimensions, ensure_ascii=False),
                json.dumps(filters, ensure_ascii=False),
                json.dumps(sort_config, ensure_ascii=False),
                limit_count,
                offset_count,
                api_timestamp,
                total_records,
                json.dumps(totals_data, ensure_ascii=False),
                status,
                error_message
            )
            
            result = self.db_manager.execute_query(insert_sql, params, fetch=False)
            
            if result:
                logger.info(f"✅ 查询记录保存成功: {query_id}")
                return True
            else:
                logger.error(f"❌ 查询记录保存失败: {query_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 保存查询记录异常: {e}")
            return False
    
    def _is_duplicate_record(self, dimension_id: str, data_date: str) -> bool:
        """
        检查是否为重复记录（SKU + 日期唯一键策略）

        Args:
            dimension_id: SKU ID
            data_date: 数据日期（YYYY-MM-DD）

        Returns:
            是否为重复记录
        """
        check_sql = """
        SELECT id FROM ozon_analytics_data
        WHERE dimension_id = %s
        AND data_date = %s
        LIMIT 1
        """

        existing_record = self.db_manager.execute_query(
            check_sql,
            (dimension_id, data_date)
        )

        return bool(existing_record)

    def _get_existing_record_id(self, dimension_id: str, data_date: str) -> Optional[int]:
        """
        获取现有记录的ID

        Args:
            dimension_id: SKU ID
            data_date: 数据日期（YYYY-MM-DD）

        Returns:
            现有记录的ID，如果不存在则返回None
        """
        check_sql = """
        SELECT id FROM ozon_analytics_data
        WHERE dimension_id = %s
        AND data_date = %s
        LIMIT 1
        """

        existing_record = self.db_manager.execute_query(
            check_sql,
            (dimension_id, data_date)
        )

        return existing_record[0]['id'] if existing_record else None

    def _update_existing_record(self, record_id: int, query_id: str, api_timestamp: datetime,
                               query_metrics: List[str], query_dimensions: List[str],
                               metric_values: Dict, dimension_name: str,
                               raw_dimensions: List, raw_metrics: List) -> bool:
        """
        更新现有记录

        Args:
            record_id: 记录ID
            query_id: 查询ID
            api_timestamp: API时间戳
            query_metrics: 查询指标
            query_dimensions: 查询维度
            metric_values: 指标值
            dimension_name: 维度名称
            raw_dimensions: 原始维度数据
            raw_metrics: 原始指标数据

        Returns:
            是否更新成功
        """
        try:
            import json

            update_sql = """
            UPDATE ozon_analytics_data SET
                query_id = %s,
                api_timestamp = %s,
                query_metrics = %s,
                query_dimensions = %s,
                dimension_name = %s,
                revenue = %s,
                ordered_units = %s,
                unknown_metric = %s,
                hits_view_search = %s,
                hits_view_pdp = %s,
                hits_view = %s,
                hits_tocart_search = %s,
                hits_tocart_pdp = %s,
                hits_tocart = %s,
                session_view_search = %s,
                session_view_pdp = %s,
                session_view = %s,
                conv_tocart_search = %s,
                conv_tocart_pdp = %s,
                conv_tocart = %s,
                returns_count = %s,
                cancellations = %s,
                delivered_units = %s,
                position_category = %s,
                postings = %s,
                postings_premium = %s,
                raw_dimensions = %s,
                raw_metrics = %s,
                updated_at = NOW()
            WHERE id = %s
            """

            params = (
                query_id,
                api_timestamp,
                json.dumps(query_metrics, ensure_ascii=False),
                json.dumps(query_dimensions, ensure_ascii=False),
                dimension_name,
                # 指标值
                metric_values.get('revenue'),
                metric_values.get('ordered_units'),
                metric_values.get('unknown_metric'),
                metric_values.get('hits_view_search'),
                metric_values.get('hits_view_pdp'),
                metric_values.get('hits_view'),
                metric_values.get('hits_tocart_search'),
                metric_values.get('hits_tocart_pdp'),
                metric_values.get('hits_tocart'),
                metric_values.get('session_view_search'),
                metric_values.get('session_view_pdp'),
                metric_values.get('session_view'),
                metric_values.get('conv_tocart_search'),
                metric_values.get('conv_tocart_pdp'),
                metric_values.get('conv_tocart'),
                metric_values.get('returns_count'),
                metric_values.get('cancellations'),
                metric_values.get('delivered_units'),
                metric_values.get('position_category'),
                metric_values.get('postings'),
                metric_values.get('postings_premium'),
                json.dumps(raw_dimensions, ensure_ascii=False),
                json.dumps(raw_metrics, ensure_ascii=False),
                record_id
            )

            result = self.db_manager.execute_query(update_sql, params, fetch=False)
            return bool(result)

        except Exception as e:
            logger.error(f"❌ 更新记录异常: {e}")
            return False

    def sync_daily_analytics_data(self, target_date: str, metrics: List[str] = None,
                                 dimension: str = 'sku') -> Dict:
        """
        同步单日分析数据

        Args:
            target_date: 目标日期（YYYY-MM-DD）
            metrics: 指标列表
            dimension: 维度类型

        Returns:
            同步结果
        """
        if not metrics:
            # 使用主人关注的核心指标（9个核心指标 + 5个补充指标 = 14个）
            metrics = [
                # 主人主要关注的核心指标（9个）
                'revenue', 'ordered_units',                    # 收入相关
                'hits_view_search', 'hits_view_pdp', 'hits_view',  # 浏览相关
                'hits_tocart',                                 # 转化相关
                'session_view_search', 'session_view_pdp', 'session_view',  # 🆕 会话相关

                # 补充的重要指标（5个）
                'delivered_units', 'returns',                 # 履约相关
                'position_category',                           # 🆕 分类排名
                'hits_tocart_search', 'hits_tocart_pdp'       # 详细转化数据
            ]

        try:
            # 生成查询ID
            query_id = str(uuid.uuid4())

            # 调用API获取单日数据（from和to都是同一天）
            api_response = self.call_ozon_analytics_api(
                date_from=target_date,
                date_to=target_date,
                metrics=metrics,
                dimensions=[dimension]
            )

            if not api_response.get('success'):
                return api_response

            # 保存查询记录
            query_saved = self.save_daily_query_record(
                query_id=query_id,
                target_date=target_date,
                query_params=api_response['query_params'],
                api_response=api_response
            )

            # 保存分析数据（按日存储）
            data_result = self.save_daily_analytics_data(
                query_id=query_id,
                target_date=target_date,
                api_response=api_response,
                query_params=api_response['query_params']
            )

            result = {
                'success': True,
                'query_id': query_id,
                'target_date': target_date,
                'api_timestamp': api_response.get('timestamp'),
                'metrics': metrics,
                'dimension': dimension,
                'total_fetched': len(api_response.get('data', [])),
                'inserted_count': data_result.get('inserted_count', 0),
                'updated_count': data_result.get('updated_count', 0),
                'error_count': data_result.get('error_count', 0),
                'query_saved': query_saved,
                'totals': api_response.get('totals', [])
            }

            logger.info(f"🎉 单日数据同步完成: {target_date}, 插入: {data_result.get('inserted_count', 0)}, 更新: {data_result.get('updated_count', 0)}")
            return result

        except Exception as e:
            error_msg = f"同步单日数据失败 ({target_date}): {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg,
                'target_date': target_date
            }

    def save_daily_analytics_data(self, query_id: str, target_date: str,
                                 api_response: Dict, query_params: Dict) -> Dict:
        """
        保存单日分析数据到数据库（按日存储策略）

        Args:
            query_id: 查询ID
            target_date: 目标日期（YYYY-MM-DD）
            api_response: API响应数据
            query_params: 查询参数

        Returns:
            保存结果
        """
        try:
            if not self.db_manager:
                logger.error("❌ 数据库连接失败")
                return {'success': False, 'error': '数据库连接失败'}

            data_list = api_response.get('data', [])
            if not data_list:
                logger.warning("⚠️ 没有数据需要保存")
                return {'success': True, 'inserted_count': 0, 'updated_count': 0, 'error_count': 0}

            # 提取查询元数据
            api_timestamp_str = api_response.get('timestamp')
            api_timestamp = datetime.strptime(api_timestamp_str, '%Y-%m-%d %H:%M:%S') if api_timestamp_str else datetime.now()
            query_metrics = query_params.get('metrics', [])
            query_dimensions = query_params.get('dimension', [])

            # 指标字段映射 - 完整的26个指标
            metric_field_mapping = {
                # 基础指标（所有卖家可用）
                'revenue': 'revenue',
                'ordered_units': 'ordered_units',

                # Premium指标（仅Premium订阅卖家可用）
                'unknown_metric': 'unknown_metric',
                'hits_view_search': 'hits_view_search',
                'hits_view_pdp': 'hits_view_pdp',
                'hits_view': 'hits_view',
                'hits_tocart_search': 'hits_tocart_search',
                'hits_tocart_pdp': 'hits_tocart_pdp',
                'hits_tocart': 'hits_tocart',
                'session_view_search': 'session_view_search',
                'session_view_pdp': 'session_view_pdp',
                'session_view': 'session_view',
                'conv_tocart_search': 'conv_tocart_search',
                'conv_tocart_pdp': 'conv_tocart_pdp',
                'conv_tocart': 'conv_tocart',
                'returns': 'returns_count',
                'cancellations': 'cancellations',
                'delivered_units': 'delivered_units',
                'position_category': 'position_category',

                # 发货相关指标（Premium）
                'postings': 'postings',
                'postings_premium': 'postings_premium'
            }

            inserted_count = 0
            updated_count = 0
            error_count = 0

            for data_item in data_list:
                try:
                    dimensions = data_item.get('dimensions', [])
                    metrics = data_item.get('metrics', [])

                    if not dimensions:
                        logger.warning("⚠️ 数据项缺少维度信息，跳过")
                        error_count += 1
                        continue

                    # 处理多维度数据 - 目前处理第一个维度
                    primary_dimension = dimensions[0] if dimensions else {}
                    dimension_id = primary_dimension.get('id', '')
                    dimension_name = primary_dimension.get('name', '')

                    # 确定维度类型
                    dimension_type = self._determine_dimension_type(dimension_id, query_dimensions)

                    # 构建指标数据
                    metric_values = {}
                    for i, metric_name in enumerate(query_metrics):
                        if i < len(metrics) and metric_name in metric_field_mapping:
                            field_name = metric_field_mapping[metric_name]
                            metric_values[field_name] = metrics[i]

                    # 检查是否为重复记录（SKU + 日期唯一键策略）
                    existing_record_id = self._get_existing_record_id(dimension_id, target_date)

                    if existing_record_id:
                        # 更新现有记录
                        if self._update_existing_record(existing_record_id, query_id, api_timestamp,
                                                      query_metrics, query_dimensions, metric_values,
                                                      dimension_name, dimensions, metrics):
                            updated_count += 1
                            logger.debug(f"🔄 更新记录: SKU {dimension_id} 日期: {target_date}")
                        else:
                            error_count += 1
                            logger.error(f"❌ 更新记录失败: SKU {dimension_id} 日期: {target_date}")
                        continue

                    # 插入新记录（按日存储策略）
                    insert_sql = """
                    INSERT INTO ozon_analytics_data (
                        query_id, api_timestamp, data_date,
                        query_metrics, query_dimensions,
                        dimension_type, dimension_id, dimension_name, dimension_order,
                        revenue, ordered_units, unknown_metric,
                        hits_view_search, hits_view_pdp, hits_view,
                        hits_tocart_search, hits_tocart_pdp, hits_tocart,
                        session_view_search, session_view_pdp, session_view,
                        conv_tocart_search, conv_tocart_pdp, conv_tocart,
                        returns_count, cancellations, delivered_units, position_category,
                        postings, postings_premium,
                        raw_dimensions, raw_metrics,
                        created_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, NOW()
                    )
                    """

                    params = (
                        query_id,
                        api_timestamp,
                        target_date,  # 使用目标日期而不是日期范围
                        json.dumps(query_metrics, ensure_ascii=False),
                        json.dumps(query_dimensions, ensure_ascii=False),
                        dimension_type,
                        dimension_id,
                        dimension_name,
                        1,  # dimension_order
                        # 基础指标
                        metric_values.get('revenue'),
                        metric_values.get('ordered_units'),
                        metric_values.get('unknown_metric'),
                        # Premium展示指标
                        metric_values.get('hits_view_search'),
                        metric_values.get('hits_view_pdp'),
                        metric_values.get('hits_view'),
                        # Premium购物车指标
                        metric_values.get('hits_tocart_search'),
                        metric_values.get('hits_tocart_pdp'),
                        metric_values.get('hits_tocart'),
                        # Premium会话指标
                        metric_values.get('session_view_search'),
                        metric_values.get('session_view_pdp'),
                        metric_values.get('session_view'),
                        # Premium转化指标
                        metric_values.get('conv_tocart_search'),
                        metric_values.get('conv_tocart_pdp'),
                        metric_values.get('conv_tocart'),
                        # Premium履约指标
                        metric_values.get('returns_count'),
                        metric_values.get('cancellations'),
                        metric_values.get('delivered_units'),
                        metric_values.get('position_category'),
                        # Premium发货指标
                        metric_values.get('postings'),
                        metric_values.get('postings_premium'),
                        # 原始数据
                        json.dumps(dimensions, ensure_ascii=False),
                        json.dumps(metrics, ensure_ascii=False)
                    )

                    result = self.db_manager.execute_query(insert_sql, params, fetch=False)

                    if result:
                        inserted_count += 1
                        logger.debug(f"✅ 数据记录保存成功: {dimension_id} ({target_date})")
                    else:
                        error_count += 1
                        logger.error(f"❌ 数据记录保存失败: {dimension_id} ({target_date})")

                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ 处理数据项失败: {e}")
                    logger.error(f"数据项: {data_item}")

            logger.info(f"📊 单日数据保存完成 ({target_date}): 插入 {inserted_count} 条, 更新 {updated_count} 条, 失败 {error_count} 条")

            return {
                'success': True,
                'inserted_count': inserted_count,
                'updated_count': updated_count,
                'error_count': error_count,
                'total_processed': len(data_list)
            }

        except Exception as e:
            logger.error(f"❌ 保存单日分析数据异常: {e}")
            return {
                'success': False,
                'error': f'保存单日分析数据异常: {str(e)}'
            }

    def save_analytics_data(self, query_id: str, api_response: Dict,
                           query_params: Dict) -> Dict:
        """
        保存分析数据到 ozon_analytics_data 表
        
        Args:
            query_id: 查询ID
            api_response: API响应数据
            query_params: 查询参数
        
        Returns:
            保存结果
        """
        try:
            if not self.db_manager:
                logger.error("❌ 数据库连接失败")
                return {'success': False, 'error': '数据库连接失败'}
            
            data_list = api_response.get('data', [])
            if not data_list:
                logger.warning("⚠️ 没有数据需要保存")
                return {'success': True, 'inserted_count': 0, 'error_count': 0}
            
            # 提取查询元数据
            api_timestamp_str = api_response.get('timestamp')
            api_timestamp = datetime.strptime(api_timestamp_str, '%Y-%m-%d %H:%M:%S') if api_timestamp_str else datetime.now()
            query_date_from = query_params.get('date_from')
            query_date_to = query_params.get('date_to')
            query_metrics = query_params.get('metrics', [])
            query_dimensions = query_params.get('dimension', [])
            
            # 指标字段映射 - 完整的26个指标
            metric_field_mapping = {
                # 基础指标（所有卖家可用）
                'revenue': 'revenue',
                'ordered_units': 'ordered_units',

                # Premium指标（仅Premium订阅卖家可用）
                'unknown_metric': 'unknown_metric',
                'hits_view_search': 'hits_view_search',
                'hits_view_pdp': 'hits_view_pdp',
                'hits_view': 'hits_view',
                'hits_tocart_search': 'hits_tocart_search',
                'hits_tocart_pdp': 'hits_tocart_pdp',
                'hits_tocart': 'hits_tocart',
                'session_view_search': 'session_view_search',
                'session_view_pdp': 'session_view_pdp',
                'session_view': 'session_view',
                'conv_tocart_search': 'conv_tocart_search',
                'conv_tocart_pdp': 'conv_tocart_pdp',
                'conv_tocart': 'conv_tocart',
                'returns': 'returns_count',
                'cancellations': 'cancellations',
                'delivered_units': 'delivered_units',
                'position_category': 'position_category',

                # 广告相关指标（Premium）
                'adv_view_pdp': 'adv_view_pdp',
                'adv_view_search_category': 'adv_view_search_category',
                'adv_view_all': 'adv_view_all',
                'adv_sum_all': 'adv_sum_all',

                # 发货相关指标（Premium）
                'postings': 'postings',
                'postings_premium': 'postings_premium'
            }
            
            inserted_count = 0
            error_count = 0
            
            for data_item in data_list:
                try:
                    dimensions = data_item.get('dimensions', [])
                    metrics = data_item.get('metrics', [])
                    
                    if not dimensions:
                        logger.warning("⚠️ 数据项缺少维度信息，跳过")
                        error_count += 1
                        continue
                    
                    # 处理多维度数据 - 目前处理第一个维度
                    primary_dimension = dimensions[0] if dimensions else {}
                    dimension_id = primary_dimension.get('id', '')
                    dimension_name = primary_dimension.get('name', '')
                    
                    # 确定维度类型
                    dimension_type = self._determine_dimension_type(dimension_id, query_dimensions)

                    # 检查是否为重复记录
                    if self._is_duplicate_record(dimension_id, query_date_from, query_date_to, dimension_type):
                        logger.debug(f"⚠️ 跳过重复记录: {dimension_id} ({dimension_type}) 日期: {query_date_from} 到 {query_date_to}")
                        continue

                    # 构建指标数据
                    metric_values = {}
                    for i, metric_name in enumerate(query_metrics):
                        if i < len(metrics) and metric_name in metric_field_mapping:
                            field_name = metric_field_mapping[metric_name]
                            metric_values[field_name] = metrics[i]
                    
                    # 插入新记录（已通过上面的检查确保不重复）
                    insert_sql = """
                    INSERT INTO ozon_analytics_data (
                        query_id, api_timestamp, query_date_from, query_date_to,
                        query_metrics, query_dimensions,
                        dimension_type, dimension_id, dimension_name, dimension_order,
                        revenue, ordered_units, unknown_metric,
                        hits_view_search, hits_view_pdp, hits_view,
                        hits_tocart_search, hits_tocart_pdp, hits_tocart,
                        session_view_search, session_view_pdp, session_view,
                        conv_tocart_search, conv_tocart_pdp, conv_tocart,
                        returns_count, cancellations, delivered_units, position_category,
                        adv_view_pdp, adv_view_search_category, adv_view_all, adv_sum_all,
                        postings, postings_premium,
                        raw_dimensions, raw_metrics,
                        created_at
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s, %s, %s, NOW()
                    )
                    """
                    
                    params = (
                        query_id,
                        api_timestamp,
                        query_date_from,
                        query_date_to,
                        json.dumps(query_metrics, ensure_ascii=False),
                        json.dumps(query_dimensions, ensure_ascii=False),
                        dimension_type,
                        dimension_id,
                        dimension_name,
                        1,  # dimension_order
                        # 基础指标
                        metric_values.get('revenue'),
                        metric_values.get('ordered_units'),
                        metric_values.get('unknown_metric'),
                        # Premium展示指标
                        metric_values.get('hits_view_search'),
                        metric_values.get('hits_view_pdp'),
                        metric_values.get('hits_view'),
                        # Premium购物车指标
                        metric_values.get('hits_tocart_search'),
                        metric_values.get('hits_tocart_pdp'),
                        metric_values.get('hits_tocart'),
                        # Premium会话指标
                        metric_values.get('session_view_search'),
                        metric_values.get('session_view_pdp'),
                        metric_values.get('session_view'),
                        # Premium转化指标
                        metric_values.get('conv_tocart_search'),
                        metric_values.get('conv_tocart_pdp'),
                        metric_values.get('conv_tocart'),
                        # Premium履约指标
                        metric_values.get('returns_count'),
                        metric_values.get('cancellations'),
                        metric_values.get('delivered_units'),
                        metric_values.get('position_category'),
                        # Premium广告指标
                        metric_values.get('adv_view_pdp'),
                        metric_values.get('adv_view_search_category'),
                        metric_values.get('adv_view_all'),
                        metric_values.get('adv_sum_all'),
                        # Premium发货指标
                        metric_values.get('postings'),
                        metric_values.get('postings_premium'),
                        # 原始数据
                        json.dumps(dimensions, ensure_ascii=False),
                        json.dumps(metrics, ensure_ascii=False)
                    )
                    
                    result = self.db_manager.execute_query(insert_sql, params, fetch=False)
                    
                    if result:
                        inserted_count += 1
                        logger.debug(f"✅ 数据记录保存成功: {dimension_id}")
                    else:
                        error_count += 1
                        logger.error(f"❌ 数据记录保存失败: {dimension_id}")
                        
                except Exception as e:
                    error_count += 1
                    logger.error(f"❌ 处理数据项失败: {e}")
                    logger.error(f"数据项: {data_item}")
            
            logger.info(f"📊 数据保存完成: 成功 {inserted_count} 条, 失败 {error_count} 条")
            
            return {
                'success': True,
                'inserted_count': inserted_count,
                'error_count': error_count,
                'total_processed': len(data_list)
            }
            
        except Exception as e:
            logger.error(f"❌ 保存分析数据异常: {e}")
            return {
                'success': False,
                'error': f'保存分析数据异常: {str(e)}'
            }
    
    def _determine_dimension_type(self, dimension_id: str, query_dimensions: List[str]) -> str:
        """
        根据维度ID和查询维度确定维度类型
        
        Args:
            dimension_id: 维度ID
            query_dimensions: 查询的维度列表
        
        Returns:
            维度类型
        """
        # 如果查询维度只有一个，直接返回
        if len(query_dimensions) == 1:
            return query_dimensions[0]
        
        # 根据ID格式判断维度类型
        if dimension_id.isdigit() and len(dimension_id) > 5:
            return 'sku'
        elif '-' in dimension_id and len(dimension_id) == 10:
            return 'day'
        elif dimension_id in query_dimensions:
            return dimension_id
        else:
            # 默认返回第一个查询维度
            return query_dimensions[0] if query_dimensions else 'unknownDimension'


    def sync_date_range_analytics_data(self, date_from: str, date_to: str,
                                      metrics: List[str] = None,
                                      dimension: str = 'sku') -> Dict:
        """
        按日期范围循环同步分析数据（按日存储策略）

        Args:
            date_from: 开始日期（YYYY-MM-DD）
            date_to: 结束日期（YYYY-MM-DD）
            metrics: 指标列表
            dimension: 维度类型

        Returns:
            同步结果汇总
        """
        try:
            from datetime import datetime, timedelta

            start_date = datetime.strptime(date_from, '%Y-%m-%d')
            end_date = datetime.strptime(date_to, '%Y-%m-%d')

            if start_date > end_date:
                return {
                    'success': False,
                    'error': '开始日期不能晚于结束日期'
                }

            total_days = (end_date - start_date).days + 1
            logger.info(f"🔄 开始按日同步数据: {date_from} 到 {date_to} ({total_days} 天)")

            results = []
            total_inserted = 0
            total_updated = 0
            total_errors = 0

            current_date = start_date
            while current_date <= end_date:
                date_str = current_date.strftime('%Y-%m-%d')

                logger.info(f"📅 同步日期: {date_str}")

                # 同步单日数据
                daily_result = self.sync_daily_analytics_data(
                    target_date=date_str,
                    metrics=metrics,
                    dimension=dimension
                )

                results.append(daily_result)

                if daily_result.get('success'):
                    total_inserted += daily_result.get('inserted_count', 0)
                    total_updated += daily_result.get('updated_count', 0)
                    total_errors += daily_result.get('error_count', 0)

                    logger.info(f"✅ {date_str}: 插入 {daily_result.get('inserted_count', 0)} 条, 更新 {daily_result.get('updated_count', 0)} 条")
                else:
                    total_errors += 1
                    logger.error(f"❌ {date_str}: {daily_result.get('error', 'Unknown error')}")

                # 移动到下一天
                current_date += timedelta(days=1)

                # 添加延迟，避免API频率限制（增加到3秒）
                import time
                time.sleep(3)

            success_days = sum(1 for r in results if r.get('success'))
            failed_days = total_days - success_days

            summary = {
                'success': True,
                'date_range': f'{date_from} 到 {date_to}',
                'total_days': total_days,
                'success_days': success_days,
                'failed_days': failed_days,
                'total_inserted': total_inserted,
                'total_updated': total_updated,
                'total_errors': total_errors,
                'daily_results': results
            }

            logger.info(f"🎉 按日同步完成: {success_days}/{total_days} 天成功, 总插入 {total_inserted} 条")

            return summary

        except Exception as e:
            error_msg = f"按日期范围同步失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }


# 创建服务实例
ozon_analytics_service = OzonAnalyticsService()


def sync_ozon_analytics_data(date_from: str, date_to: str,
                            metrics: List[str], dimensions: List[str],
                            filters: List[Dict] = None,
                            sort: List[Dict] = None,
                            limit: int = 1000, offset: int = 0) -> Dict:
    """
    同步OZON数据分析数据的便捷函数（按日存储策略）

    Args:
        date_from: 开始日期
        date_to: 结束日期
        metrics: 指标列表
        dimensions: 维度列表
        filters: 过滤器（暂不支持）
        sort: 排序（暂不支持）
        limit: 限制数量（暂不支持）
        offset: 偏移量（暂不支持）

    Returns:
        同步结果
    """
    logger.info(f"🔄 开始按日同步OZON数据分析数据")
    logger.info(f"📅 日期范围: {date_from} 到 {date_to}")
    logger.info(f"📊 指标: {metrics}")
    logger.info(f"📋 维度: {dimensions}")

    try:
        # 使用新的按日同步策略
        dimension = dimensions[0] if dimensions else 'sku'

        result = ozon_analytics_service.sync_date_range_analytics_data(
            date_from=date_from,
            date_to=date_to,
            metrics=metrics,
            dimension=dimension
        )

        if result.get('success'):
            logger.info(f"🎉 按日同步完成: {result.get('success_days', 0)}/{result.get('total_days', 0)} 天成功")
        else:
            logger.error(f"❌ 按日同步失败: {result.get('error')}")

        return result

    except Exception as e:
        error_msg = f"同步OZON数据分析数据失败: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            'success': False,
            'error': error_msg
        }
