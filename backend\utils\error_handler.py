"""
统一错误处理模块
提供错误处理装饰器、异常类和日志记录功能
"""

import logging
import traceback
import functools
import os
from typing import Any, Dict, Optional, Callable
from flask import jsonify, request
import time

# 创建日志目录
os.makedirs('logs', exist_ok=True)

# 配置日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/app.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class BaseAppException(Exception):
    """应用基础异常类"""
    
    def __init__(self, message: str, error_code: str = None, details: Dict = None):
        self.message = message
        self.error_code = error_code or 'UNKNOWN_ERROR'
        self.details = details or {}
        super().__init__(self.message)

class ValidationError(BaseAppException):
    """数据验证错误"""
    
    def __init__(self, message: str, field: str = None, details: Dict = None):
        super().__init__(message, 'VALIDATION_ERROR', details)
        self.field = field

class DatabaseError(BaseAppException):
    """数据库操作错误"""
    
    def __init__(self, message: str, query: str = None, details: Dict = None):
        super().__init__(message, 'DATABASE_ERROR', details)
        self.query = query

class ExternalServiceError(BaseAppException):
    """外部服务错误"""
    
    def __init__(self, message: str, service: str = None, details: Dict = None):
        super().__init__(message, 'EXTERNAL_SERVICE_ERROR', details)
        self.service = service

class FileOperationError(BaseAppException):
    """文件操作错误"""
    
    def __init__(self, message: str, file_path: str = None, details: Dict = None):
        super().__init__(message, 'FILE_OPERATION_ERROR', details)
        self.file_path = file_path

def handle_api_errors(func: Callable) -> Callable:
    """
    API错误处理装饰器
    统一处理API异常并返回标准格式的错误响应
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        request_id = getattr(request, 'request_id', 'unknown')
        
        try:
            # 记录请求开始
            logger.info(f"API请求开始: {request.method} {request.path} [ID: {request_id}]")
            
            # 执行原函数
            result = func(*args, **kwargs)
            
            # 记录请求成功
            duration = time.time() - start_time
            logger.info(f"API请求成功: {request.method} {request.path} [ID: {request_id}] 耗时: {duration:.3f}s")
            
            return result
            
        except ValidationError as e:
            duration = time.time() - start_time
            logger.warning(f"验证错误: {request.method} {request.path} [ID: {request_id}] 错误: {e.message} 耗时: {duration:.3f}s")
            
            return jsonify({
                'success': False,
                'error': e.message,
                'error_code': e.error_code,
                'field': getattr(e, 'field', None),
                'details': e.details,
                'request_id': request_id
            }), 400
            
        except DatabaseError as e:
            duration = time.time() - start_time
            logger.error(f"数据库错误: {request.method} {request.path} [ID: {request_id}] 错误: {e.message} 耗时: {duration:.3f}s")
            logger.error(f"查询语句: {getattr(e, 'query', 'N/A')}")
            
            return jsonify({
                'success': False,
                'error': 'Database operation failed',
                'error_code': e.error_code,
                'details': e.details,
                'request_id': request_id
            }), 500
            
        except ExternalServiceError as e:
            duration = time.time() - start_time
            logger.error(f"外部服务错误: {request.method} {request.path} [ID: {request_id}] 服务: {getattr(e, 'service', 'unknown')} 错误: {e.message} 耗时: {duration:.3f}s")
            
            return jsonify({
                'success': False,
                'error': f'External service error: {e.message}',
                'error_code': e.error_code,
                'service': getattr(e, 'service', None),
                'details': e.details,
                'request_id': request_id
            }), 502
            
        except FileOperationError as e:
            duration = time.time() - start_time
            logger.error(f"文件操作错误: {request.method} {request.path} [ID: {request_id}] 文件: {getattr(e, 'file_path', 'unknown')} 错误: {e.message} 耗时: {duration:.3f}s")
            
            return jsonify({
                'success': False,
                'error': f'File operation failed: {e.message}',
                'error_code': e.error_code,
                'file_path': getattr(e, 'file_path', None),
                'details': e.details,
                'request_id': request_id
            }), 500
            
        except BaseAppException as e:
            duration = time.time() - start_time
            logger.error(f"应用错误: {request.method} {request.path} [ID: {request_id}] 错误: {e.message} 耗时: {duration:.3f}s")
            
            return jsonify({
                'success': False,
                'error': e.message,
                'error_code': e.error_code,
                'details': e.details,
                'request_id': request_id
            }), 500
            
        except Exception as e:
            duration = time.time() - start_time
            error_trace = traceback.format_exc()
            logger.error(f"未处理异常: {request.method} {request.path} [ID: {request_id}] 错误: {str(e)} 耗时: {duration:.3f}s")
            logger.error(f"异常堆栈: {error_trace}")
            
            return jsonify({
                'success': False,
                'error': 'Internal server error',
                'error_code': 'INTERNAL_SERVER_ERROR',
                'details': {'message': str(e)} if logger.level <= logging.DEBUG else {},
                'request_id': request_id
            }), 500
    
    return wrapper

def log_function_call(func: Callable) -> Callable:
    """
    函数调用日志装饰器
    记录函数的调用和执行时间
    """
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        func_name = f"{func.__module__}.{func.__name__}"
        
        try:
            logger.debug(f"函数调用开始: {func_name}")
            result = func(*args, **kwargs)
            
            duration = time.time() - start_time
            logger.debug(f"函数调用成功: {func_name} 耗时: {duration:.3f}s")
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"函数调用失败: {func_name} 错误: {str(e)} 耗时: {duration:.3f}s")
            raise
    
    return wrapper

def validate_required_fields(data: Dict, required_fields: list) -> None:
    """
    验证必需字段
    
    Args:
        data: 要验证的数据字典
        required_fields: 必需字段列表
    
    Raises:
        ValidationError: 当缺少必需字段时
    """
    missing_fields = []
    
    for field in required_fields:
        if field not in data or data[field] is None or data[field] == '':
            missing_fields.append(field)
    
    if missing_fields:
        raise ValidationError(
            f"缺少必需字段: {', '.join(missing_fields)}",
            details={'missing_fields': missing_fields}
        )

def validate_field_types(data: Dict, field_types: Dict) -> None:
    """
    验证字段类型
    
    Args:
        data: 要验证的数据字典
        field_types: 字段类型映射 {field_name: expected_type}
    
    Raises:
        ValidationError: 当字段类型不匹配时
    """
    type_errors = []
    
    for field, expected_type in field_types.items():
        if field in data and data[field] is not None:
            if not isinstance(data[field], expected_type):
                type_errors.append({
                    'field': field,
                    'expected_type': expected_type.__name__,
                    'actual_type': type(data[field]).__name__
                })
    
    if type_errors:
        raise ValidationError(
            "字段类型错误",
            details={'type_errors': type_errors}
        )

def safe_execute(func: Callable, *args, default=None, **kwargs) -> Any:
    """
    安全执行函数，捕获异常并返回默认值
    
    Args:
        func: 要执行的函数
        *args: 函数参数
        default: 异常时返回的默认值
        **kwargs: 函数关键字参数
    
    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        logger.warning(f"安全执行失败: {func.__name__} 错误: {str(e)}")
        return default

class ErrorCollector:
    """错误收集器，用于批量处理时收集错误"""
    
    def __init__(self):
        self.errors = []
        self.warnings = []
    
    def add_error(self, message: str, context: Dict = None):
        """添加错误"""
        self.errors.append({
            'message': message,
            'context': context or {},
            'timestamp': time.time()
        })
    
    def add_warning(self, message: str, context: Dict = None):
        """添加警告"""
        self.warnings.append({
            'message': message,
            'context': context or {},
            'timestamp': time.time()
        })
    
    def has_errors(self) -> bool:
        """是否有错误"""
        return len(self.errors) > 0
    
    def has_warnings(self) -> bool:
        """是否有警告"""
        return len(self.warnings) > 0
    
    def get_summary(self) -> Dict:
        """获取错误摘要"""
        return {
            'error_count': len(self.errors),
            'warning_count': len(self.warnings),
            'errors': self.errors,
            'warnings': self.warnings
        }
    
    def clear(self):
        """清空错误和警告"""
        self.errors.clear()
        self.warnings.clear()
