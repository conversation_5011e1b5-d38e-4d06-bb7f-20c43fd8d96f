"""
新版图片服务 - 不依赖image_product_mapping表
直接基于product_info表进行图片匹配和上传
"""

import os
import re
import logging
from typing import List, Dict, Optional
from difflib import SequenceMatcher

from models.database import get_db_manager

logger = logging.getLogger(__name__)


class ImageServiceNew:
    """新版图片服务 - 简化版本"""
    
    def __init__(self):
        self.logger = logger
        
    def extract_model_name_from_filename(self, filename: str) -> str:
        """从文件名提取设备型号（简化版本）"""
        try:
            # 去除文件扩展名
            base_name = os.path.splitext(filename)[0]
            
            # 移除"_数字"部分和"LCD"字样
            clean_name = re.sub(r'_\d+$', '', base_name).replace("LCD", "").strip()
            
            # 标准化空格和下划线
            device_name = re.sub(r'[_\s]+', ' ', clean_name).strip()
            
            logger.info(f"🔧 文件名解析: '{filename}' -> '{device_name}'")
            return device_name
            
        except Exception as e:
            logger.error(f"提取型号名称失败 {filename}: {e}")
            return filename
    
    def find_matching_product_direct(self, model_name: str) -> Optional[Dict]:
        """
        直接在product_info表中查找最佳匹配的产品
        
        Args:
            model_name: 从文件名提取的型号名称
            
        Returns:
            最佳匹配的产品信息，如果没有找到返回None
        """
        try:
            clean_model_name = model_name.strip()
            if not clean_model_name:
                return None
                
            logger.info(f"🔍 直接匹配产品: '{clean_model_name}'")
            
            db_manager = get_db_manager()
            if not db_manager:
                logger.error("无法获取数据库管理器")
                return None
            
            # 生成搜索变体
            search_variants = self._generate_search_variants(clean_model_name)
            logger.info(f"🔧 搜索变体: {search_variants}")
            
            best_match = None
            best_confidence = 0.0
            
            # 按优先级搜索
            for variant in search_variants:
                # 1. 精确匹配 model_name
                match = self._search_exact_match(db_manager, 'model_name', variant)
                if match and match['confidence'] > best_confidence:
                    best_match = match
                    best_confidence = match['confidence']
                    if best_confidence >= 1.0:  # 完美匹配，直接返回
                        break
                
                # 2. 精确匹配 item_code
                match = self._search_exact_match(db_manager, 'item_code', variant)
                if match and match['confidence'] > best_confidence:
                    best_match = match
                    best_confidence = match['confidence']
                    if best_confidence >= 1.0:
                        break
            
            # 如果没有精确匹配，尝试模糊匹配
            if best_confidence < 0.9:
                for variant in search_variants:
                    match = self._search_fuzzy_match(db_manager, variant)
                    if match and match['confidence'] > best_confidence:
                        best_match = match
                        best_confidence = match['confidence']
            
            if best_match:
                logger.info(f"✅ 找到最佳匹配: ID={best_match['id']}, 型号='{best_match['model_name']}', 置信度={best_match['confidence']:.2f}")
            else:
                logger.warning(f"❌ 未找到匹配的产品: '{clean_model_name}'")
                
            return best_match
            
        except Exception as e:
            logger.error(f"直接匹配产品失败: {e}")
            return None
    
    def _generate_search_variants(self, model_name: str) -> List[str]:
        """生成搜索变体"""
        variants = [model_name]  # 原始名称
        
        # 添加连字符版本
        dash_version = model_name.replace(' ', '-')
        if dash_version != model_name:
            variants.append(dash_version)
            
        # 添加无空格版本  
        no_space_version = model_name.replace(' ', '')
        if no_space_version != model_name:
            variants.append(no_space_version)
            
        # 添加下划线版本
        underscore_version = model_name.replace(' ', '_')
        if underscore_version != model_name:
            variants.append(underscore_version)
            
        return list(set(variants))  # 去重
    
    def _search_exact_match(self, db_manager, field_name: str, search_value: str) -> Optional[Dict]:
        """精确匹配搜索"""
        try:
            query = f"SELECT id, model_name, item_code, main_image_url, image_upload_status FROM product_info WHERE {field_name} = %s"
            results = db_manager.execute_query(query, (search_value,))
            
            if results and len(results) > 0:
                result = results[0]  # 取第一个结果
                return {
                    'id': result['id'],
                    'model_name': result['model_name'],
                    'item_code': result['item_code'],
                    'main_image_url': result.get('main_image_url', ''),
                    'image_upload_status': result.get('image_upload_status', 'none'),
                    'confidence': 1.0,
                    'match_method': f'exact_{field_name}'
                }
            return None
            
        except Exception as e:
            logger.error(f"精确匹配搜索失败 {field_name}: {e}")
            return None
    
    def _search_fuzzy_match(self, db_manager, search_value: str) -> Optional[Dict]:
        """模糊匹配搜索"""
        try:
            # 搜索包含关键词的产品
            query = """
            SELECT id, model_name, item_code, main_image_url, image_upload_status 
            FROM product_info 
            WHERE model_name LIKE %s OR item_code LIKE %s
            ORDER BY 
                CASE 
                    WHEN model_name = %s THEN 1
                    WHEN item_code = %s THEN 2
                    WHEN model_name LIKE %s THEN 3
                    WHEN item_code LIKE %s THEN 4
                    ELSE 5
                END
            LIMIT 5
            """
            
            like_pattern = f'%{search_value}%'
            results = db_manager.execute_query(query, (
                like_pattern, like_pattern, 
                search_value, search_value,
                like_pattern, like_pattern
            ))
            
            if results and len(results) > 0:
                best_match = None
                best_confidence = 0.0
                
                for result in results:
                    # 计算与model_name的相似度
                    model_similarity = SequenceMatcher(None, search_value.lower(), result['model_name'].lower()).ratio()
                    # 计算与item_code的相似度
                    item_similarity = SequenceMatcher(None, search_value.lower(), result['item_code'].lower()).ratio()
                    
                    # 取较高的相似度
                    confidence = max(model_similarity, item_similarity) * 0.8  # 模糊匹配最高0.8
                    
                    if confidence > best_confidence and confidence >= 0.6:  # 最低阈值0.6
                        best_confidence = confidence
                        best_match = {
                            'id': result['id'],
                            'model_name': result['model_name'],
                            'item_code': result['item_code'],
                            'main_image_url': result.get('main_image_url', ''),
                            'image_upload_status': result.get('image_upload_status', 'none'),
                            'confidence': confidence,
                            'match_method': 'fuzzy'
                        }
                
                return best_match
            return None
            
        except Exception as e:
            logger.error(f"模糊匹配搜索失败: {e}")
            return None
    
    def update_product_image_url(self, product_id: int, image_url: str) -> bool:
        """更新产品的图片URL"""
        try:
            logger.info(f"🔄 更新产品图片URL: product_id={product_id}, image_url={image_url}")
            
            db_manager = get_db_manager()
            if not db_manager:
                logger.error("无法获取数据库管理器")
                return False
            
            query = """
            UPDATE product_info 
            SET main_image_url = %s,
                image_upload_status = 'uploaded',
                last_image_update = CURRENT_TIMESTAMP
            WHERE id = %s
            """
            
            result = db_manager.execute_query(query, (image_url, product_id), fetch=False)
            
            if result and result > 0:
                logger.info(f"✅ 产品图片URL更新成功: product_id={product_id}")
                return True
            else:
                logger.error(f"❌ 产品图片URL更新失败: product_id={product_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新产品图片URL失败: {e}")
            return False


# 创建全局实例
image_service_new = ImageServiceNew()
