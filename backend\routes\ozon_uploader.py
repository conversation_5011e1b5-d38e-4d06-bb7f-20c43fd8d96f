#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON上传路由
提供产品批量上传到OZON平台的API接口
"""

import logging
import json
import uuid
import requests
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify, render_template
from services.ozon_service import ozon_service
from services.config_service import config_service
from services.product_service import product_service
from services.ozon_api_service import OzonApiService
from services.ozon_sync_scheduler import ozon_sync_scheduler
from models.database import db_manager, get_db_manager
from utils.i18n import t, get_current_language, SUPPORTED_LANGUAGES

logger = logging.getLogger(__name__)

# 创建蓝图
ozon_uploader_bp = Blueprint('ozon_uploader', __name__)

@ozon_uploader_bp.route('/')
def ozon_uploader_page():
    """OZON上传页面"""
    try:
        return render_template('ozon_uploader.html',
                             t=t,
                             get_current_language=get_current_language,
                             supported_languages=SUPPORTED_LANGUAGES)
    except Exception as e:
        logger.error(f"渲染OZON上传页面失败: {e}")
        return f"页面加载失败: {str(e)}", 500

@ozon_uploader_bp.route('/api/products-ready', methods=['GET'])
def api_get_products_ready():
    """获取准备上传的产品列表API（优化版本）"""
    try:
        limit = int(request.args.get('limit', 5))
        offset = int(request.args.get('offset', 0))
        search = request.args.get('search', '').strip()
        status_filter = request.args.get('status', '').strip()

        # 新增排序参数
        sort_by = request.args.get('sort_by', 'updated_at').strip()
        sort_order = request.args.get('sort_order', 'desc').strip()

        # 验证排序参数
        valid_sort_fields = ['last_ozon_upload', 'updated_at', 'created_at']
        valid_sort_orders = ['asc', 'desc']

        if sort_by not in valid_sort_fields:
            sort_by = 'updated_at'
        if sort_order not in valid_sort_orders:
            sort_order = 'desc'

        # 构建筛选条件
        filters = {
            'has_image': True,  # 只获取有图片的产品
        }

        if search:
            filters['search'] = search

        if status_filter:
            filters['status'] = status_filter

        # 添加排序参数到filters
        filters['sort_by'] = sort_by
        filters['sort_order'] = sort_order

        # 使用优化的产品服务获取数据（带缓存）
        result = product_service.get_products_ready_for_upload(
            limit=limit,
            offset=offset,
            filters=filters
        )

        # 获取统计信息（带缓存）
        stats = product_service.get_upload_statistics()

        return jsonify({
            'success': True,
            'data': {
                'products': result['products'],
                'total_count': result['total_count'],
                'returned_count': len(result['products']),
                'offset': offset,
                'limit': limit,
                'has_more': result['has_more'],
                'statistics': stats
            }
        })

    except Exception as e:
        logger.error(f"获取准备上传的产品失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取产品列表失败: {str(e)}'
        }), 500

@ozon_uploader_bp.route('/api/search-products', methods=['GET'])
def api_search_products():
    """搜索产品API"""
    try:
        query = request.args.get('query', '').strip()
        limit = int(request.args.get('limit', 10))

        if not query:
            return jsonify({
                'success': True,
                'data': {'products': []}
            })

        # 使用产品服务搜索
        products = product_service.search_products(query, limit)

        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'query': query,
                'count': len(products)
            }
        })

    except Exception as e:
        logger.error(f"搜索产品失败: {e}")
        return jsonify({
            'success': False,
            'error': f'搜索失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-products', methods=['GET'])
def api_get_ozon_products():
    """获取OZON商品列表API"""
    try:
        # 获取查询参数
        limit = min(int(request.args.get('limit', 10)), 100)
        offset = int(request.args.get('offset', 0))
        status_filter = request.args.get('status', '').strip()
        sync_status_filter = request.args.get('sync_status', '').strip()

        # 新增排序参数
        sort_by = request.args.get('sort_by', '').strip()
        sort_order = request.args.get('sort_order', '').strip().lower()

        # 参数验证
        if limit < 1:
            limit = 10
        if offset < 0:
            offset = 0

        # 验证排序参数
        valid_sort_fields = ['total_present', 'total_available', 'updated_at']
        valid_sort_orders = ['asc', 'desc']

        if sort_by and sort_by not in valid_sort_fields:
            sort_by = ''  # 无效字段，重置为默认
        if sort_order and sort_order not in valid_sort_orders:
            sort_order = ''  # 无效排序，重置为默认

        logger.info(f"获取OZON商品列表: limit={limit}, offset={offset}, status={status_filter}, sync_status={sync_status_filter}, sort_by={sort_by}, sort_order={sort_order}")

        # 构建筛选条件
        where_conditions = ["1=1"]
        params = {}

        if status_filter:
            if status_filter == 'active':
                where_conditions.append("NOT op.is_archived AND op.api_sync_status = 'success'")
            elif status_filter == 'archived':
                where_conditions.append("op.is_archived = 1")
            elif status_filter == 'discounted':
                where_conditions.append("op.is_discounted = 1")

        if sync_status_filter:
            where_conditions.append("op.api_sync_status = %(sync_status_filter)s")
            params['sync_status_filter'] = sync_status_filter

        # 构建WHERE子句，为ozon_products表添加op.前缀
        where_conditions_with_prefix = []
        for condition in where_conditions:
            if condition == "1=1":
                where_conditions_with_prefix.append("1=1")
            else:
                where_conditions_with_prefix.append(condition)  # 条件已经包含op.前缀

        where_clause = "WHERE " + " AND ".join(where_conditions_with_prefix)
        params.update({'limit': limit, 'offset': offset})

        # 构建排序子句
        order_clause = "ORDER BY op.updated_at DESC"  # 默认排序

        if sort_by and sort_order:
            if sort_by in ['total_present', 'total_available']:
                # 对于库存字段，需要从JSON中提取数据进行排序
                # 大多数商品只有一个stocks元素，所以简化处理
                if sort_by == 'total_present':
                    # 计算总库存：从第一个stocks元素提取present值，并转换为数字
                    sort_expression = 'CAST(COALESCE(JSON_EXTRACT(op.stocks_detail, \'$."stocks"[0]."present"\'), 0) AS SIGNED)'
                elif sort_by == 'total_available':
                    # 计算可用库存：present - reserved，并转换为数字
                    sort_expression = '''CAST((
                        COALESCE(JSON_EXTRACT(op.stocks_detail, '$."stocks"[0]."present"'), 0) -
                        COALESCE(JSON_EXTRACT(op.stocks_detail, '$."stocks"[0]."reserved"'), 0)
                    ) AS SIGNED)'''

                # 构建完整的排序子句：库存字段 + 更新时间作为次要排序
                order_clause = f"ORDER BY {sort_expression} {sort_order.upper()}, op.updated_at DESC"

            elif sort_by == 'updated_at':
                order_clause = f"ORDER BY op.updated_at {sort_order.upper()}"

        # 主查询 - 包含sku字段和评级信息
        query = f"""
            SELECT
                op.id, op.ozon_product_id, op.offer_id, op.local_product_id, op.name,
                op.price, op.old_price, op.marketing_price, op.currency_code,
                op.is_archived, op.is_discounted, op.has_fbo_stocks, op.has_fbs_stocks,
                op.has_discounted_fbo_item, op.discounted_fbo_stocks, op.stocks_detail,
                op.images, op.primary_image, op.api_sync_status,
                op.last_list_sync_at, op.last_detail_sync_at,
                op.ozon_created_at, op.ozon_updated_at, op.created_at, op.updated_at,
                op.sku,
                -- 评级信息
                opr.overall_rating, opr.rating_status, opr.content_quality_score,
                opr.content_quality_status, opr.title_score, opr.description_score,
                opr.images_score, opr.attributes_score, opr.category_score,
                opr.last_sync_at as rating_last_sync_at
            FROM ozon_products op
            LEFT JOIN product_info pi ON op.local_product_id = pi.id
            LEFT JOIN ozon_product_ratings opr ON op.sku = opr.sku
            {where_clause}
            {order_clause}
            LIMIT %(limit)s OFFSET %(offset)s
        """

        # 统计查询
        count_query = f"""
            SELECT
                COUNT(*) as total_count,
                SUM(CASE WHEN NOT op.is_archived AND op.api_sync_status = 'success' THEN 1 ELSE 0 END) as active_count,
                SUM(CASE WHEN op.is_archived THEN 1 ELSE 0 END) as archived_count,
                SUM(CASE WHEN op.is_discounted THEN 1 ELSE 0 END) as discounted_count,
                SUM(CASE WHEN op.has_fbo_stocks THEN 1 ELSE 0 END) as fbo_stocks_count,
                SUM(CASE WHEN op.has_fbs_stocks THEN 1 ELSE 0 END) as fbs_stocks_count
            FROM ozon_products op
            LEFT JOIN product_info pi ON op.local_product_id = pi.id
            {where_clause}
        """

        # 执行查询
        products_data = db_manager.execute_query(query, params)
        stats_data = db_manager.execute_query(count_query, {k: v for k, v in params.items() if k not in ['limit', 'offset']})

        # 处理商品数据
        products = []
        for row in products_data or []:
            # 处理JSON字段
            images = []
            primary_image = None

            try:
                if row['images']:
                    images = json.loads(row['images']) if isinstance(row['images'], str) else row['images']
                    if not isinstance(images, list):
                        images = []
            except (json.JSONDecodeError, TypeError):
                images = []

            try:
                if row['primary_image']:
                    primary_image = json.loads(row['primary_image']) if isinstance(row['primary_image'], str) else row['primary_image']
            except (json.JSONDecodeError, TypeError):
                primary_image = None

            # 格式化时间戳
            def format_timestamp(ts):
                if ts:
                    return ts.isoformat() + 'Z' if hasattr(ts, 'isoformat') else str(ts)
                return None

            # 构建评级信息
            rating_info = None
            if row['overall_rating'] is not None:
                rating_info = {
                    'overall_rating': float(row['overall_rating']) if row['overall_rating'] else None,
                    'rating_status': row['rating_status'],
                    'content_quality_score': float(row['content_quality_score']) if row['content_quality_score'] else None,
                    'content_quality_status': row['content_quality_status'],
                    'scores': {
                        'title_score': float(row['title_score']) if row['title_score'] else None,
                        'description_score': float(row['description_score']) if row['description_score'] else None,
                        'images_score': float(row['images_score']) if row['images_score'] else None,
                        'attributes_score': float(row['attributes_score']) if row['attributes_score'] else None,
                        'category_score': float(row['category_score']) if row['category_score'] else None
                    },
                    'last_sync_at': format_timestamp(row['rating_last_sync_at'])
                }

            # 构建库存信息
            stocks_info = None
            if row['stocks_detail']:
                try:
                    if isinstance(row['stocks_detail'], str):
                        stocks_data = json.loads(row['stocks_detail'])
                    else:
                        stocks_data = row['stocks_detail']

                    # 解析库存详情
                    stocks_list = stocks_data.get('stocks', [])
                    total_present = sum(stock.get('present', 0) for stock in stocks_list)
                    total_reserved = sum(stock.get('reserved', 0) for stock in stocks_list)

                    stocks_info = {
                        'has_stock': stocks_data.get('has_stock', False),
                        'total_present': total_present,  # 总可用库存
                        'total_reserved': total_reserved,  # 总预留库存
                        'total_available': total_present - total_reserved,  # 总可售库存
                        'stocks_by_source': {}  # 按来源分组的库存
                    }

                    # 按来源分组库存
                    for stock in stocks_list:
                        source = stock.get('source', 'unknown')
                        stocks_info['stocks_by_source'][source] = {
                            'sku': stock.get('sku'),
                            'present': stock.get('present', 0),
                            'reserved': stock.get('reserved', 0),
                            'available': stock.get('present', 0) - stock.get('reserved', 0)
                        }

                except (json.JSONDecodeError, TypeError, AttributeError):
                    stocks_info = None

            # 提取库存数据到顶级字段，方便前端使用
            total_present = stocks_info.get('total_present', 0) if stocks_info else 0
            total_reserved = stocks_info.get('total_reserved', 0) if stocks_info else 0
            total_available = stocks_info.get('total_available', 0) if stocks_info else 0

            product = {
                'id': row['id'],
                'ozon_product_id': row['ozon_product_id'],
                'offer_id': row['offer_id'],
                'local_product_id': row['local_product_id'],
                'sku': row['sku'],  # SKU字段
                'name': row['name'],
                'price': float(row['price']) if row['price'] else 0.0,
                'old_price': float(row['old_price']) if row['old_price'] else None,
                'marketing_price': float(row['marketing_price']) if row['marketing_price'] else None,
                'currency_code': row['currency_code'] or 'RUB',
                'is_archived': bool(row['is_archived']),
                'is_discounted': bool(row['is_discounted']),
                'has_fbo_stocks': bool(row['has_fbo_stocks']),
                'has_fbs_stocks': bool(row['has_fbs_stocks']),
                'has_discounted_fbo_item': bool(row['has_discounted_fbo_item']),
                'discounted_fbo_stocks': row['discounted_fbo_stocks'] or 0,
                # 库存信息 - 顶级字段方便前端直接使用
                'total_present': total_present,      # 总库存数量
                'total_reserved': total_reserved,    # 总预留数量
                'total_available': total_available,  # 总可售数量
                'stocks': stocks_info,  # 详细库存信息
                'images': images,
                'primary_image': primary_image,
                'api_sync_status': row['api_sync_status'],
                'last_list_sync_at': format_timestamp(row['last_list_sync_at']),
                'last_detail_sync_at': format_timestamp(row['last_detail_sync_at']),
                'ozon_created_at': format_timestamp(row['ozon_created_at']),
                'ozon_updated_at': format_timestamp(row['ozon_updated_at']),
                'created_at': format_timestamp(row['created_at']),
                'updated_at': format_timestamp(row['updated_at']),
                'rating': rating_info,  # 评级信息
                # 为了兼容性，也在顶级添加评分字段
                'overall_rating': float(row['overall_rating']) if row['overall_rating'] else None,
                'rating_status': row['rating_status'],
                'content_quality_score': float(row['content_quality_score']) if row['content_quality_score'] else None
            }
            products.append(product)

        # 处理统计数据
        stats = {
            'total_count': 0,
            'active_count': 0,
            'archived_count': 0,
            'discounted_count': 0,
            'fbo_stocks_count': 0,
            'fbs_stocks_count': 0
        }

        if stats_data and len(stats_data) > 0:
            stats_row = stats_data[0]
            stats = {
                'total_count': int(stats_row['total_count'] or 0),
                'active_count': int(stats_row['active_count'] or 0),
                'archived_count': int(stats_row['archived_count'] or 0),
                'discounted_count': int(stats_row['discounted_count'] or 0),
                'fbo_stocks_count': int(stats_row['fbo_stocks_count'] or 0),
                'fbs_stocks_count': int(stats_row['fbs_stocks_count'] or 0)
            }

        # 分页信息
        pagination = {
            'limit': limit,
            'offset': offset,
            'has_more': len(products) == limit,
            'next_offset': offset + limit if len(products) == limit else None
        }

        sort_info = f", 排序: {sort_by} {sort_order}" if sort_by and sort_order else ""
        logger.info(f"成功获取OZON商品列表: {len(products)} 个商品, 总计: {stats['total_count']}{sort_info}")

        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'statistics': stats,
                'pagination': pagination
            }
        })

    except ValueError as e:
        logger.error(f"参数错误: {e}")
        return jsonify({
            'success': False,
            'error': f'参数错误: {str(e)}'
        }), 400

    except Exception as e:
        logger.error(f"获取OZON商品列表失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取商品列表失败: {str(e)}'
        }), 500




@ozon_uploader_bp.route('/api/upload-to-ozon', methods=['POST'])
def api_upload_to_ozon():
    """批量上传产品到OZON API"""
    try:
        logger.info("🚀 收到OZON产品上传请求")
        data = request.get_json()
        product_ids = data.get('product_ids', [])
        upload_config = data.get('config', {})

        logger.info(f"📦 准备上传产品数量: {len(product_ids)}")
        logger.info(f"📋 产品IDs: {product_ids}")

        if not product_ids:
            logger.warning("❌ 上传失败: 没有提供产品ID列表")
            return jsonify({
                'success': False,
                'error': '没有提供产品ID列表'
            }), 400
        
        # 创建上传任务记录
        logger.info("📝 创建上传任务记录...")
        task_id = ozon_service.create_upload_task(product_ids, upload_config)
        if not task_id:
            logger.error("❌ 创建上传任务失败")
            return jsonify({
                'success': False,
                'error': '创建上传任务失败'
            }), 500

        logger.info(f"✅ 上传任务创建成功: {task_id}")
        
        # 获取产品详细信息
        logger.info("🔍 查询产品详细信息...")
        product_query = """
        SELECT * FROM product_info
        WHERE id IN ({})
        """.format(','.join(['%s'] * len(product_ids)))

        products = db_manager.execute_query(product_query, tuple(product_ids))

        if not products:
            logger.error("❌ 没有找到指定的产品")
            ozon_service.update_upload_task_status(task_id, 'failed', error_details={'error': '没有找到指定的产品'})
            return jsonify({
                'success': False,
                'error': '没有找到指定的产品'
            }), 404

        logger.info(f"✅ 找到 {len(products)} 个产品，准备上传")
        
        # 更新任务状态为上传中
        logger.info("📤 更新任务状态为上传中...")
        ozon_service.update_upload_task_status(task_id, 'uploading')

        # 更新产品状态为上传中
        logger.info("📤 更新产品状态为待上传...")
        ozon_service.update_product_upload_status(product_ids, 'pending')

        # 执行上传
        logger.info("🚀 开始执行OZON API上传...")
        ozon_task_id = ozon_service.upload_products_to_ozon(products)
        
        if ozon_task_id:
            logger.info(f"✅ OZON API上传成功! OZON任务ID: {ozon_task_id}")

            # 更新任务状态
            logger.info("📝 更新任务状态为成功...")
            ozon_service.update_upload_task_status(
                task_id,
                'success',
                ozon_task_id=ozon_task_id,
                success_count=len(products)
            )

            # 更新产品状态
            logger.info("📝 更新产品状态为已上传...")
            ozon_service.update_product_upload_status(product_ids, 'uploaded')

            logger.info(f"🎉 上传完成! 成功上传 {len(products)} 个产品")

            return jsonify({
                'success': True,
                'data': {
                    'task_id': task_id,
                    'ozon_task_id': ozon_task_id,
                    'uploaded_count': len(products),
                    'message': f'成功上传 {len(products)} 个产品到OZON'
                }
            })
        else:
            logger.error("❌ OZON API上传失败")

            # 上传失败
            ozon_service.update_upload_task_status(
                task_id,
                'failed',
                failed_count=len(products),
                error_details={'error': 'OZON API上传失败'}
            )

            # 更新产品状态
            ozon_service.update_product_upload_status(product_ids, 'failed')

            return jsonify({
                'success': False,
                'error': 'OZON API上传失败',
                'task_id': task_id
            }), 500
            
    except Exception as e:
        logger.error(f"批量上传到OZON失败: {e}")
        return jsonify({
            'success': False,
            'error': f'批量上传失败: {str(e)}'
        }), 500

@ozon_uploader_bp.route('/api/task-status/<task_id>', methods=['GET'])
def api_get_task_status(task_id):
    """查询上传任务状态API"""
    try:
        # 从数据库获取任务信息
        task_query = "SELECT * FROM ozon_upload_tasks WHERE task_id = %s"
        task_result = db_manager.execute_query(task_query, (task_id,))
        
        if not task_result:
            return jsonify({
                'success': False,
                'error': '任务不存在'
            }), 404
        
        task_info = task_result[0]
        
        # 如果有OZON任务ID，查询OZON的任务状态
        ozon_status = None
        if task_info.get('ozon_task_id'):
            ozon_status = ozon_service.check_import_status(task_info['ozon_task_id'])
        
        return jsonify({
            'success': True,
            'data': {
                'task_info': task_info,
                'ozon_status': ozon_status
            }
        })
        
    except Exception as e:
        logger.error(f"查询任务状态失败: {e}")
        return jsonify({
            'success': False,
            'error': f'查询任务状态失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/update-stock', methods=['POST'])
def api_update_product_stock():
    """更新OZON商品库存API"""
    try:
        data = request.get_json()

        if not data:
            return jsonify({
                'success': False,
                'error': '请求数据不能为空'
            }), 400

        # 支持两种请求格式：
        # 1. 单个商品: {"model_name": "xxx", "stock": 100, "warehouse_id": 123} (warehouse_id可选，默认1020002878812000)
        # 2. 批量商品: {"stocks": [{"model_name": "xxx", "stock": 100, "warehouse_id": 123}]} (warehouse_id可选，默认1020002878812000)

        # 默认仓库ID
        DEFAULT_WAREHOUSE_ID = 1020002878812000

        stocks_to_update = []

        if 'stocks' in data:
            # 批量更新格式
            stocks_input = data['stocks']
        else:
            # 单个商品格式
            stocks_input = [data]

        # 验证和转换数据
        for stock_item in stocks_input:
            model_name = stock_item.get('model_name')
            stock = stock_item.get('stock')
            warehouse_id = stock_item.get('warehouse_id', DEFAULT_WAREHOUSE_ID)  # 使用默认仓库ID

            logger.info(f"🔍 处理库存更新请求: model_name='{model_name}', stock={stock}, warehouse_id={warehouse_id}")

            if not model_name:
                return jsonify({
                    'success': False,
                    'error': 'model_name是必需字段'
                }), 400

            if stock is None or stock < 0:
                return jsonify({
                    'success': False,
                    'error': 'stock必须是非负整数'
                }), 400

            # warehouse_id现在有默认值，不需要验证是否为空

            # 从数据库查找商品信息 - 支持多种格式匹配
            # 前端可能传递 "Hotwav Note12" (空格) 或 "Hotwav-Note12" (连字符)
            model_name_with_dash = model_name.replace(' ', '-')  # 转换为连字符格式

            logger.info(f"🔍 查找产品: 原始名称='{model_name}', 连字符格式='{model_name_with_dash}'")

            product_query = """
                SELECT id, ozon_product_id, item_code, product_name, model_name
                FROM product_info
                WHERE (model_name = %s OR item_code = %s OR item_code = %s)
                AND ozon_product_id IS NOT NULL
                LIMIT 1
            """

            product_result = db_manager.execute_query(product_query, (model_name, model_name, model_name_with_dash))

            if not product_result:
                return jsonify({
                    'success': False,
                    'error': f'未找到型号为 {model_name} 的已上传商品'
                }), 404

            product = product_result[0]

            # 构建OZON API格式的库存数据
            stock_data = {
                "product_id": int(product['ozon_product_id']),
                "stock": int(stock),
                "warehouse_id": int(warehouse_id)
            }

            # 如果有item_code，也添加offer_id
            if product['item_code']:
                stock_data["offer_id"] = product['item_code']

            stocks_to_update.append(stock_data)

        if not stocks_to_update:
            return jsonify({
                'success': False,
                'error': '没有找到可更新的商品'
            }), 400

        # 调用OZON API更新库存
        from services.ozon_api_service import OzonApiService
        ozon_api = OzonApiService()

        result = ozon_api.update_product_stocks(stocks_to_update)

        if result['success']:
            logger.info(f"库存更新成功: {result['updated_count']} 个商品")

            # 🔄 同步更新MySQL数据库中的库存信息
            try:
                updated_mysql_count = 0
                for stock_data in stocks_to_update:
                    ozon_product_id = stock_data['product_id']
                    new_stock = stock_data['stock']

                    # 查询当前的stocks_detail
                    current_query = """
                        SELECT stocks_detail FROM ozon_products
                        WHERE ozon_product_id = %s
                    """
                    current_result = db_manager.execute_query(current_query, (ozon_product_id,))

                    if current_result:
                        current_stocks = current_result[0]['stocks_detail']

                        # 更新stocks_detail中的present值
                        if current_stocks and 'stocks' in current_stocks:
                            # 更新第一个stocks元素的present值
                            if len(current_stocks['stocks']) > 0:
                                current_stocks['stocks'][0]['present'] = new_stock
                                current_stocks['has_stock'] = new_stock > 0

                                # 更新数据库
                                update_query = """
                                    UPDATE ozon_products
                                    SET stocks_detail = %s, updated_at = NOW()
                                    WHERE ozon_product_id = %s
                                """
                                db_manager.execute_query(
                                    update_query,
                                    (json.dumps(current_stocks), ozon_product_id),
                                    fetch=False
                                )
                                updated_mysql_count += 1
                                logger.info(f"✅ 已同步更新MySQL库存: OZON产品ID {ozon_product_id}, 新库存: {new_stock}")
                        else:
                            # 如果没有现有的stocks_detail，创建新的
                            new_stocks_detail = {
                                "stocks": [{
                                    "sku": 0,  # 这里可能需要从其他地方获取真实的SKU
                                    "source": "fbs",
                                    "present": new_stock,
                                    "reserved": 0
                                }],
                                "has_stock": new_stock > 0
                            }

                            update_query = """
                                UPDATE ozon_products
                                SET stocks_detail = %s, updated_at = NOW()
                                WHERE ozon_product_id = %s
                            """
                            db_manager.execute_query(
                                update_query,
                                (json.dumps(new_stocks_detail), ozon_product_id),
                                fetch=False
                            )
                            updated_mysql_count += 1
                            logger.info(f"✅ 已创建并同步MySQL库存: OZON产品ID {ozon_product_id}, 新库存: {new_stock}")

                logger.info(f"🔄 MySQL库存同步完成: {updated_mysql_count}/{len(stocks_to_update)} 个商品")

            except Exception as mysql_error:
                logger.error(f"⚠️ MySQL库存同步失败: {mysql_error}")
                # 不影响主要的OZON更新结果，只记录错误

            # 🔄 清除相关缓存，确保前端获取到最新数据
            try:
                from utils.cache import invalidate_cache_pattern
                # 清除已上传商品列表和统计信息的缓存
                deleted_count = 0
                deleted_count += invalidate_cache_pattern("products:")  # 清除所有products:前缀的缓存
                deleted_count += invalidate_cache_pattern("uploaded_products")  # 清除已上传商品列表缓存
                deleted_count += invalidate_cache_pattern("get_upload_statistics")  # 清除统计信息缓存
                deleted_count += invalidate_cache_pattern("system:")  # 清除系统统计缓存
                logger.info(f"✅ 已清除库存更新相关缓存: {deleted_count} 个条目")
            except Exception as cache_error:
                logger.warning(f"⚠️ 清除缓存失败: {cache_error}")

            # 🔄 清除相关缓存，确保前端获取到最新数据
            try:
                from utils.cache import invalidate_cache_pattern
                # 清除已上传商品列表的缓存
                invalidate_cache_pattern("uploaded_products")
                invalidate_cache_pattern("products:")
                logger.info("✅ 已清除库存更新相关缓存")
            except Exception as cache_error:
                logger.warning(f"⚠️ 清除缓存失败: {cache_error}")

            return jsonify({
                'success': True,
                'data': {
                    'updated_count': result['updated_count'],
                    'mysql_synced_count': updated_mysql_count if 'updated_mysql_count' in locals() else 0,
                    'stocks': stocks_to_update,
                    'ozon_response': result.get('data', {})
                },
                'message': f'成功更新 {result["updated_count"]} 个商品的库存，MySQL同步: {updated_mysql_count if "updated_mysql_count" in locals() else 0} 个'
            })
        else:
            logger.error(f"库存更新失败: {result['error']}")

            return jsonify({
                'success': False,
                'error': result['error'],
                'status_code': result.get('status_code')
            }), 500

    except Exception as e:
        logger.error(f"更新商品库存失败: {e}")
        return jsonify({
            'success': False,
            'error': f'更新商品库存失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/stocks', methods=['GET'])
def api_get_product_stocks():
    """获取OZON商品库存信息API"""
    try:
        # 获取查询参数
        limit = min(int(request.args.get('limit', 20)), 100)
        offset = int(request.args.get('offset', 0))
        search = request.args.get('search', '').strip()
        has_stock = request.args.get('has_stock')  # true/false
        source = request.args.get('source')  # fbo/fbs

        # 构建查询条件
        where_conditions = []
        params = {}

        if search:
            where_conditions.append("(op.name LIKE %(search)s OR op.offer_id LIKE %(search)s)")
            params['search'] = f'%{search}%'

        if has_stock is not None:
            if has_stock.lower() == 'true':
                where_conditions.append("(op.has_fbo_stocks = 1 OR op.has_fbs_stocks = 1)")
            else:
                where_conditions.append("(op.has_fbo_stocks = 0 AND op.has_fbs_stocks = 0)")

        if source:
            if source.lower() == 'fbo':
                where_conditions.append("op.has_fbo_stocks = 1")
            elif source.lower() == 'fbs':
                where_conditions.append("op.has_fbs_stocks = 1")

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 查询商品库存信息
        query = f"""
            SELECT
                op.id, op.ozon_product_id, op.offer_id, op.sku, op.name,
                op.price, op.currency_code, op.is_archived,
                op.has_fbo_stocks, op.has_fbs_stocks, op.has_discounted_fbo_item,
                op.discounted_fbo_stocks, op.stocks_detail,
                op.api_sync_status, op.updated_at
            FROM ozon_products op
            {where_clause}
            ORDER BY op.updated_at DESC
            LIMIT %(limit)s OFFSET %(offset)s
        """

        params.update({'limit': limit, 'offset': offset})
        result = db_manager.execute_query(query, params)

        # 统计查询
        count_query = f"""
            SELECT
                COUNT(*) as total_count,
                SUM(CASE WHEN op.has_fbo_stocks = 1 OR op.has_fbs_stocks = 1 THEN 1 ELSE 0 END) as has_stock_count,
                SUM(CASE WHEN op.has_fbo_stocks = 1 THEN 1 ELSE 0 END) as fbo_count,
                SUM(CASE WHEN op.has_fbs_stocks = 1 THEN 1 ELSE 0 END) as fbs_count
            FROM ozon_products op
            {where_clause}
        """

        count_params = {k: v for k, v in params.items() if k not in ['limit', 'offset']}
        count_result = db_manager.execute_query(count_query, count_params)

        # 处理商品数据
        products = []
        for row in result:
            # 解析库存详情
            stocks_info = None
            total_present = 0
            total_reserved = 0

            if row['stocks_detail']:
                try:
                    if isinstance(row['stocks_detail'], str):
                        stocks_data = json.loads(row['stocks_detail'])
                    else:
                        stocks_data = row['stocks_detail']

                    stocks_list = stocks_data.get('stocks', [])
                    total_present = sum(stock.get('present', 0) for stock in stocks_list)
                    total_reserved = sum(stock.get('reserved', 0) for stock in stocks_list)

                    stocks_info = {
                        'has_stock': stocks_data.get('has_stock', False),
                        'total_present': total_present,
                        'total_reserved': total_reserved,
                        'total_available': total_present - total_reserved,
                        'stocks_by_source': {}
                    }

                    for stock in stocks_list:
                        source = stock.get('source', 'unknown')
                        stocks_info['stocks_by_source'][source] = {
                            'sku': stock.get('sku'),
                            'present': stock.get('present', 0),
                            'reserved': stock.get('reserved', 0),
                            'available': stock.get('present', 0) - stock.get('reserved', 0)
                        }

                except (json.JSONDecodeError, TypeError, AttributeError):
                    pass

            product = {
                'id': row['id'],
                'ozon_product_id': row['ozon_product_id'],
                'offer_id': row['offer_id'],
                'sku': row['sku'],
                'name': row['name'],
                'price': float(row['price']) if row['price'] else 0.0,
                'currency_code': row['currency_code'] or 'RUB',
                'is_archived': bool(row['is_archived']),
                'has_fbo_stocks': bool(row['has_fbo_stocks']),
                'has_fbs_stocks': bool(row['has_fbs_stocks']),
                'has_discounted_fbo_item': bool(row['has_discounted_fbo_item']),
                'discounted_fbo_stocks': row['discounted_fbo_stocks'] or 0,
                'stocks': stocks_info,
                'total_present': total_present,
                'total_available': total_present - total_reserved,
                'api_sync_status': row['api_sync_status'],
                'updated_at': row['updated_at'].isoformat() + 'Z' if row['updated_at'] else None
            }
            products.append(product)

        # 统计信息
        stats = count_result[0] if count_result else {}

        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'statistics': {
                    'total_count': stats.get('total_count', 0),
                    'has_stock_count': stats.get('has_stock_count', 0),
                    'fbo_count': stats.get('fbo_count', 0),
                    'fbs_count': stats.get('fbs_count', 0),
                    'no_stock_count': stats.get('total_count', 0) - stats.get('has_stock_count', 0)
                },
                'pagination': {
                    'limit': limit,
                    'offset': offset,
                    'has_more': len(products) == limit,
                    'total_pages': (stats.get('total_count', 0) + limit - 1) // limit
                }
            }
        })

    except Exception as e:
        logger.error(f"获取商品库存失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取商品库存失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/upload-tasks', methods=['GET'])
def api_get_upload_tasks():
    """获取上传任务列表API"""
    try:
        status = request.args.get('status')
        limit = int(request.args.get('limit', 5))  # 默认每页5个任务

        tasks = ozon_service.get_upload_tasks(status, limit)

        return jsonify({
            'success': True,
            'data': {
                'tasks': tasks,
                'total_count': len(tasks),
                'filter_status': status
            }
        })

    except Exception as e:
        logger.error(f"获取上传任务列表失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取任务列表失败: {str(e)}'
        }), 500

@ozon_uploader_bp.route('/api/uploaded-products', methods=['GET'])
def api_get_uploaded_products():
    """获取已上传到OZON的商品列表API"""
    try:
        # 获取查询参数
        limit = int(request.args.get('limit', 10))
        offset = int(request.args.get('offset', 0))
        status_filter = request.args.get('status', '')
        search = request.args.get('search', '').strip()
        sort_by = request.args.get('sort_by', '')
        sort_order = request.args.get('sort_order', 'desc')

        # 新增：销售状态筛选参数
        selling_status = request.args.get('selling_status', '').strip()

        # 正确的查询：从ozon_products表获取已上传的商品数据，并关联价格配置
        base_query = """
        SELECT
            op.id, op.ozon_product_id, op.offer_id, op.sku, op.name,
            op.price, op.old_price, op.marketing_price, op.currency_code,
            op.is_archived, op.is_discounted, op.has_fbo_stocks, op.has_fbs_stocks,
            op.has_discounted_fbo_item, op.discounted_fbo_stocks,
            op.images, op.primary_image, op.api_sync_status,
            op.last_list_sync_at, op.last_detail_sync_at,
            op.ozon_created_at, op.ozon_updated_at, op.created_at, op.updated_at,
            -- 库存信息
            op.stocks_detail,
            -- 新增：OZON详细状态信息
            op.statuses_detail,
            -- 新增最低价格信息
            ppc.min_price, ppc.max_price, ppc.auto_adjust_enabled,
            ppc.updated_at as price_config_updated_at,
            -- 评级信息
            opr.overall_rating, opr.rating_status, opr.content_quality_score
        FROM ozon_products op
        LEFT JOIN product_price_config ppc ON op.id = ppc.product_id
        LEFT JOIN ozon_product_ratings opr ON op.sku = opr.sku
        WHERE op.api_sync_status = 'detail_synced'
        """

        params = []

        # 添加状态筛选
        if status_filter:
            if status_filter == 'active':
                base_query += " AND op.is_archived = 0"
            elif status_filter == 'archived':
                base_query += " AND op.is_archived = 1"
            elif status_filter == 'discounted':
                base_query += " AND op.is_discounted = 1"

        # 添加搜索功能
        if search:
            search_pattern = f'%{search}%'
            base_query += """ AND (
                op.name LIKE %s OR
                CAST(op.sku AS CHAR) LIKE %s OR
                CAST(op.ozon_product_id AS CHAR) LIKE %s OR
                op.offer_id LIKE %s
            )"""
            params.extend([search_pattern, search_pattern, search_pattern, search_pattern])

        # 注意：销售状态筛选将在Python代码中处理，因为ozon_selling_status是计算字段

        # 获取总数（构建不包含ORDER BY和LIMIT的查询）
        count_query_base = base_query[base_query.find('FROM'):]
        count_query = f"SELECT COUNT(*) as total {count_query_base}"
        count_result = db_manager.execute_query(count_query, params)
        total_count = count_result[0]['total'] if count_result else 0

        # 构建排序子句
        order_clause = "ORDER BY op.updated_at DESC"  # 默认排序

        if sort_by and sort_order:
            if sort_by in ['total_present', 'total_available']:
                # 对于库存字段，需要从JSON中提取数据进行排序
                if sort_by == 'total_present':
                    # 改进的总库存排序：使用JSON_UNQUOTE确保正确的数据类型转换
                    sort_expression = '''
                        COALESCE(
                            CAST(
                                JSON_UNQUOTE(
                                    JSON_EXTRACT(op.stocks_detail, '$.stocks[0].present')
                                ) AS SIGNED
                            ),
                            0
                        )
                    '''
                elif sort_by == 'total_available':
                    # 改进的可用库存排序：present - reserved
                    sort_expression = '''
                        COALESCE(
                            CAST(
                                JSON_UNQUOTE(
                                    JSON_EXTRACT(op.stocks_detail, '$.stocks[0].present')
                                ) AS SIGNED
                            ),
                            0
                        ) - COALESCE(
                            CAST(
                                JSON_UNQUOTE(
                                    JSON_EXTRACT(op.stocks_detail, '$.stocks[0].reserved')
                                ) AS SIGNED
                            ),
                            0
                        )
                    '''

                # 构建完整的排序子句：库存字段 + 更新时间作为次要排序
                order_clause = f"ORDER BY {sort_expression} {sort_order.upper()}, op.updated_at DESC"

            elif sort_by == 'updated_at':
                order_clause = f"ORDER BY op.updated_at {sort_order.upper()}"

        # 添加排序和分页
        # 如果有销售状态筛选，获取更多数据以确保找到匹配的商品
        if selling_status:
            # 获取所有数据进行筛选（或者一个较大的数量）
            query_limit = 1000  # 获取足够多的数据
            base_query += f" {order_clause} LIMIT %s"
            params.extend([query_limit])
        else:
            # 正常分页
            base_query += f" {order_clause} LIMIT %s OFFSET %s"
            params.extend([limit, offset])

        # 执行查询
        results = db_manager.execute_query(base_query, params)

        # 处理结果
        uploaded_products = []
        for row in results:
            # 解析JSON字段
            images = []
            try:
                if row.get('images'):
                    images = json.loads(row['images']) if isinstance(row['images'], str) else row['images']
            except json.JSONDecodeError:
                images = []

            # 解析库存详情
            stocks_info = None
            total_present = 0
            total_reserved = 0
            total_available = 0

            if row.get('stocks_detail'):
                try:
                    if isinstance(row['stocks_detail'], str):
                        stocks_data = json.loads(row['stocks_detail'])
                    else:
                        stocks_data = row['stocks_detail']

                    # 解析库存详情
                    stocks_list = stocks_data.get('stocks', [])
                    total_present = sum(stock.get('present', 0) for stock in stocks_list)
                    total_reserved = sum(stock.get('reserved', 0) for stock in stocks_list)
                    total_available = total_present - total_reserved

                    stocks_info = {
                        'has_stock': stocks_data.get('has_stock', False),
                        'total_present': total_present,
                        'total_reserved': total_reserved,
                        'total_available': total_available,
                        'stocks_by_source': {}
                    }

                    # 按来源分组库存
                    for stock in stocks_list:
                        source = stock.get('source', 'unknown')
                        if source not in stocks_info['stocks_by_source']:
                            stocks_info['stocks_by_source'][source] = {
                                'present': 0,
                                'reserved': 0,
                                'available': 0
                            }
                        stocks_info['stocks_by_source'][source]['present'] += stock.get('present', 0)
                        stocks_info['stocks_by_source'][source]['reserved'] += stock.get('reserved', 0)
                        stocks_info['stocks_by_source'][source]['available'] = (
                            stocks_info['stocks_by_source'][source]['present'] -
                            stocks_info['stocks_by_source'][source]['reserved']
                        )

                except (json.JSONDecodeError, TypeError, AttributeError):
                    stocks_info = None

            # 处理OZON状态详情
            ozon_status_detail = None
            ozon_selling_status = 'unknown'
            status_warnings = []

            if row.get('statuses_detail'):
                try:
                    if isinstance(row['statuses_detail'], str):
                        statuses_data = json.loads(row['statuses_detail'])
                    else:
                        statuses_data = row['statuses_detail']

                    if statuses_data:
                        ozon_status_detail = {
                            'status': statuses_data.get('status'),
                            'status_name': statuses_data.get('status_name', ''),
                            'status_description': statuses_data.get('status_description', ''),
                            'is_created': statuses_data.get('is_created', False),
                            'moderate_status': statuses_data.get('moderate_status'),
                            'validation_status': statuses_data.get('validation_status'),
                            'status_updated_at': statuses_data.get('status_updated_at'),
                            'status_failed': statuses_data.get('status_failed'),
                            'status_tooltip': statuses_data.get('status_tooltip')
                        }

                        # 判断销售状态 - 重新定义逻辑
                        status_name = statuses_data.get('status_name', '')
                        status_description = statuses_data.get('status_description', '')
                        moderate_status = statuses_data.get('moderate_status', '')
                        validation_status = statuses_data.get('validation_status', '')

                        # 默认为正常销售状态
                        ozon_selling_status = 'selling'

                        # 检查异常状态
                        if status_name == 'Не продается':
                            ozon_selling_status = 'removed'
                            status_warnings.append('商品已被OZON标记为不销售')
                        elif 'Убран из продажи' in status_description:
                            ozon_selling_status = 'removed'
                            status_warnings.append('商品已被OZON从销售中移除')
                        elif moderate_status != 'approved':
                            ozon_selling_status = 'pending'
                            status_warnings.append(f'商品审核状态异常: {moderate_status}')
                        elif validation_status != 'success':
                            ozon_selling_status = 'pending'
                            status_warnings.append(f'商品验证状态异常: {validation_status}')

                        # 正常状态：Продается, Готов к продаже 等都是 selling

                except (json.JSONDecodeError, TypeError, AttributeError):
                    logger.warning(f"解析商品 {row.get('offer_id', 'Unknown')} 的状态详情失败")

            # 构建商品数据
            product_data = {
                'id': row['id'],
                'ozon_product_id': row['ozon_product_id'],
                'offer_id': row['offer_id'],
                'sku': row['sku'],
                'name': row['name'],
                'price': float(row['price']) if row['price'] else 0,
                'old_price': float(row['old_price']) if row['old_price'] else 0,
                'marketing_price': float(row['marketing_price']) if row['marketing_price'] else 0,
                'currency_code': row['currency_code'],
                'is_archived': bool(row['is_archived']),
                'is_discounted': bool(row['is_discounted']),
                'has_fbo_stocks': bool(row['has_fbo_stocks']),
                'has_fbs_stocks': bool(row['has_fbs_stocks']),
                'has_discounted_fbo_item': bool(row.get('has_discounted_fbo_item', False)),
                'discounted_fbo_stocks': row.get('discounted_fbo_stocks', 0) or 0,
                # 库存信息 - 顶级字段方便前端直接使用
                'total_present': total_present,      # 总库存数量
                'total_reserved': total_reserved,    # 总预留数量
                'total_available': total_available,  # 总可售数量
                'stocks': stocks_info,  # 详细库存信息
                'images': images,
                'primary_image': row['primary_image'],
                'api_sync_status': row['api_sync_status'],
                'last_list_sync_at': row['last_list_sync_at'].isoformat() if row['last_list_sync_at'] else None,
                'last_detail_sync_at': row['last_detail_sync_at'].isoformat() if row['last_detail_sync_at'] else None,
                'ozon_created_at': row['ozon_created_at'].isoformat() if row['ozon_created_at'] else None,
                'ozon_updated_at': row['ozon_updated_at'].isoformat() if row['ozon_updated_at'] else None,
                'created_at': row['created_at'].isoformat() if row['created_at'] else None,
                'updated_at': row['updated_at'].isoformat() if row['updated_at'] else None,
                # 新增：最低价格配置信息
                'price_config': {
                    'min_price': float(row['min_price']) if row['min_price'] else None,
                    'max_price': float(row['max_price']) if row['max_price'] else None,
                    'auto_adjust_enabled': bool(row['auto_adjust_enabled']) if row['auto_adjust_enabled'] is not None else False,
                    'updated_at': row['price_config_updated_at'].isoformat() if row['price_config_updated_at'] else None
                },
                # 评级信息 - 保持向后兼容的顶级字段
                'overall_rating': float(row['overall_rating']) if row['overall_rating'] else None,
                'rating_status': row['rating_status'],
                'content_quality_score': float(row['content_quality_score']) if row['content_quality_score'] else None,
                # 新增：评级信息嵌套对象，方便前端使用
                'rating_info': {
                    'overall_rating': float(row['overall_rating']) if row['overall_rating'] else None,
                    'rating_status': row['rating_status'],
                    'content_quality_score': float(row['content_quality_score']) if row['content_quality_score'] else None
                },
                # 新增：rating嵌套对象（前端期望的格式）
                'rating': {
                    'overall_rating': float(row['overall_rating']) if row['overall_rating'] else None,
                    'rating_status': row['rating_status'],
                    'content_quality_score': float(row['content_quality_score']) if row['content_quality_score'] else None,
                    'content_quality_status': None,  # 暂时没有这个字段
                    'title_score': None,             # 暂时没有这个字段
                    'description_score': None,       # 暂时没有这个字段
                    'images_score': None,            # 暂时没有这个字段
                    'attributes_score': None,        # 暂时没有这个字段
                    'category_score': None,          # 暂时没有这个字段
                    'last_sync_at': None             # 暂时没有这个字段
                } if row['overall_rating'] else None,
                # 新增：OZON详细状态信息
                'ozon_status_detail': ozon_status_detail,
                'ozon_selling_status': ozon_selling_status,
                'status_warnings': status_warnings if status_warnings else None,
                'status': 'archived' if row['is_archived'] else 'active'
            }

            # 销售状态筛选（在Python中处理）
            if selling_status:
                valid_selling_statuses = ['selling', 'removed', 'pending', 'not_selling', 'unknown']
                if selling_status in valid_selling_statuses:

                    if selling_status != ozon_selling_status:
                        continue  # 跳过不匹配的商品

            uploaded_products.append(product_data)

        # 如果有销售状态筛选，应用分页
        if selling_status:
            total_filtered = len(uploaded_products)
            uploaded_products = uploaded_products[offset:offset + limit]
        else:
            total_filtered = len(uploaded_products)

        # 计算统计信息
        stats_query = """
        SELECT
            COUNT(*) as total,
            SUM(CASE WHEN op.is_archived = 0 THEN 1 ELSE 0 END) as active_count,
            SUM(CASE WHEN op.is_archived = 1 THEN 1 ELSE 0 END) as archived_count,
            SUM(CASE WHEN op.is_discounted = 1 THEN 1 ELSE 0 END) as discounted_count,
            SUM(CASE WHEN ppc.min_price IS NOT NULL THEN 1 ELSE 0 END) as with_min_price_count,
            AVG(CASE WHEN ppc.min_price IS NOT NULL THEN ppc.min_price ELSE NULL END) as avg_min_price
        FROM ozon_products op
        LEFT JOIN product_price_config ppc ON op.id = ppc.product_id
        WHERE op.api_sync_status = 'detail_synced'
        """

        stats_result = db_manager.execute_query(stats_query)

        # 计算销售状态统计（从已处理的商品数据中计算）
        selling_status_stats = {
            'selling': 0,
            'removed': 0,
            'pending': 0,
            'not_selling': 0,
            'unknown': 0
        }

        # 简化版本：从当前结果计算统计
        for product in uploaded_products:
            status = product.get('ozon_selling_status', 'unknown')
            selling_status_stats[status] += 1


        statistics = {
            'total_count': stats_result[0]['total'] if stats_result else 0,
            'active_count': stats_result[0]['active_count'] if stats_result else 0,
            'archived_count': stats_result[0]['archived_count'] if stats_result else 0,
            'discounted_count': stats_result[0]['discounted_count'] if stats_result else 0,
            'with_min_price_count': stats_result[0]['with_min_price_count'] if stats_result else 0,
            'avg_min_price': float(stats_result[0]['avg_min_price']) if stats_result[0]['avg_min_price'] else 0,
            # 新增：销售状态统计
            'selling_status_stats': selling_status_stats
        }

        return jsonify({
            'success': True,
            'data': {
                'products': uploaded_products,
                'total_count': total_count,
                'returned_count': len(uploaded_products),
                'offset': offset,
                'limit': limit,
                'has_more': offset + len(uploaded_products) < total_count,
                'statistics': statistics
            }
        })

    except Exception as e:
        logger.error(f"获取已上传商品列表失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取已上传商品列表失败: {str(e)}'
        }), 500

@ozon_uploader_bp.route('/api/retry-failed', methods=['POST'])
def api_retry_failed_upload():
    """重试失败的上传任务API"""
    try:
        data = request.get_json()
        task_id = data.get('task_id')
        
        if not task_id:
            return jsonify({
                'success': False,
                'error': '缺少任务ID'
            }), 400
        
        # 获取失败的任务信息
        task_query = "SELECT * FROM ozon_upload_tasks WHERE task_id = %s AND status = 'failed'"
        task_result = db_manager.execute_query(task_query, (task_id,))
        
        if not task_result:
            return jsonify({
                'success': False,
                'error': '任务不存在或状态不是失败'
            }), 404
        
        task_info = task_result[0]
        product_ids = task_info['product_ids']
        
        if isinstance(product_ids, str):
            import json
            product_ids = json.loads(product_ids)
        
        # 重新获取产品信息并上传
        product_query = """
        SELECT * FROM product_info 
        WHERE id IN ({})
        """.format(','.join(['%s'] * len(product_ids)))
        
        products = db_manager.execute_query(product_query, tuple(product_ids))
        
        if not products:
            return jsonify({
                'success': False,
                'error': '没有找到相关产品'
            }), 404
        
        # 更新任务状态为重试中
        ozon_service.update_upload_task_status(task_id, 'uploading')
        
        # 执行重新上传
        ozon_task_id = ozon_service.upload_products_to_ozon(products)
        
        if ozon_task_id:
            ozon_service.update_upload_task_status(
                task_id, 
                'success', 
                ozon_task_id=ozon_task_id,
                success_count=len(products),
                failed_count=0
            )
            
            return jsonify({
                'success': True,
                'data': {
                    'task_id': task_id,
                    'ozon_task_id': ozon_task_id,
                    'message': '重试上传成功'
                }
            })
        else:
            ozon_service.update_upload_task_status(
                task_id, 
                'failed', 
                failed_count=len(products)
            )
            
            return jsonify({
                'success': False,
                'error': '重试上传失败'
            }), 500
            
    except Exception as e:
        logger.error(f"重试上传失败: {e}")
        return jsonify({
            'success': False,
            'error': f'重试上传失败: {str(e)}'
        }), 500

def validate_offer_id_format(offer_id: str) -> tuple[bool, str]:
    """
    验证offer_id格式
    支持字母、数字、连字符、下划线、斜杠
    """
    if not offer_id or not offer_id.strip():
        return False, 'Offer ID不能为空'

    trimmed = offer_id.strip()

    if len(trimmed) < 1:
        return False, 'Offer ID长度不能少于1个字符'

    if len(trimmed) > 100:
        return False, 'Offer ID长度不能超过100个字符'

    # 允许字母、数字、连字符、下划线、斜杠、点号
    import re
    valid_pattern = re.compile(r'^[a-zA-Z0-9\-_/.]+$')
    if not valid_pattern.match(trimmed):
        return False, 'Offer ID只能包含字母、数字、连字符(-)、下划线(_)、斜杠(/)和点号(.)'

    return True, ''


@ozon_uploader_bp.route('/api/change-offer-id', methods=['POST'])
def api_change_offer_id():
    """修改offer_id并重新上传到OZON"""
    try:
        logger.info("🔄 收到修改offer_id请求")

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '没有提供请求数据'
            }), 400

        current_offer_id = data.get('current_offer_id', '').strip()
        new_offer_id = data.get('new_offer_id', '').strip()
        auto_reupload = data.get('auto_reupload', True)  # 是否自动重新上传

        if not current_offer_id or not new_offer_id:
            return jsonify({
                'success': False,
                'error': '必须提供当前offer_id和新的offer_id'
            }), 400

        # 验证offer_id格式
        is_valid, error_msg = validate_offer_id_format(current_offer_id)
        if not is_valid:
            return jsonify({
                'success': False,
                'error': f'当前offer_id格式无效: {error_msg}'
            }), 400

        is_valid, error_msg = validate_offer_id_format(new_offer_id)
        if not is_valid:
            return jsonify({
                'success': False,
                'error': f'新offer_id格式无效: {error_msg}'
            }), 400

        if current_offer_id == new_offer_id:
            return jsonify({
                'success': False,
                'error': '新的offer_id不能与当前相同'
            }), 400

        logger.info(f"📋 修改offer_id: {current_offer_id} -> {new_offer_id}")

        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        # 第一步：检查当前offer_id是否存在
        check_query = """
        SELECT pi.id, pi.item_code, pi.product_name, pi.ozon_product_id,
               op.id as ozon_record_id, op.ozon_product_id as ozon_pid
        FROM product_info pi
        LEFT JOIN ozon_products op ON pi.item_code = op.offer_id
        WHERE pi.item_code = %s
        """

        current_product = db_manager.execute_query(check_query, (current_offer_id,))

        if not current_product:
            return jsonify({
                'success': False,
                'error': f'未找到offer_id为 {current_offer_id} 的产品'
            }), 404

        product_info = current_product[0]
        product_id = product_info['id']

        # 第二步：检查新offer_id是否已被使用
        check_new_query = """
        SELECT id, item_code FROM product_info WHERE item_code = %s
        UNION
        SELECT id, offer_id FROM ozon_products WHERE offer_id = %s
        """

        existing_new = db_manager.execute_query(check_new_query, (new_offer_id, new_offer_id))

        if existing_new:
            return jsonify({
                'success': False,
                'error': f'新的offer_id {new_offer_id} 已被使用'
            }), 400

        # 第三步：开始事务处理
        try:
            # 更新product_info表的item_code
            update_product_query = """
            UPDATE product_info
            SET item_code = %s,
                ozon_upload_status = 'pending',
                ozon_product_id = NULL,
                updated_at = NOW()
            WHERE id = %s
            """

            result1 = db_manager.execute_query(
                update_product_query,
                (new_offer_id, product_id),
                fetch=False
            )

            if not result1:
                raise Exception("更新product_info表失败")

            # 如果存在OZON记录，删除旧的关联
            if product_info.get('ozon_record_id'):
                delete_ozon_query = "DELETE FROM ozon_products WHERE offer_id = %s"
                db_manager.execute_query(delete_ozon_query, (current_offer_id,), fetch=False)
                logger.info(f"🗑️ 删除旧的OZON记录: offer_id={current_offer_id}")

            logger.info(f"✅ 成功修改offer_id: {current_offer_id} -> {new_offer_id}")

            # 第四步：如果需要自动重新上传
            reupload_result = None
            if auto_reupload:
                try:
                    logger.info(f"🚀 开始自动重新上传产品: {new_offer_id}")

                    # 获取产品数据
                    product_query = "SELECT * FROM product_info WHERE id = %s"
                    product_data = db_manager.execute_query(product_query, (product_id,))

                    if product_data:
                        # 使用现有的上传服务
                        upload_task_id = ozon_service.create_upload_task([product_id], {
                            'reason': f'offer_id_change_{current_offer_id}_to_{new_offer_id}',
                            'auto_retry': True
                        })

                        if upload_task_id:
                            # 执行上传
                            ozon_task_id = ozon_service.upload_products_to_ozon(product_data)

                            if ozon_task_id:
                                ozon_service.update_upload_task_status(
                                    upload_task_id,
                                    'success',
                                    ozon_task_id=ozon_task_id,
                                    success_count=1
                                )
                                reupload_result = {
                                    'upload_task_id': upload_task_id,
                                    'ozon_task_id': ozon_task_id,
                                    'status': 'success'
                                }
                                logger.info(f"✅ 自动重新上传成功: task_id={ozon_task_id}")
                            else:
                                ozon_service.update_upload_task_status(
                                    upload_task_id,
                                    'failed',
                                    failed_count=1
                                )
                                reupload_result = {
                                    'upload_task_id': upload_task_id,
                                    'status': 'failed',
                                    'error': '上传到OZON失败'
                                }
                        else:
                            reupload_result = {
                                'status': 'failed',
                                'error': '创建上传任务失败'
                            }

                except Exception as upload_error:
                    logger.error(f"❌ 自动重新上传失败: {upload_error}")
                    reupload_result = {
                        'status': 'failed',
                        'error': f'自动重新上传失败: {str(upload_error)}'
                    }

            return jsonify({
                'success': True,
                'data': {
                    'product_id': product_id,
                    'old_offer_id': current_offer_id,
                    'new_offer_id': new_offer_id,
                    'product_name': product_info['product_name'],
                    'auto_reupload': auto_reupload,
                    'reupload_result': reupload_result,
                    'message': f'成功修改offer_id: {current_offer_id} -> {new_offer_id}'
                }
            })

        except Exception as e:
            logger.error(f"❌ 修改offer_id事务失败: {e}")
            return jsonify({
                'success': False,
                'error': f'修改offer_id失败: {str(e)}'
            }), 500

    except Exception as e:
        logger.error(f"❌ 修改offer_id请求处理失败: {e}")
        return jsonify({
            'success': False,
            'error': f'请求处理失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/batch-change-offer-id', methods=['POST'])
def api_batch_change_offer_id():
    """批量修改offer_id并重新上传到OZON"""
    try:
        logger.info("🔄 收到批量修改offer_id请求")

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '没有提供请求数据'
            }), 400

        changes = data.get('changes', [])
        auto_reupload = data.get('auto_reupload', True)

        if not changes:
            return jsonify({
                'success': False,
                'error': '没有提供修改列表'
            }), 400

        logger.info(f"📋 批量修改数量: {len(changes)}")

        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        results = []
        success_count = 0
        failed_count = 0

        for i, change in enumerate(changes, 1):
            current_offer_id = change.get('current_offer_id', '').strip()
            new_offer_id = change.get('new_offer_id', '').strip()

            logger.info(f"📦 处理第 {i}/{len(changes)} 个修改: {current_offer_id} -> {new_offer_id}")

            try:
                # 验证输入
                if not current_offer_id or not new_offer_id:
                    raise ValueError('offer_id不能为空')

                if current_offer_id == new_offer_id:
                    raise ValueError('新旧offer_id相同')

                # 检查当前产品是否存在
                check_query = """
                SELECT pi.id, pi.item_code, pi.product_name, pi.ozon_product_id,
                       op.id as ozon_record_id
                FROM product_info pi
                LEFT JOIN ozon_products op ON pi.item_code = op.offer_id
                WHERE pi.item_code = %s
                """

                current_product = db_manager.execute_query(check_query, (current_offer_id,))

                if not current_product:
                    raise ValueError(f'未找到offer_id为 {current_offer_id} 的产品')

                product_info = current_product[0]
                product_id = product_info['id']

                # 检查新offer_id是否已被使用
                check_new_query = """
                SELECT id FROM product_info WHERE item_code = %s
                UNION
                SELECT id FROM ozon_products WHERE offer_id = %s
                """

                existing_new = db_manager.execute_query(check_new_query, (new_offer_id, new_offer_id))

                if existing_new:
                    raise ValueError(f'新的offer_id {new_offer_id} 已被使用')

                # 执行修改
                update_product_query = """
                UPDATE product_info
                SET item_code = %s,
                    ozon_upload_status = 'pending',
                    ozon_product_id = NULL,
                    updated_at = NOW()
                WHERE id = %s
                """

                result = db_manager.execute_query(
                    update_product_query,
                    (new_offer_id, product_id),
                    fetch=False
                )

                if not result:
                    raise Exception("更新product_info表失败")

                # 删除旧的OZON记录
                if product_info.get('ozon_record_id'):
                    delete_ozon_query = "DELETE FROM ozon_products WHERE offer_id = %s"
                    db_manager.execute_query(delete_ozon_query, (current_offer_id,), fetch=False)

                success_count += 1
                results.append({
                    'current_offer_id': current_offer_id,
                    'new_offer_id': new_offer_id,
                    'product_id': product_id,
                    'product_name': product_info['product_name'],
                    'status': 'success',
                    'message': '修改成功'
                })

                logger.info(f"✅ 修改成功: {current_offer_id} -> {new_offer_id}")

            except Exception as e:
                failed_count += 1
                error_msg = str(e)
                results.append({
                    'current_offer_id': current_offer_id,
                    'new_offer_id': new_offer_id,
                    'status': 'failed',
                    'error': error_msg
                })

                logger.error(f"❌ 修改失败: {current_offer_id} -> {new_offer_id}, 错误: {error_msg}")

        # 如果需要自动重新上传成功的产品
        reupload_results = []
        if auto_reupload and success_count > 0:
            logger.info(f"🚀 开始批量重新上传 {success_count} 个产品")

            success_product_ids = [r['product_id'] for r in results if r['status'] == 'success']

            try:
                # 创建批量上传任务
                upload_task_id = ozon_service.create_upload_task(success_product_ids, {
                    'reason': 'batch_offer_id_change',
                    'auto_retry': True
                })

                if upload_task_id:
                    # 获取产品数据
                    product_query = f"""
                    SELECT * FROM product_info
                    WHERE id IN ({','.join(['%s'] * len(success_product_ids))})
                    """

                    products_data = db_manager.execute_query(product_query, success_product_ids)

                    if products_data:
                        # 执行批量上传
                        ozon_task_id = ozon_service.upload_products_to_ozon(products_data)

                        if ozon_task_id:
                            ozon_service.update_upload_task_status(
                                upload_task_id,
                                'success',
                                ozon_task_id=ozon_task_id,
                                success_count=len(products_data)
                            )
                            reupload_results = {
                                'upload_task_id': upload_task_id,
                                'ozon_task_id': ozon_task_id,
                                'status': 'success',
                                'uploaded_count': len(products_data)
                            }
                        else:
                            reupload_results = {
                                'upload_task_id': upload_task_id,
                                'status': 'failed',
                                'error': '批量上传到OZON失败'
                            }

            except Exception as upload_error:
                logger.error(f"❌ 批量重新上传失败: {upload_error}")
                reupload_results = {
                    'status': 'failed',
                    'error': f'批量重新上传失败: {str(upload_error)}'
                }

        return jsonify({
            'success': True,
            'data': {
                'total_count': len(changes),
                'success_count': success_count,
                'failed_count': failed_count,
                'results': results,
                'auto_reupload': auto_reupload,
                'reupload_results': reupload_results,
                'message': f'批量修改完成: 成功 {success_count} 个, 失败 {failed_count} 个'
            }
        })

    except Exception as e:
        logger.error(f"❌ 批量修改offer_id请求处理失败: {e}")
        return jsonify({
            'success': False,
            'error': f'批量修改失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/sync-finance-transactions', methods=['POST'])
def api_sync_finance_transactions():
    """同步OZON财务交易数据"""
    try:
        logger.info("🔄 收到财务交易数据同步请求")

        # 导入财务服务
        from services.ozon_finance_service import sync_finance_transactions

        # 执行同步
        result = sync_finance_transactions()

        if result.get('success'):
            logger.info(f"✅ 财务交易数据同步成功: {result}")
            return jsonify({
                'success': True,
                'data': result,
                'message': '财务交易数据同步完成'
            })
        else:
            logger.error(f"❌ 财务交易数据同步失败: {result}")
            return jsonify({
                'success': False,
                'error': result.get('error', '同步失败'),
                'data': result
            }), 500

    except Exception as e:
        logger.error(f"❌ 财务交易数据同步请求处理失败: {e}")
        return jsonify({
            'success': False,
            'error': f'同步请求处理失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/finance-transactions', methods=['GET'])
def api_get_finance_transactions():
    """获取财务交易数据列表"""
    try:
        logger.info("📊 收到财务交易数据查询请求")

        # 获取查询参数
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))

        # 默认查询近一个月的数据
        from datetime import datetime, timedelta
        default_date_from = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')

        date_from = request.args.get('date_from', default_date_from)
        date_to = request.args.get('date_to', '')
        transaction_category = request.args.get('category', '')

        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        # 构建查询条件
        where_conditions = []
        params = []

        if date_from:
            where_conditions.append("operation_date >= %s")
            params.append(f"{date_from} 00:00:00")

        if date_to:
            where_conditions.append("operation_date <= %s")
            params.append(f"{date_to} 23:59:59")

        if transaction_category:
            where_conditions.append("transaction_category = %s")
            params.append(transaction_category)

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 查询数据
        query = f"""
        SELECT
            id, operation_id, operation_type, operation_type_name,
            operation_date, amount, currency_code, posting_number,
            transaction_category, created_at
        FROM ozon_finance_transactions
        {where_clause}
        ORDER BY operation_date DESC
        LIMIT %s OFFSET %s
        """

        params.extend([limit, offset])

        transactions = db_manager.execute_query(query, params)

        # 查询总数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM ozon_finance_transactions
        {where_clause}
        """

        count_params = params[:-2]  # 移除limit和offset参数
        count_result = db_manager.execute_query(count_query, count_params)
        total_count = count_result[0]['total'] if count_result else 0

        # 查询统计信息
        stats_query = f"""
        SELECT
            transaction_category,
            COUNT(*) as count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount
        FROM ozon_finance_transactions
        {where_clause}
        GROUP BY transaction_category
        ORDER BY total_amount DESC
        """

        stats_result = db_manager.execute_query(stats_query, count_params)

        return jsonify({
            'success': True,
            'data': {
                'transactions': transactions,
                'pagination': {
                    'total_count': total_count,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total_count
                },
                'statistics': stats_result,
                'filters': {
                    'date_from': date_from,
                    'date_to': date_to,
                    'category': transaction_category
                }
            }
        })

    except Exception as e:
        logger.error(f"❌ 财务交易数据查询失败: {e}")
        return jsonify({
            'success': False,
            'error': f'查询失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/update-product/<int:product_id>', methods=['PUT'])
def api_update_product(product_id):
    """更新产品信息API"""
    try:
        logger.info(f"📝 收到产品更新请求: ID={product_id}")

        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '没有提供更新数据'
            }), 400

        logger.info(f"📋 更新数据: {data}")

        # 验证产品是否存在
        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        # 检查产品是否存在
        check_query = "SELECT id FROM product_info WHERE id = %s"
        existing = db_manager.execute_query(check_query, (product_id,))

        if not existing:
            return jsonify({
                'success': False,
                'error': '产品不存在'
            }), 404

        # 构建更新查询
        allowed_fields = {
            'product_name': 'product_name',
            'model_name': 'model_name',
            'item_code': 'item_code',
            'main_image_url': 'main_image_url',
            'price_cny': 'price_cny',
            'procurement_cost': 'procurement_cost'
        }

        # 过滤有效字段
        update_fields = {}
        for field, db_field in allowed_fields.items():
            if field in data:
                update_fields[db_field] = data[field]

        if not update_fields:
            return jsonify({
                'success': False,
                'error': '没有有效的更新字段'
            }), 400

        # 构建SQL更新语句
        set_clauses = []
        params = []

        for db_field, value in update_fields.items():
            set_clauses.append(f"{db_field} = %s")
            params.append(value)

        # 添加更新时间
        set_clauses.append("updated_at = NOW()")
        params.append(product_id)

        update_query = f"""
        UPDATE product_info
        SET {', '.join(set_clauses)}
        WHERE id = %s
        """

        logger.info(f"🔄 执行更新查询: {update_query}")
        logger.info(f"📊 参数: {params}")

        # 执行更新
        result = db_manager.execute_query(update_query, params, fetch=False)

        if result:
            logger.info(f"✅ 产品 {product_id} 更新成功")

            # 获取更新后的产品信息
            select_query = """
            SELECT id, model_name, item_code, product_name, main_image_url,
                   price_cny, procurement_cost, ozon_upload_status, created_at, updated_at
            FROM product_info
            WHERE id = %s
            """

            updated_product = db_manager.execute_query(select_query, (product_id,))

            return jsonify({
                'success': True,
                'data': {
                    'product': updated_product[0] if updated_product else None,
                    'updated_fields': list(update_fields.keys()),
                    'message': f'产品信息更新成功'
                }
            })
        else:
            logger.error(f"❌ 产品 {product_id} 更新失败")
            return jsonify({
                'success': False,
                'error': '更新失败'
            }), 500

    except Exception as e:
        logger.error(f"更新产品信息异常: {e}")
        return jsonify({
            'success': False,
            'error': f'更新失败: {str(e)}'
        }), 500


# ================================
# OZON商品同步相关API
# ================================

@ozon_uploader_bp.route('/api/ozon-sync/test', methods=['GET'])
def api_test_ozon_sync():
    """测试OZON同步API"""
    return jsonify({
        'success': True,
        'message': 'OZON同步API测试成功',
        'data': {
            'timestamp': '2025-06-26 12:35:00',
            'version': '1.0.0'
        }
    })

@ozon_uploader_bp.route('/api/ozon-sync/products', methods=['GET'])
def api_get_ozon_sync_products():
    """获取从OZON同步的商品列表API"""
    try:
        # 获取查询参数
        limit = int(request.args.get('limit', 10))
        offset = int(request.args.get('offset', 0))
        status_filter = request.args.get('status', '')  # all/synced/failed
        linked_filter = request.args.get('linked', '')  # all/linked/unlinked
        search = request.args.get('search', '').strip()

        logger.info(f"获取OZON同步商品列表: limit={limit}, offset={offset}, status={status_filter}, linked={linked_filter}, search={search}")

        # 构建查询条件
        where_conditions = []
        params = []

        # 状态筛选
        if status_filter == 'synced':
            where_conditions.append("api_sync_status IN ('list_synced', 'detail_synced')")
        elif status_filter == 'failed':
            where_conditions.append("api_sync_status = 'failed'")

        # 关联状态筛选
        if linked_filter == 'linked':
            where_conditions.append("local_product_id IS NOT NULL")
        elif linked_filter == 'unlinked':
            where_conditions.append("local_product_id IS NULL")

        # 搜索条件
        if search:
            where_conditions.append("(name LIKE %s OR offer_id LIKE %s)")
            params.extend([f"%{search}%", f"%{search}%"])

        # 构建WHERE子句
        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 查询商品列表
        query = f"""
            SELECT
                ozon_product_id,
                offer_id,
                local_product_id,
                name,
                price,
                old_price,
                currency_code,
                is_archived,
                is_discounted,
                has_fbo_stocks,
                has_fbs_stocks,
                api_sync_status,
                last_list_sync_at,
                last_detail_sync_at,
                ozon_created_at,
                ozon_updated_at,
                created_at,
                updated_at
            FROM ozon_products
            {where_clause}
            ORDER BY updated_at DESC
            LIMIT %s OFFSET %s
        """

        params.extend([limit, offset])
        products = db_manager.execute_query(query, params)

        # 查询总数
        count_query = f"SELECT COUNT(*) as total FROM ozon_products {where_clause}"
        count_params = params[:-2]  # 移除limit和offset参数
        total_result = db_manager.execute_query(count_query, count_params)
        total_count = total_result[0]['total'] if total_result else 0

        # 查询统计信息
        stats_query = """
            SELECT
                COUNT(*) as total,
                SUM(CASE WHEN api_sync_status IN ('list_synced', 'detail_synced') THEN 1 ELSE 0 END) as synced,
                SUM(CASE WHEN api_sync_status = 'failed' THEN 1 ELSE 0 END) as failed,
                SUM(CASE WHEN local_product_id IS NOT NULL THEN 1 ELSE 0 END) as linked,
                SUM(CASE WHEN local_product_id IS NULL THEN 1 ELSE 0 END) as unlinked,
                SUM(CASE WHEN is_archived = 1 THEN 1 ELSE 0 END) as archived,
                SUM(CASE WHEN has_fbo_stocks = 1 THEN 1 ELSE 0 END) as has_fbo_stocks,
                SUM(CASE WHEN has_fbs_stocks = 1 THEN 1 ELSE 0 END) as has_fbs_stocks
            FROM ozon_products
        """
        stats_result = db_manager.execute_query(stats_query)
        statistics = stats_result[0] if stats_result else {}

        # 处理商品数据
        processed_products = []
        for product in products:
            processed_product = {
                'ozon_product_id': product['ozon_product_id'],
                'offer_id': product['offer_id'],
                'local_product_id': product['local_product_id'],
                'name': product['name'],
                'price': float(product['price']) if product['price'] else None,
                'old_price': float(product['old_price']) if product['old_price'] else None,
                'currency_code': product['currency_code'],
                'is_archived': bool(product['is_archived']),
                'is_discounted': bool(product['is_discounted']),
                'has_fbo_stocks': bool(product['has_fbo_stocks']),
                'has_fbs_stocks': bool(product['has_fbs_stocks']),
                'api_sync_status': product['api_sync_status'],
                'last_list_sync_at': product['last_list_sync_at'].isoformat() if product['last_list_sync_at'] else None,
                'last_detail_sync_at': product['last_detail_sync_at'].isoformat() if product['last_detail_sync_at'] else None,
                'ozon_created_at': product['ozon_created_at'].isoformat() if product['ozon_created_at'] else None,
                'ozon_updated_at': product['ozon_updated_at'].isoformat() if product['ozon_updated_at'] else None,
                'created_at': product['created_at'].isoformat() if product['created_at'] else None,
                'updated_at': product['updated_at'].isoformat() if product['updated_at'] else None,
                'linked_status': 'linked' if product['local_product_id'] else 'unlinked'
            }
            processed_products.append(processed_product)

        logger.info(f"成功获取 {len(processed_products)} 个OZON同步商品，总数: {total_count}")

        return jsonify({
            'success': True,
            'data': {
                'products': processed_products,
                'total_count': total_count,
                'returned_count': len(processed_products),
                'offset': offset,
                'limit': limit,
                'has_more': offset + len(processed_products) < total_count,
                'statistics': {
                    'total': statistics.get('total', 0),
                    'synced': statistics.get('synced', 0),
                    'failed': statistics.get('failed', 0),
                    'linked': statistics.get('linked', 0),
                    'unlinked': statistics.get('unlinked', 0),
                    'archived': statistics.get('archived', 0),
                    'has_fbo_stocks': statistics.get('has_fbo_stocks', 0),
                    'has_fbs_stocks': statistics.get('has_fbs_stocks', 0)
                }
            }
        })

    except Exception as e:
        logger.error(f"获取OZON同步商品列表失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取商品列表失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-sync/sync-trigger', methods=['POST'])
def api_trigger_ozon_sync():
    """手动触发OZON商品同步API"""
    try:
        data = request.get_json() or {}
        sync_type = data.get('sync_type', 'incremental')  # full/incremental
        max_pages = int(data.get('max_pages', 5))

        logger.info(f"触发OZON商品同步: sync_type={sync_type}, max_pages={max_pages}")

        # 使用调度器的手动同步功能
        result = ozon_sync_scheduler.trigger_manual_sync(sync_type=sync_type, max_pages=max_pages)

        if result['success']:
            return jsonify({
                'success': True,
                'data': {
                    'sync_type': sync_type,
                    'statistics': result['stats'],
                    'message': result['message']
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': result['error']
            }), 400

    except Exception as e:
        logger.error(f"触发OZON同步失败: {e}")
        return jsonify({
            'success': False,
            'error': f'同步失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-sync/sync-status', methods=['GET'])
def api_get_ozon_sync_status():
    """获取OZON同步状态API"""
    try:
        # 检查是否有正在运行的同步任务
        running_tasks = db_manager.execute_query("""
            SELECT sync_task_id, api_endpoint, sync_type, started_at
            FROM ozon_api_sync_log
            WHERE sync_status = 'running'
            ORDER BY started_at DESC
            LIMIT 1
        """)

        is_syncing = len(running_tasks) > 0

        # 获取最近的同步结果
        last_sync = db_manager.execute_query("""
            SELECT sync_type, sync_status, total_requested, total_processed, total_failed,
                   started_at, completed_at, duration_seconds, error_message
            FROM ozon_api_sync_log
            WHERE sync_status IN ('success', 'failed')
            ORDER BY completed_at DESC
            LIMIT 1
        """)

        last_sync_result = None
        if last_sync:
            sync = last_sync[0]
            last_sync_result = {
                'sync_type': sync['sync_type'],
                'sync_status': sync['sync_status'],
                'total_requested': sync['total_requested'],
                'total_processed': sync['total_processed'],
                'total_failed': sync['total_failed'],
                'started_at': sync['started_at'].isoformat() if sync['started_at'] else None,
                'completed_at': sync['completed_at'].isoformat() if sync['completed_at'] else None,
                'duration_seconds': sync['duration_seconds'],
                'error_message': sync['error_message']
            }

        # 获取商品统计
        product_stats = db_manager.execute_query("""
            SELECT
                COUNT(*) as total_products,
                MAX(last_list_sync_at) as last_list_sync,
                MAX(last_detail_sync_at) as last_detail_sync
            FROM ozon_products
        """)

        stats = product_stats[0] if product_stats else {}

        return jsonify({
            'success': True,
            'data': {
                'is_syncing': is_syncing,
                'current_task': running_tasks[0] if running_tasks else None,
                'last_sync_result': last_sync_result,
                'product_statistics': {
                    'total_products': stats.get('total_products', 0),
                    'last_list_sync': stats['last_list_sync'].isoformat() if stats.get('last_list_sync') else None,
                    'last_detail_sync': stats['last_detail_sync'].isoformat() if stats.get('last_detail_sync') else None
                }
            }
        })

    except Exception as e:
        logger.error(f"获取OZON同步状态失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取同步状态失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-sync/sync-logs', methods=['GET'])
def api_get_ozon_sync_logs():
    """获取OZON同步日志API"""
    try:
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))

        # 查询同步日志
        logs = db_manager.execute_query("""
            SELECT sync_task_id, api_endpoint, sync_type, sync_status,
                   total_requested, total_processed, total_failed,
                   started_at, completed_at, duration_seconds, error_message
            FROM ozon_api_sync_log
            ORDER BY started_at DESC
            LIMIT %s OFFSET %s
        """, (limit, offset))

        # 查询总数
        total_result = db_manager.execute_query("SELECT COUNT(*) as total FROM ozon_api_sync_log")
        total_count = total_result[0]['total'] if total_result else 0

        # 处理日志数据
        processed_logs = []
        for log in logs:
            processed_log = {
                'sync_task_id': log['sync_task_id'],
                'api_endpoint': log['api_endpoint'],
                'sync_type': log['sync_type'],
                'sync_status': log['sync_status'],
                'total_requested': log['total_requested'],
                'total_processed': log['total_processed'],
                'total_failed': log['total_failed'],
                'started_at': log['started_at'].isoformat() if log['started_at'] else None,
                'completed_at': log['completed_at'].isoformat() if log['completed_at'] else None,
                'duration_seconds': log['duration_seconds'],
                'error_message': log['error_message']
            }
            processed_logs.append(processed_log)

        return jsonify({
            'success': True,
            'data': {
                'logs': processed_logs,
                'total_count': total_count,
                'returned_count': len(processed_logs),
                'offset': offset,
                'limit': limit,
                'has_more': offset + len(processed_logs) < total_count
            }
        })

    except Exception as e:
        logger.error(f"获取OZON同步日志失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取同步日志失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-sync/scheduler-status', methods=['GET'])
def api_get_scheduler_status():
    """获取调度器状态API"""
    try:
        status = ozon_sync_scheduler.get_scheduler_status()

        # 获取同步统计
        ozon_api_service = OzonApiService()
        sync_stats = ozon_api_service.get_sync_statistics()

        return jsonify({
            'success': True,
            'data': {
                'scheduler': status,
                'statistics': sync_stats
            }
        })

    except Exception as e:
        logger.error(f"获取调度器状态失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取调度器状态失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-sync/scheduler-control', methods=['POST'])
def api_control_scheduler():
    """控制调度器API（启动/停止）"""
    try:
        data = request.get_json() or {}
        action = data.get('action')  # start/stop

        if action == 'start':
            if ozon_sync_scheduler.is_running:
                return jsonify({
                    'success': False,
                    'error': '调度器已在运行中'
                }), 400

            ozon_sync_scheduler.start_scheduler()
            message = '调度器启动成功'

        elif action == 'stop':
            if not ozon_sync_scheduler.is_running:
                return jsonify({
                    'success': False,
                    'error': '调度器未在运行'
                }), 400

            ozon_sync_scheduler.stop_scheduler()
            message = '调度器停止成功'

        else:
            return jsonify({
                'success': False,
                'error': '无效的操作，支持的操作: start, stop'
            }), 400

        return jsonify({
            'success': True,
            'data': {
                'action': action,
                'message': message,
                'scheduler_status': ozon_sync_scheduler.get_scheduler_status()
            }
        })

    except Exception as e:
        logger.error(f"控制调度器失败: {e}")
        return jsonify({
            'success': False,
            'error': f'控制调度器失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-sync/cleanup', methods=['POST'])
def api_cleanup_sync_data():
    """清理同步数据API"""
    try:
        data = request.get_json() or {}
        days_old = int(data.get('days_old', 30))

        logger.info(f"手动清理同步数据，清理 {days_old} 天前的数据")

        # 创建OZON API服务实例
        ozon_api_service = OzonApiService()
        ozon_api_service.cleanup_old_sync_data(days_old=days_old)

        return jsonify({
            'success': True,
            'data': {
                'message': f'成功清理 {days_old} 天前的同步数据',
                'days_old': days_old
            }
        })

    except Exception as e:
        logger.error(f"清理同步数据失败: {e}")
        return jsonify({
            'success': False,
            'error': f'清理数据失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/sync-ratings', methods=['POST'])
def api_sync_product_ratings():
    """同步商品评分API"""
    try:
        data = request.get_json() or {}

        # 获取参数
        limit = data.get('limit', 10)  # 默认同步10个商品
        force_update = data.get('force_update', False)  # 是否强制更新已有评分

        # 验证参数
        if not isinstance(limit, int) or limit < 1 or limit > 50:
            return jsonify({
                'success': False,
                'error': '限制数量必须在1-50之间'
            }), 400

        # 获取需要同步评分的商品
        db_manager = get_db_manager()

        if force_update:
            # 强制更新：获取所有有SKU的商品
            query = """
                SELECT op.sku, op.offer_id, op.name
                FROM ozon_products op
                WHERE op.sku IS NOT NULL
                ORDER BY op.updated_at DESC
                LIMIT %s
            """
            params = (limit,)
        else:
            # 正常模式：只获取没有评分或评分过期的商品
            query = """
                SELECT op.sku, op.offer_id, op.name
                FROM ozon_products op
                LEFT JOIN ozon_product_ratings opr ON op.sku = opr.sku
                WHERE op.sku IS NOT NULL
                AND (opr.sku IS NULL OR opr.last_sync_at < DATE_SUB(NOW(), INTERVAL 7 DAY))
                ORDER BY op.updated_at DESC
                LIMIT %s
            """
            params = (limit,)

        products = db_manager.execute_query(query, params)

        if not products:
            return jsonify({
                'success': True,
                'data': {
                    'synced_count': 0,
                    'total_found': 0,
                    'message': '没有需要同步评分的商品'
                },
                'message': '没有需要同步评分的商品'
            })

        # 批量同步评分（优化版）
        from services.ozon_api_service import OzonApiService
        ozon_service = OzonApiService()
        synced_count = 0
        failed_count = 0
        results = []
        batch_size = 20  # 每批处理20个SKU

        logger.info(f"开始批量同步评分，总共 {len(products)} 个商品，批次大小: {batch_size}")

        # 按批次处理
        for i in range(0, len(products), batch_size):
            batch = products[i:i + batch_size]
            skus = [str(product['sku']) for product in batch]

            try:
                logger.info(f"处理第 {i//batch_size + 1} 批，包含 {len(skus)} 个SKU")

                # 调用批量评分API
                rating_data = ozon_service.get_product_ratings_by_skus(skus, save_to_db=True)

                if rating_data and rating_data.get('products'):
                    # 处理成功的评分数据
                    rating_products = rating_data.get('products', [])

                    # 创建SKU到评分的映射
                    sku_to_rating = {}
                    for rating_product in rating_products:
                        product_sku = str(rating_product.get('sku', ''))
                        rating_value = rating_product.get('rating')
                        sku_to_rating[product_sku] = rating_value

                    # 为批次中的每个产品记录结果
                    for product in batch:
                        sku = str(product['sku'])
                        offer_id = product['offer_id']

                        if sku in sku_to_rating:
                            synced_count += 1
                            results.append({
                                'offer_id': offer_id,
                                'sku': sku,
                                'status': 'success',
                                'rating': sku_to_rating[sku]
                            })
                            logger.info(f"✅ 评分同步成功: {offer_id}")
                        else:
                            failed_count += 1
                            results.append({
                                'offer_id': offer_id,
                                'sku': sku,
                                'status': 'failed',
                                'error': '该SKU未返回评分数据'
                            })
                            logger.warning(f"⚠️ 评分同步失败: {offer_id}")
                else:
                    # 整批失败
                    for product in batch:
                        failed_count += 1
                        results.append({
                            'offer_id': product['offer_id'],
                            'sku': str(product['sku']),
                            'status': 'failed',
                            'error': '批量API调用失败'
                        })
                        logger.warning(f"⚠️ 评分同步失败: {product['offer_id']}")

                # 避免API限流 - 批量调用间隔更长
                import time
                time.sleep(2)

            except Exception as e:
                # 批次异常处理
                for product in batch:
                    failed_count += 1
                    results.append({
                        'offer_id': product['offer_id'],
                        'sku': str(product['sku']),
                        'status': 'error',
                        'error': str(e)
                    })
                    logger.error(f"评分同步异常: {product['offer_id']} - {e}")

        return jsonify({
            'success': True,
            'data': {
                'synced_count': synced_count,
                'failed_count': failed_count,
                'total_found': len(products),
                'results': results
            },
            'message': f'评分同步完成: 成功 {synced_count} 个, 失败 {failed_count} 个'
        })

    except Exception as e:
        logger.error(f"评分同步失败: {e}")
        return jsonify({
            'success': False,
            'error': f'评分同步失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/sync-ozon-analytics', methods=['POST'])
def api_sync_ozon_analytics():
    """同步OZON数据分析数据"""
    try:
        logger.info("🔄 收到OZON数据分析同步请求")

        # 获取请求参数
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '请求参数不能为空'
            }), 400

        # 验证必需参数
        required_fields = ['date_from', 'date_to', 'metrics', 'dimension']
        for field in required_fields:
            if field not in data:
                return jsonify({
                    'success': False,
                    'error': f'缺少必需参数: {field}'
                }), 400

        # 导入数据分析服务
        from services.ozon_analytics_service import sync_ozon_analytics_data

        # 执行数据同步
        result = sync_ozon_analytics_data(
            date_from=data['date_from'],
            date_to=data['date_to'],
            metrics=data['metrics'],
            dimensions=data['dimension'],
            filters=data.get('filters', []),
            sort=data.get('sort', []),
            limit=data.get('limit', 1000),
            offset=data.get('offset', 0)
        )

        if result.get('success'):
            logger.info(f"✅ OZON数据分析同步成功: {result}")
            return jsonify({
                'success': True,
                'data': result,
                'message': 'OZON数据分析同步完成'
            })
        else:
            logger.error(f"❌ OZON数据分析同步失败: {result}")
            return jsonify({
                'success': False,
                'error': result.get('error', '同步失败'),
                'data': result
            }), 500

    except Exception as e:
        logger.error(f"❌ OZON数据分析同步请求处理失败: {e}")
        return jsonify({
            'success': False,
            'error': f'同步请求处理失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/ozon-analytics-data', methods=['GET'])
def api_get_ozon_analytics_data():
    """查询OZON数据分析数据"""
    try:
        logger.info("📊 收到OZON数据分析数据查询请求")

        # 获取查询参数
        limit = int(request.args.get('limit', 20))
        offset = int(request.args.get('offset', 0))
        date_from = request.args.get('date_from', '')
        date_to = request.args.get('date_to', '')
        dimension_type = request.args.get('dimension_type', '')
        dimension_id = request.args.get('dimension_id', '')
        query_id = request.args.get('query_id', '')

        # 默认查询近一个月的数据
        if not date_from:
            default_date_from = (datetime.now() - timedelta(days=30)).strftime('%Y-%m-%d')
            date_from = default_date_from

        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        # 构建查询条件
        where_conditions = []
        params = []

        if date_from:
            where_conditions.append("data_date >= %s")
            params.append(date_from)

        if date_to:
            where_conditions.append("data_date <= %s")
            params.append(date_to)

        if dimension_type:
            where_conditions.append("dimension_type = %s")
            params.append(dimension_type)

        if dimension_id:
            where_conditions.append("dimension_id = %s")
            params.append(dimension_id)

        if query_id:
            where_conditions.append("query_id = %s")
            params.append(query_id)

        where_clause = ""
        if where_conditions:
            where_clause = "WHERE " + " AND ".join(where_conditions)

        # 查询数据 - 包含所有26个指标字段（使用新的data_date字段）
        query = f"""
        SELECT
            id, query_id, api_timestamp, data_date,
            dimension_type, dimension_id, dimension_name,
            revenue, ordered_units, unknown_metric,
            hits_view_search, hits_view_pdp, hits_view,
            hits_tocart_search, hits_tocart_pdp, hits_tocart,
            session_view_search, session_view_pdp, session_view,
            conv_tocart_search, conv_tocart_pdp, conv_tocart,
            returns_count, cancellations, delivered_units, position_category,
            postings, postings_premium,
            created_at
        FROM ozon_analytics_data
        {where_clause}
        ORDER BY data_date DESC, revenue DESC
        LIMIT %s OFFSET %s
        """

        params.extend([limit, offset])

        analytics_data = db_manager.execute_query(query, tuple(params))

        # 查询总数
        count_query = f"""
        SELECT COUNT(*) as total
        FROM ozon_analytics_data
        {where_clause}
        """

        count_params = params[:-2]  # 移除limit和offset参数
        count_result = db_manager.execute_query(count_query, tuple(count_params))
        total_count = count_result[0]['total'] if count_result else 0

        # 查询统计信息
        stats_query = f"""
        SELECT
            dimension_type,
            COUNT(*) as record_count,
            SUM(revenue) as total_revenue,
            SUM(ordered_units) as total_orders,
            SUM(hits_view) as total_views,
            AVG(conv_tocart) as avg_conversion
        FROM ozon_analytics_data
        {where_clause}
        GROUP BY dimension_type
        ORDER BY total_revenue DESC
        """

        stats_result = db_manager.execute_query(stats_query, tuple(count_params))

        return jsonify({
            'success': True,
            'data': {
                'analytics_data': analytics_data,
                'pagination': {
                    'total_count': total_count,
                    'limit': limit,
                    'offset': offset,
                    'has_more': offset + limit < total_count
                },
                'statistics': stats_result,
                'filters': {
                    'date_from': date_from,
                    'date_to': date_to,
                    'dimension_type': dimension_type,
                    'dimension_id': dimension_id,
                    'query_id': query_id
                }
            }
        })

    except Exception as e:
        logger.error(f"❌ OZON数据分析数据查询失败: {e}")
        return jsonify({
            'success': False,
            'error': f'查询失败: {str(e)}'
        }), 500


@ozon_uploader_bp.route('/api/trigger-analytics-sync', methods=['POST'])
def trigger_analytics_sync():
    """手动触发数据分析同步"""
    try:
        data = request.get_json() or {}
        target_date = data.get('target_date')  # 可选，默认为昨天

        result = ozon_sync_scheduler.trigger_analytics_sync(target_date)

        return jsonify(result)

    except Exception as e:
        logger.error(f"触发数据分析同步失败: {e}")
        return jsonify({
            'success': False,
            'error': f'触发数据分析同步失败: {str(e)}'
        }), 500
