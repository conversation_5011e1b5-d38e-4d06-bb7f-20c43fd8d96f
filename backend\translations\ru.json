{"app_title": "Веб-панель анализа товаров", "app_subtitle": "Интеллектуальная система анализа и управления товарами", "nav_dashboard": "Главная панель", "nav_image_manager": "Управление изображениями", "nav_ozon_uploader": "Загрузка товаров", "nav_ozon_sync": "Синхронизация данных", "nav_config_manager": "Управление конфигурацией", "nav_language": "Язык", "dashboard_title": "Панель анализа товаров", "dashboard_subtitle": "Мониторинг данных товаров и результатов анализа в реальном времени", "btn_search": "Поиск", "btn_export": "Экспорт", "btn_refresh": "Обновить", "btn_upload": "Загрузить", "btn_download": "Скачать", "btn_delete": "Удалить", "btn_edit": "Редактировать", "btn_save": "Сохранить", "btn_cancel": "Отмена", "btn_confirm": "Подтвердить", "btn_close": "Закрыть", "btn_submit": "Отправить", "btn_reset": "Сброс", "btn_back": "Назад", "btn_next": "Далее", "btn_previous": "Предыдущий", "btn_start": "Начать", "btn_stop": "Остановить", "btn_pause": "Пауза", "btn_resume": "Продолжить", "btn_test_connection": "Тестировать соединение", "btn_save_config": "Сохранить конфигурацию", "btn_refresh_status": "Обновить статус", "btn_reset_defaults": "Сбросить настройки по умолчанию", "btn_test_all": "Тестировать все соединения", "btn_scan_images": "Сканировать изображения", "btn_auto_match": "Автоматическое сопоставление", "btn_upload_selected": "Загрузить выбранные", "btn_change_path": "Изменить путь", "btn_manual_match": "Ручное сопоставление", "btn_clear_match": "Очистить сопоставление", "btn_refresh_list": "Обновить список", "btn_clear_filters": "Очистить фильтры", "btn_view_task_history": "Просмотреть историю задач", "btn_retry_failed": "Повторить неудачные", "btn_retry_failed_tasks": "Повторить неудачные задачи", "btn_complete": "Завершить", "btn_done": "Готово", "btn_retry": "Повторить", "btn_validate": "Проверить", "btn_full_sync": "Начать полную синхронизацию", "system_status_title": "Состояние системы", "system_status_checking": "Проверка состояния системы...", "status_pending": "Ожидание", "status_processing": "Обработка", "status_completed": "Завершено", "status_failed": "Неудача", "status_success": "Успех", "status_error": "Ошибка", "status_warning": "Предупреждение", "status_info": "Информация", "status_synced": "Синхронизировано", "status_not_found": "Не найдено", "table_product_code": "Код товара", "table_product_name": "Название товара", "table_model_name": "Название модели", "table_price": "Цена", "table_cost": "Стоимость", "table_profit": "Прибыль", "table_profit_rate": "Норма прибыли", "table_status": "Статус", "table_actions": "Действия", "table_created_time": "Время создания", "table_updated_time": "Время обновления", "table_sync_time": "Время синхронизации", "form_product_code": "Код товара", "form_product_name": "Название товара", "form_target_profit": "Целевая прибыль", "form_procurement_cost": "Стоимость закупки", "form_shipping_cost": "Стоимость доставки", "form_commission_rate": "Комиссионная ставка", "form_first_promotion": "Первая акция", "form_second_promotion": "Вторая акция", "msg_loading": "Загрузка...", "msg_no_data": "Нет данных", "msg_operation_success": "Операция выполнена успешно", "msg_operation_failed": "Операция не удалась", "msg_confirm_delete": "Подтвердить удаление этого элемента?", "msg_confirm_upload": "Подтвердить загрузку выбранных товаров?", "msg_upload_success": "Загрузка успешна", "msg_upload_failed": "Загрузка не удалась", "msg_sync_success": "Синхронизация успешна", "msg_sync_failed": "Синхронизация не удалась", "msg_network_error": "Ошибка сети, проверьте соединение", "msg_server_error": "Ошибка сервера, повторите попытку позже", "placeholder_search": "Введите ключевые слова для поиска...", "placeholder_product_code": "Введите код товара", "placeholder_product_name": "Введите название товара", "tooltip_refresh": "Обновить данные", "tooltip_export": "Экспорт данных", "tooltip_upload": "Загрузить в OZON", "tooltip_sync": "Статус синхронизации", "tooltip_edit": "Редактировать товар", "tooltip_delete": "Удалить товар", "pagination_total": "Всего {total} записей", "pagination_page": "Страница {current} из {total}", "pagination_per_page": "Показать на странице", "pagination_items": "элементов", "filter_all": "Все", "filter_with_links": "С ссылками", "filter_without_links": "Без ссылок", "filter_uploaded": "Загружено", "filter_not_uploaded": "Не загружено", "currency_cny": "Китайский юань", "currency_rub": "Российский рубль", "currency_symbol_cny": "¥", "currency_symbol_rub": "₽", "ozon_uploader_title": "Система загрузки товаров", "ozon_uploader_subtitle": "Выберите готовые товары, массово загрузите их на торговую платформу, отслеживайте прогресс и статус загрузки в реальном времени", "products_ready_for_upload": "Товары готовые к загрузке", "refresh_list": "Обновить список", "upload_selected": "Загрузить выбранные", "search_placeholder": "Поиск модели товара, названия или кода...", "all_statuses": "Все статусы", "ready": "<PERSON><PERSON><PERSON><PERSON>", "pending": "Загрузка", "uploaded": "Загружено", "failed": "Ошибка загрузки", "clear_filters": "Очистить фильтры", "total_products": "Всего товаров", "ready_products": "Готовы", "selected_products": "Выбрано", "uploaded_products": "Загружено", "product_image": "Изображение", "product_model": "Модель товара", "product_name": "Название товара", "price": "Цена", "status": "Статус", "update_time": "Время обновления", "loading_products": "Загрузка списка товаров...", "upload_settings": "Настройки загрузки", "batch_size": "Размер пакета", "products_per_batch": "товаров/пакет", "skip_uploaded": "Пропустить загруженные товары", "auto_retry": "Автоматический повтор при ошибке", "task_monitoring": "Монитор<PERSON><PERSON><PERSON> задач", "view_task_history": "Просмотр истории задач", "retry_failed_tasks": "Повторить неудачные задачи", "system_status": "Состояние системы", "checking_system_status": "Проверка состояния системы...", "upload_progress": "Прогресс загрузки товаров", "overall_progress": "Общий прогресс", "preparing_upload": "Подготовка к загрузке...", "task_info": "Информация о задаче", "task_id": "ID задачи", "ozon_task_id": "ID задачи", "start_time": "Время начала", "detailed_status": "Подробный статус", "waiting_task_start": "Ожидание начала задачи...", "complete": "Завершить", "retry": "Повторить", "ozon_sync_title": "Управление синхронизацией данных", "ozon_sync_subtitle": "Проверка загруженных товаров и синхронизация информации о товарах с локальной базой данных", "sync_operations": "Операции синхронизации", "full_sync": "Полная синхронизация", "full_sync_desc": "Синхронизация всей информации о товарах с локальной базой данных", "start_full_sync": "Начать полную синхронизацию", "validate_specific_product": "Проверить конкретный товар", "validate_product_desc": "Проверить статус загрузки конкретного товара", "enter_product_code": "Введите код товара (например: Hotwav-Note12)", "validate": "Проверить", "sync_in_progress": "Синхронизация в процессе...", "processing_please_wait": "Обработка, пожалуйста подождите...", "sync_status_overview": "Обзор статуса синхронизации", "total_products_count": "Общее количество товаров", "synced_count": "Синхронизировано", "pending_sync": "Ожидает синхронизации", "failed_not_found": "Неудача/Не найдено", "refresh_status": "Обновить статус", "product_sync_status": "Статус синхронизации товаров", "all_status": "Все статусы", "pending_sync_status": "Ожидает синхронизации", "synced_status": "Синхронизировано", "failed_status": "Неудача", "not_found_status": "Не найдено", "search_product_code": "Поиск кода товара...", "ozon_product_id": "ID товара OZON", "ozon_status": "Статус OZON", "sync_status": "Статус синхронизации", "last_sync_time": "Время последней синхронизации", "actions": "Действия", "refresh_list_btn": "Обновить список", "config_manager_title": "Управление системной конфигурацией", "config_manager_subtitle": "Управлени<PERSON> GitHub, OZON API, базой данных и другими системными настройками", "config_loading_status": "Загрузка состояния системы...", "config_github_title": "Конфигурация GitHub хранилища изображений", "config_github_token": "Токен доступа", "config_github_token_desc": "Персональный токен доступа для GitHub API, требуются права repo", "config_github_repo": "Название репозитория", "config_github_repo_desc": "Полное название GitHub репозитория, формат: пользователь/репозиторий", "config_github_branch": "Название ветки", "config_github_branch_desc": "Название ветки для хранения изображений, по умолчанию main", "config_github_folder": "Папка хранения", "config_github_folder_desc": "Путь к папке в репозитории для хранения изображений", "config_ozon_title": "Конфигурация OZON API", "config_ozon_client_id": "Client ID", "config_ozon_client_id_desc": "Client ID, предоставленный центром продавца OZON", "config_ozon_api_key": "<PERSON> ключ", "config_ozon_api_key_desc": "API ключ, предоставленный центром продавца OZON", "config_local_title": "Конфигурация локальной папки", "config_local_folder": "Путь к папке изображений", "config_local_folder_desc": "Локальный путь для хранения изображений товаров", "config_system_operations": "Системные операции", "status_github_connected": "✅ Соединение в норме<br>✅ Конфигурация полная", "status_github_configured": "⚠️ Конфигурация установлена, но соединение не удалось<br>Проверьте Token и настройки репозитория", "status_github_not_configured": "❌ Не настроено<br>Необходимо установить Token и информацию о репозитории", "status_ozon_connected": "✅ Соединение в норме<br>✅ Конфигурация API действительна", "status_ozon_configured": "⚠️ Конфигурация установлена, но соединение не удалось<br>Проверьте Client ID и API Key", "status_ozon_not_configured": "❌ Не настроено<br>Необходимо установить Client ID и API Key", "status_database_connected": "✅ Соединение с базой данных в норме<br>✅ Все структуры таблиц полные", "status_database_failed": "❌ Соединение с базой данных не удалось<br>Проверьте конфигурацию базы данных", "status_folder_configured": "✅ Папка настроена<br>📁 {path}", "status_folder_not_configured": "❌ Локальная папка не настроена<br>Необходимо установить путь хранения изображений", "image_manager_title": "Система управления изображениями", "image_manager_subtitle": "Сканирование локальных файлов изображений, интеллектуальное сопоставление информации о товарах, массовая загрузка в GitHub хранилище изображений", "image_local_files": "Локальные файлы изображений", "image_stats_total": "Всего изображений", "image_stats_matched": "Сопоставлено", "image_stats_selected": "Выбрано", "image_stats_uploaded": "Загружено", "image_scan_prompt": "Нажмите \"Сканировать изображения\" для начала сканирования локальных файлов изображений", "image_folder_settings": "📂 Настройки папки", "image_local_folder": "Локальная папка изображений", "image_match_operations": "🔗 Операции сопоставления", "image_upload_settings": "☁️ Настройки загрузки", "image_update_existing": "Обновить существующие изображения", "image_backup_original": "Резервное копирование оригинальных файлов", "image_system_status": "⚙️ Состояние системы", "image_check_status": "Проверка состояния системы...", "image_upload_progress": "☁️ Прогресс загрузки", "image_overall_progress": "Общий прогресс", "image_progress_preparing": "Подготовка к загрузке...", "image_progress_details": "Подробный прогресс", "image_upload_details": "Детали загрузки будут отображены здесь", "image_manual_match": "🎯 Ручное сопоставление товара", "image_current_image": "Текущее изображение", "image_current_filename": "Имя файла", "image_current_model": "Извлеченная модель", "image_product_search": "Поиск товара", "image_search_placeholder": "Введите модель товара, название или код...", "image_search_results": "Результаты поиска", "image_search_results_placeholder": "Результаты поиска будут отображены здесь", "image_match_keyword": "Связанные ключевые слова (необязательно)", "image_match_keyword_placeholder": "Введите связанные ключевые слова...", "image_confirm_match": "Подтвердить сопоставление", "ozon_products_ready": "Товары готовые к загрузке", "ozon_search_placeholder": "Поиск модели товара, названия или кода...", "ozon_all_statuses": "Все статусы", "ozon_status_ready": "Готов к загрузке", "ozon_status_pending": "Загрузка", "ozon_status_uploaded": "Загружено", "ozon_status_failed": "Ошибка загрузки", "ozon_stats_total": "Всего товаров", "ozon_stats_ready": "Готовы", "ozon_stats_selected": "Выбрано", "ozon_stats_uploaded": "Загружено", "ozon_can_reupload": "можно перезагрузить", "ozon_table_image": "Изображение", "ozon_table_model": "Модель товара", "ozon_table_name": "Название товара", "ozon_table_price": "Цена", "ozon_table_status": "Статус", "ozon_table_update_time": "Время обновления", "ozon_loading_products": "Загрузка списка товаров...", "ozon_pagination_info": "Показано 1-20 из 0 записей", "ozon_pagination_pages": "Кнопки страниц будут созданы динамически через JavaScript", "ozon_pagination_per_page": "Показать на странице:", "ozon_pagination_5": "5 записей", "ozon_pagination_10": "10 записей", "ozon_pagination_20": "20 записей", "ozon_pagination_50": "50 записей", "ozon_pagination_100": "100 записей", "ozon_upload_settings": "🚀 Настройки загрузки", "ozon_batch_size": "Размер пакета", "ozon_batch_5": "5 товаров/пакет", "ozon_batch_10": "10 товаров/пакет", "ozon_batch_20": "20 товаров/пакет", "ozon_skip_uploaded": "Пропустить загруженные товары", "ozon_auto_retry": "Автоматический повтор при ошибке", "ozon_task_monitoring": "📊 Мониторинг задач", "ozon_upload_progress_title": "🚀 Прогресс загрузки OZON", "ozon_overall_progress": "Общий прогресс", "ozon_progress_preparing": "Подготовка к загрузке...", "ozon_task_info": "Информация о задаче", "ozon_ozon_task_id": "ID задачи OZON", "ozon_start_time": "Время начала", "ozon_detailed_status": "Подробный статус", "ozon_waiting_task": "Ожидание начала задачи...", "sync_title": "Управление синхронизацией товаров OZON", "sync_subtitle": "Проверка загруженных товаров и синхронизация информации о товарах OZON с локальной базой данных", "sync_full_sync": "Полная синхронизация", "sync_full_sync_desc": "Синхронизация всей информации о товарах OZON с локальной базой данных", "sync_validate_product": "Проверить конкретный товар", "sync_validate_product_desc": "Проверить статус загрузки конкретного товара", "sync_product_code_placeholder": "Введите код товара (например: Hotwav-Note12)", "sync_progress_title": "Синхронизация в процессе...", "sync_progress_text": "Обработка, пожалуйста подождите...", "sync_total_products": "Общее количество товаров", "sync_synced_products": "Синхронизировано", "sync_pending_products": "Ожидает синхронизации", "sync_failed_products": "Неудача/Не найдено", "sync_product_status": "Статус синхронизации товаров", "sync_all_status": "Все статусы", "sync_pending_status": "Ожидает синхронизации", "sync_synced_status": "Синхронизировано", "sync_failed_status": "Неудача", "sync_not_found_status": "Не найдено", "sync_search_code": "Поиск кода товара...", "sync_table_code": "Код товара", "sync_table_name": "Название товара", "sync_table_ozon_id": "ID товара OZON", "sync_table_ozon_status": "Статус OZON", "sync_table_sync_status": "Статус синхронизации", "sync_table_last_sync": "Время последней синхронизации", "sync_table_actions": "Действия", "sync_loading": "Загрузка...", "js_calculating": "🔄 Вычисление...", "js_calculation_complete": "✅ Вычисление завершено", "js_calculation_failed": "❌ Вычисление не удалось", "js_refreshing_rate": "🔄 Обновление курса валют...", "js_auto_saving": "💾 Автоматическое сохранение данных...", "js_real_time_rate": "Курс в реальном времени", "js_backup_rate": "Резервный курс", "js_save_success": "✅ Сохранено", "js_save_failed": "❌ Сохранение не удалось", "js_delete_confirm": "Вы уверены, что хотите удалить этот элемент?", "js_clear_confirm": "Вы уверены, что хотите очистить все ссылки на товары для этого ключевого слова?", "js_no_products": "Нет ссылок на товары", "js_product_link": "Ссылка на товар", "js_view_details": "Посмотреть детали →", "js_intelligent_pricing": "💰 Интеллектуальное ценообразование", "js_multi_strategy": "Многостратегический анализ", "js_pricing_strategies": "Анализ стратегий ценообразования", "js_strategy_type": "Тип стратегии и цена продажи", "js_procurement_cost": "Стоимость закупки", "js_shipping_type": "Тип доставки", "js_ozon_commission": "Комиссия OZON", "js_packaging_cost": "Стоимость упаковки", "js_total_cost": "Общая стоимость", "js_net_profit": "Чистая прибыль", "js_profit_margin": "Маржа прибыли", "js_click_to_save": "Нажмите для сохранения цены", "js_suggested_price": "Рекомендуемая цена", "js_first_promo": "Первая акция", "js_second_promo": "Вторая акция", "js_basic_cost": "💰 Базовая стоимость", "js_logistics_desc": "🚚 Описание логистики", "js_exchange_rate_info": "📊 Информация о курсе валют", "js_calculating_status": "Вычисление", "js_calculating_desc": "Пожалуйста подождите, анализируем стратегии ценообразования...", "js_calculation_error": "Ошибка вычисления", "js_calculation_error_desc": "Пожалуйста проверьте входные параметры и попробуйте снова", "js_waiting_calculation": "Ожидание вычисления", "js_waiting_desc": "Пожалуйста выберите ключевое слово и введите параметры, затем нажмите вычислить", "js_data_explanation": "📊 Объяснение данных", "js_cost_calculation": "💰 Логика расчета стоимости", "js_ozon_commission_rules": "📊 Правила комиссии OZON", "js_logistics_explanation": "🚚 Объяснение стоимости логистики", "js_pricing_strategy_desc": "🎯 Стратегия ценообразования", "js_select_keyword_first": "Пожалуйста сначала выберите ключевое слово", "js_input_cleared": "🗑️ Поле ввода очищено", "js_auto_save": "Автоматическое сохранение", "js_reset_all": "Все входные данные сброшены", "js_risk_warning": "⚠️ Предупреждение о рисках", "js_all_loss": "Все стратегии показывают убытки, рекомендуется пересмотреть структуру затрат или повысить целевую прибыль.", "js_recommended_strategy": "Рекомендуемая стратегия", "js_logistics_detail": "📦 Детали стоимости логистики {group}", "js_base_fee": "• Базовая стоимость (первый вес)", "js_weight_fee": "• Стоимость веса", "js_total_fee": "Итого", "js_close": "Закрыть", "js_delete_selected": "Удалить выбранные", "js_delete_product_confirm": "Вы уверены, что хотите удалить товар {index}?\\n\\nЭто действие нельзя отменить.", "js_delete_selected_confirm": "Вы уверены, что хотите удалить выбранные {count} товаров?\\n\\nЭто действие нельзя отменить.", "js_clear_all_confirm": "Вы уверены, что хотите очистить все ссылки на товары для ключевого слова \"{keyword}\"?\\n\\nЭто действие нельзя отменить.", "js_deleting": "🗑️ Удаление", "js_delete_success": "✅ Удалено", "js_clearing": "🗑️ Очистка всех товаров...", "js_clear_success": "✅ Все ссылки на товары очищены", "js_sort_default": "Сохранить исходный порядок", "js_sort_price_asc": "Цена от низкой к высокой, без цены в конце", "js_sort_price_desc": "Цена от высокой к низкой, без цены в конце", "js_sort_complete": "✅ Сортировка товаров завершена", "api_keyword_empty": "Ключевое слово не может быть пустым", "api_price_empty": "Цена не может быть пустой", "api_field_empty": "Ключевое слово и название поля не могут быть пустыми", "api_value_empty": "Значение параметра не может быть пустым", "api_keyword_missing": "Отсутствует параметр ключевого слова", "api_keyword_not_found": "Указанное ключевое слово не найдено", "api_product_index_empty": "Индекс товара не может быть пустым", "api_product_not_found": "Данные для указанного ключевого слова не найдены", "api_index_out_of_range": "Индекс товара {index} выходит за пределы диапазона", "api_database_update_failed": "Обновление базы данных не удалось", "api_server_error": "Ошибка сервера: {error}", "api_price_update_success": "Цена успешно обновлена", "api_price_update_failed": "Обновление цены не удалось", "api_price_save_success": "Цена успешно сохранена", "api_parameter_save_success": "Параметр {field} успешно сохранен", "api_parameter_save_failed": "Сохранение параметра {field} не удалось, возможно не найдена соответствующая запись товара", "api_delete_success": "Товар {index} успешно удален", "api_clear_success": "Все ссылки на товары для ключевого слова \"{keyword}\" успешно очищены", "api_clear_not_found": "Указанное ключевое слово не найдено или обновление базы данных не удалось", "dashboard_input_section": "Ввод базовой информации", "dashboard_show_keywords": "Показать ключевые слова", "dashboard_filter_all": "Все", "dashboard_filter_has_links": "С ссылками на товары", "dashboard_filter_no_links": "Без ссылок на товары", "dashboard_select_keyword": "Выбрать ключевое слово", "dashboard_select_keyword_placeholder": "Пожалуйста, выберите ключевое слово...", "dashboard_procurement_cost": "Стоимость закупки (¥)", "dashboard_packaging_cost": "Стоимость упаковки (¥)", "dashboard_weight": "Вес (г)", "dashboard_target_profit": "Целевая прибыль (¥)", "dashboard_promo_discount": "Скидка по акции (%)", "dashboard_second_promo": "Вторая скидка (%)", "dashboard_calculate_price": "Рассчитать цену", "dashboard_reset": "Сбросить", "dashboard_pricing_results": "Результаты анализа ценообразования", "dashboard_waiting_calculation": "Ожидание расчета", "dashboard_waiting_desc": "Пожалуйста, выберите ключевое слово и введите параметры, затем нажмите рассчитать", "dashboard_product_links": "Связанные ссылки на товары", "dashboard_price_sort": "Сортировка по цене", "dashboard_price_asc": "Цена от низкой к высокой", "dashboard_price_desc": "Цена от высокой к низкой", "dashboard_default_sort": "Сортировка по умолчанию", "dashboard_select_all": "Выбрать все", "dashboard_delete_selected": "Удалить выбранные", "dashboard_no_products": "Нет ссылок на товары", "dashboard_no_products_desc": "Автоматически отображается после выбора ключевого слова", "dashboard_pagination_desc": "Элементы управления пагинацией будут загружены динамически через JavaScript", "dashboard_shortcut_help": "Справка по горячим клавишам", "dashboard_shortcut_help_f1": "Справка по горячим клавишам (F1)", "dashboard_back_to_top": "Вернуться наверх", "dashboard_quick_calculate": "Быстрый расчет", "dashboard_quick_calculate_ctrl": "Быстрый расчет (Ctrl+Enter)", "js_language_switch_failed": "Переключение языка не удалось", "js_language_switch_request_failed": "Запрос переключения языка не удался"}