#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON数据分析工具 - 基于 /v1/analytics/data API
"""

import sys
import os
import json
import requests
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from decimal import Decimal

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import get_db_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class OzonAnalyticsTool:
    """OZON数据分析工具"""
    
    def __init__(self):
        self.db_manager = get_db_manager()
        self.base_url = "https://api-seller.ozon.ru"
        self.api_path = "/v1/analytics/data"
        self.headers = {
            'Content-Type': 'application/json',
            'Client-Id': '2024407390',  # 需要配置实际的Client-Id
            'Api-Key': 'your-api-key-here'  # 需要配置实际的API密钥
        }
    
    def json_serializer(self, obj):
        """JSON序列化处理函数"""
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, Decimal):
            return float(obj)
        raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")
    
    def get_analytics_data(self, date_from: str, date_to: str, 
                          metrics: List[str], dimensions: List[str],
                          filters: List[Dict] = None, 
                          sort: List[Dict] = None,
                          limit: int = 1000, offset: int = 0) -> Dict:
        """
        调用OZON数据分析API获取数据
        
        Args:
            date_from: 开始日期 (YYYY-MM-DD)
            date_to: 结束日期 (YYYY-MM-DD)
            metrics: 指标列表
            dimensions: 分组维度列表
            filters: 过滤器列表
            sort: 排序设置
            limit: 返回数据量限制
            offset: 分页偏移量
        
        Returns:
            API响应数据
        """
        try:
            url = f"{self.base_url}{self.api_path}"
            
            request_data = {
                "date_from": date_from,
                "date_to": date_to,
                "metrics": metrics,
                "dimension": dimensions,
                "filters": filters or [],
                "sort": sort or [],
                "limit": limit,
                "offset": offset
            }
            
            logger.info(f"🔄 调用OZON数据分析API")
            logger.info(f"📡 请求URL: {url}")
            logger.info(f"📊 查询参数: {json.dumps(request_data, ensure_ascii=False)}")
            
            response = requests.post(
                url,
                json=request_data,
                headers=self.headers,
                timeout=60
            )
            
            if response.status_code == 200:
                data = response.json()
                logger.info(f"✅ 数据获取成功")
                
                if 'result' in data and 'data' in data['result']:
                    result_data = data['result']['data']
                    totals = data['result'].get('totals', [])
                    logger.info(f"📈 返回数据条数: {len(result_data)}")
                    logger.info(f"📊 总计值: {totals}")
                    
                    return {
                        'success': True,
                        'data': result_data,
                        'totals': totals,
                        'timestamp': data.get('timestamp'),
                        'query_params': request_data
                    }
                else:
                    logger.warning("⚠️ API返回数据格式异常")
                    return {
                        'success': False,
                        'error': 'API返回数据格式异常',
                        'raw_response': data
                    }
            else:
                error_msg = f"API请求失败: {response.status_code} - {response.text}"
                logger.error(f"❌ {error_msg}")
                return {
                    'success': False,
                    'error': error_msg,
                    'status_code': response.status_code
                }
                
        except Exception as e:
            error_msg = f"数据分析API调用异常: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }
    
    def analyze_product_performance(self, days_back: int = 30) -> Dict:
        """
        分析商品表现数据
        
        Args:
            days_back: 分析最近多少天的数据
        
        Returns:
            分析结果
        """
        logger.info(f"📊 开始分析商品表现数据，回溯 {days_back} 天")
        
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            date_from = start_date.strftime('%Y-%m-%d')
            date_to = end_date.strftime('%Y-%m-%d')
            
            # 查询基础指标（所有用户可用）
            basic_result = self.get_analytics_data(
                date_from=date_from,
                date_to=date_to,
                metrics=['revenue', 'ordered_units'],
                dimensions=['sku'],
                sort=[{'key': 'revenue', 'order': 'DESC'}],
                limit=100
            )
            
            if not basic_result.get('success'):
                return basic_result
            
            # 分析数据
            analysis = {
                'success': True,
                'period': f'{date_from} 到 {date_to}',
                'total_products': len(basic_result['data']),
                'total_revenue': basic_result['totals'][0] if basic_result['totals'] else 0,
                'total_orders': basic_result['totals'][1] if len(basic_result['totals']) > 1 else 0,
                'top_products': [],
                'performance_summary': {}
            }
            
            # 分析TOP商品
            for item in basic_result['data'][:10]:  # 取前10名
                dimensions = item.get('dimensions', [])
                metrics = item.get('metrics', [])
                
                if dimensions and metrics:
                    sku_info = dimensions[0] if dimensions else {}
                    product_info = {
                        'sku': sku_info.get('id', 'unknown'),
                        'name': sku_info.get('name', 'unknown'),
                        'revenue': metrics[0] if metrics else 0,
                        'orders': metrics[1] if len(metrics) > 1 else 0,
                        'avg_order_value': metrics[0] / metrics[1] if len(metrics) > 1 and metrics[1] > 0 else 0
                    }
                    analysis['top_products'].append(product_info)
            
            # 计算性能摘要
            if analysis['total_orders'] > 0:
                analysis['performance_summary'] = {
                    'avg_order_value': analysis['total_revenue'] / analysis['total_orders'],
                    'revenue_concentration': (analysis['top_products'][0]['revenue'] / analysis['total_revenue'] * 100) if analysis['top_products'] else 0,
                    'active_products': analysis['total_products']
                }
            
            logger.info(f"✅ 商品表现分析完成")
            return analysis
            
        except Exception as e:
            error_msg = f"商品表现分析失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }
    
    def analyze_time_trends(self, days_back: int = 30) -> Dict:
        """
        分析时间趋势数据
        
        Args:
            days_back: 分析最近多少天的数据
        
        Returns:
            趋势分析结果
        """
        logger.info(f"📈 开始分析时间趋势，回溯 {days_back} 天")
        
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            date_from = start_date.strftime('%Y-%m-%d')
            date_to = end_date.strftime('%Y-%m-%d')
            
            # 按日查询趋势数据
            trend_result = self.get_analytics_data(
                date_from=date_from,
                date_to=date_to,
                metrics=['revenue', 'ordered_units'],
                dimensions=['day'],
                sort=[{'key': 'day', 'order': 'ASC'}],
                limit=days_back
            )
            
            if not trend_result.get('success'):
                return trend_result
            
            # 分析趋势
            daily_data = []
            total_revenue = 0
            total_orders = 0
            
            for item in trend_result['data']:
                dimensions = item.get('dimensions', [])
                metrics = item.get('metrics', [])
                
                if dimensions and metrics:
                    day_info = dimensions[0] if dimensions else {}
                    day_data = {
                        'date': day_info.get('id', 'unknown'),
                        'revenue': metrics[0] if metrics else 0,
                        'orders': metrics[1] if len(metrics) > 1 else 0
                    }
                    daily_data.append(day_data)
                    total_revenue += day_data['revenue']
                    total_orders += day_data['orders']
            
            # 计算趋势指标
            analysis = {
                'success': True,
                'period': f'{date_from} 到 {date_to}',
                'daily_data': daily_data,
                'trend_summary': {
                    'total_days': len(daily_data),
                    'avg_daily_revenue': total_revenue / len(daily_data) if daily_data else 0,
                    'avg_daily_orders': total_orders / len(daily_data) if daily_data else 0,
                    'best_day': max(daily_data, key=lambda x: x['revenue']) if daily_data else None,
                    'worst_day': min(daily_data, key=lambda x: x['revenue']) if daily_data else None
                }
            }
            
            logger.info(f"✅ 时间趋势分析完成")
            return analysis
            
        except Exception as e:
            error_msg = f"时间趋势分析失败: {str(e)}"
            logger.error(f"❌ {error_msg}")
            return {
                'success': False,
                'error': error_msg
            }
    
    def save_analytics_to_db(self, analytics_data: Dict, analysis_type: str) -> bool:
        """
        保存分析数据到数据库
        
        Args:
            analytics_data: 分析数据
            analysis_type: 分析类型
        
        Returns:
            是否保存成功
        """
        try:
            if not self.db_manager:
                logger.error("❌ 数据库连接失败")
                return False
            
            # 创建分析记录表（如果不存在）
            create_table_sql = """
            CREATE TABLE IF NOT EXISTS ozon_analytics_reports (
                id INT AUTO_INCREMENT PRIMARY KEY,
                analysis_type VARCHAR(50) NOT NULL,
                period_from DATE,
                period_to DATE,
                total_revenue DECIMAL(15,2),
                total_orders INT,
                total_products INT,
                analysis_data JSON,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                INDEX idx_analysis_type (analysis_type),
                INDEX idx_period (period_from, period_to)
            )
            """
            
            self.db_manager.execute_query(create_table_sql, fetch=False)
            
            # 提取关键数据
            period_from = analytics_data.get('period', '').split(' 到 ')[0] if ' 到 ' in analytics_data.get('period', '') else None
            period_to = analytics_data.get('period', '').split(' 到 ')[1] if ' 到 ' in analytics_data.get('period', '') else None
            
            # 插入分析记录
            insert_sql = """
            INSERT INTO ozon_analytics_reports (
                analysis_type, period_from, period_to,
                total_revenue, total_orders, total_products,
                analysis_data, created_at
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, NOW())
            """
            
            params = (
                analysis_type,
                period_from,
                period_to,
                analytics_data.get('total_revenue', 0),
                analytics_data.get('total_orders', 0),
                analytics_data.get('total_products', 0),
                json.dumps(analytics_data, ensure_ascii=False, default=self.json_serializer)
            )
            
            result = self.db_manager.execute_query(insert_sql, params, fetch=False)
            
            if result:
                logger.info(f"✅ 分析数据保存成功: {analysis_type}")
                return True
            else:
                logger.error(f"❌ 分析数据保存失败: {analysis_type}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 保存分析数据异常: {e}")
            return False


def main():
    """主函数 - 命令行工具入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='OZON数据分析工具')
    parser.add_argument('--action', choices=['product', 'trend', 'both'], 
                       default='both', help='分析类型')
    parser.add_argument('--days', type=int, default=30, 
                       help='分析天数（默认30天）')
    parser.add_argument('--save', action='store_true',
                       help='是否保存到数据库')
    
    args = parser.parse_args()
    
    print("📊 OZON数据分析工具")
    print("=" * 50)
    
    tool = OzonAnalyticsTool()
    
    if args.action in ['product', 'both']:
        print(f"🔍 分析商品表现数据（最近 {args.days} 天）...")
        product_result = tool.analyze_product_performance(args.days)
        
        print("\n📄 商品表现分析结果:")
        print(json.dumps(product_result, ensure_ascii=False, indent=2, default=tool.json_serializer))
        
        if args.save and product_result.get('success'):
            tool.save_analytics_to_db(product_result, 'product_performance')
    
    if args.action in ['trend', 'both']:
        print(f"\n📈 分析时间趋势数据（最近 {args.days} 天）...")
        trend_result = tool.analyze_time_trends(args.days)
        
        print("\n📄 时间趋势分析结果:")
        print(json.dumps(trend_result, ensure_ascii=False, indent=2, default=tool.json_serializer))
        
        if args.save and trend_result.get('success'):
            tool.save_analytics_to_db(trend_result, 'time_trends')
    
    print("\n" + "=" * 50)
    print("🎉 分析完成！")


if __name__ == '__main__':
    main()
