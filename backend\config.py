#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用配置文件
"""

import os
from datetime import timedelta
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

class Config:
    """基础配置"""
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'ozon-dashboard-secret-key-2024'
    JSON_AS_ASCII = False
    
    # 数据库配置 - 临时使用SQLite进行演示
    DATABASE_HOST = os.environ.get('DATABASE_HOST') or 'localhost'
    DATABASE_PORT = int(os.environ.get('DATABASE_PORT') or 3306)
    DATABASE_USER = os.environ.get('DATABASE_USER') or 'root'
    DATABASE_PASSWORD = os.environ.get('DATABASE_PASSWORD') or 'root'
    DATABASE_NAME = os.environ.get('DATABASE_NAME') or 'model_database'

    # 临时使用SQLite数据库进行演示
    USE_SQLITE = False
    SQLITE_DB_PATH = 'demo_database.db'
    
    # 汇率API配置
    EXCHANGE_RATE_APIS = [
        'https://api.exchangerate-api.com/v4/latest/CNY',
        'https://api.fixer.io/latest?base=CNY&symbols=RUB',
    ]
    EXCHANGE_RATE_CACHE_TIMEOUT = timedelta(hours=1)
    DEFAULT_EXCHANGE_RATE = 13.5
    
    # 分页配置
    DEFAULT_PAGE_SIZE = 10
    MAX_PAGE_SIZE = 50
    
    # OZON定价配置
    DEFAULT_PROCUREMENT_COST = 46
    OZON_COMMISSION_RATES = {
        'low': 0.12,   # 12% for products under 1500 RUB
        'high': 0.20   # 20% for products over 1500 RUB
    }
    OZON_COMMISSION_THRESHOLD = 1500
    
    # 物流配置
    LOGISTICS_CONFIG = {
        'small': {
            'weight_range': (1, 2000),  # 1-2000g
            'price_range': (1501, 7000),  # 1501-7000₽
            'base_cost_cny': 16,
            'per_gram_cost_cny': 0.043
        }
    }
    
    # 促销配置
    PROMOTION_CONFIG = {
        'primary_discount': 0.15,    # 15%
        'secondary_discount': 0.10   # 10%
    }

    # ===========================================
    # 敏感API密钥配置 - 从环境变量读取
    # ===========================================

    # GitHub配置 - 移除硬编码密钥，强制使用环境变量
    GITHUB_TOKEN = os.environ.get('GITHUB_TOKEN') or ''
    GITHUB_REPO = os.environ.get('GITHUB_REPO') or 'righBai/images'
    GITHUB_BRANCH = os.environ.get('GITHUB_BRANCH') or 'main'
    GITHUB_FOLDER = os.environ.get('GITHUB_FOLDER') or 'images'

    # OZON API配置 - 移除硬编码密钥，强制使用环境变量
    OZON_CLIENT_ID = os.environ.get('OZON_CLIENT_ID') or '2962343'
    OZON_API_KEY = os.environ.get('OZON_API_KEY') or ''

    # 配置加密密钥
    CONFIG_ENCRYPTION_KEY = os.environ.get('CONFIG_ENCRYPTION_KEY') or ''

class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """测试环境配置"""
    DEBUG = True
    TESTING = True

# 配置字典
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
