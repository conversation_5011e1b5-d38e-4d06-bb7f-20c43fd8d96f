#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GitHub图床服务
基于tempfile/github_image_uploader_mod.py，提供图片上传到GitHub的服务
"""

import os
import sys
import requests
import base64
import uuid
import datetime
import hashlib
import logging
from typing import List, Tuple, Optional, Dict
from werkzeug.utils import secure_filename
from models.database import db_manager
from services.config_service import config_service

logger = logging.getLogger(__name__)

# 支持的图片格式
IMAGE_EXTS = {'.jpg', '.jpeg', '.png', '.gif', '.webp'}

class GitHubService:
    """GitHub图床服务类"""
    
    def __init__(self):
        self.token = None
        self.repo = None
        self.branch = None
        self._load_config()
    
    def _load_config(self):
        """加载GitHub配置 - 优先从环境变量读取"""
        try:
            import os

            # 优先从环境变量读取敏感配置
            # 注意：敏感配置只从环境变量读取，不再从config_service获取
            self.token = os.environ.get('GITHUB_TOKEN') or ''
            self.repo = os.environ.get('GITHUB_REPO') or config_service.get_config('github_repo', 'righBai/images')
            self.branch = os.environ.get('GITHUB_BRANCH') or config_service.get_config('github_branch', 'main')

            if not self.token:
                logger.warning("GitHub token未配置，请设置GITHUB_TOKEN环境变量")
            else:
                logger.info(f"GitHub配置加载成功，仓库: {self.repo}, 分支: {self.branch}")

        except Exception as e:
            logger.error(f"加载GitHub配置失败: {e}")
    
    def reload_config(self):
        """重新加载配置"""
        self._load_config()
    
    def file_to_base64(self, filepath: str) -> str:
        """将文件转换为base64编码"""
        try:
            with open(filepath, 'rb') as f:
                return base64.b64encode(f.read()).decode()
        except Exception as e:
            logger.error(f"文件转base64失败 {filepath}: {e}")
            raise
    
    def calculate_file_hash(self, filepath: str) -> str:
        """计算文件哈希值"""
        try:
            with open(filepath, 'rb') as f:
                return hashlib.md5(f.read()).hexdigest()
        except Exception as e:
            logger.error(f"计算文件哈希失败 {filepath}: {e}")
            return ""
    
    def upload_single_image(self, local_path: str, custom_filename: str = None) -> Tuple[bool, str, str]:
        """
        上传单个图片到GitHub
        
        Args:
            local_path: 本地文件路径
            custom_filename: 自定义文件名（可选）
        
        Returns:
            (成功标志, GitHub URL, 仓库路径)
        """
        try:
            if not os.path.isfile(local_path):
                raise ValueError(f"文件不存在: {local_path}")
            
            # 检查文件格式
            ext = os.path.splitext(local_path)[1].lower()
            if ext not in IMAGE_EXTS:
                raise ValueError(f"不支持的图片格式: {ext}")
            
            # 检查配置
            if not self.token or not self.repo:
                raise ValueError("GitHub配置不完整，请检查token和repo设置")
            
            # 生成唯一文件名
            if custom_filename:
                file_name = custom_filename
                if not file_name.endswith(ext):
                    file_name += ext
            else:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                unique_id = str(uuid.uuid4())[:8]
                file_name = f"{timestamp}_{unique_id}{ext}"
            
            # 固定上传到models文件夹
            repo_path = f"models/{file_name}"
            
            # 构建API URL
            url = f"https://api.github.com/repos/{self.repo}/contents/{repo_path}"
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Accept": "application/vnd.github.v3+json"
            }
            
            # 转换文件为base64
            content_b64 = self.file_to_base64(local_path)
            
            # 构建请求数据
            data = {
                "message": "upload model image to models folder",
                "content": content_b64,
                "branch": self.branch
            }
            
            # 发送请求
            response = requests.put(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 201:
                raw_url = f"https://raw.githubusercontent.com/{self.repo}/{self.branch}/{repo_path}"
                logger.info(f"图片上传成功: {local_path} -> {raw_url}")
                return True, raw_url, repo_path
            else:
                error_msg = f"上传失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return False, "", ""
                
        except Exception as e:
            logger.error(f"上传图片失败 {local_path}: {e}")
            return False, "", ""

    def upload_image_from_memory(self, file_content: bytes, filename: str) -> Tuple[bool, str, str]:
        """从内存直接上传图片到GitHub，支持覆盖已存在的文件"""
        try:
            logger.info(f"开始从内存上传图片: {filename} (大小: {len(file_content)} 字节)")

            # 检查配置
            if not self.token or not self.repo:
                raise ValueError("GitHub配置不完整，请检查token和repo设置")

            # 检查文件格式
            ext = os.path.splitext(filename)[1].lower()
            if ext not in IMAGE_EXTS:
                raise ValueError(f"不支持的图片格式: {ext}")

            # 生成安全的文件名
            safe_filename = secure_filename(filename)
            if not safe_filename:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                unique_id = str(uuid.uuid4())[:8]
                safe_filename = f"{timestamp}_{unique_id}{ext}"

            # 固定上传到models文件夹
            repo_path = f"models/{safe_filename}"

            # 构建API URL
            url = f"https://api.github.com/repos/{self.repo}/contents/{repo_path}"
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Accept": "application/vnd.github.v3+json"
            }

            # 转换文件内容为base64
            import base64
            content_b64 = base64.b64encode(file_content).decode('utf-8')

            # 检查文件是否已存在，如果存在则获取SHA用于更新
            logger.info(f"检查文件路径: {repo_path}")
            existing_sha = self.get_file_sha(repo_path)

            # 构建请求数据
            data = {
                "message": f"upload {safe_filename} via dashboard" + (" (update)" if existing_sha else " (create)"),
                "content": content_b64,
                "branch": self.branch
            }

            # 如果文件已存在，添加SHA参数用于更新
            if existing_sha:
                data["sha"] = existing_sha
                logger.info(f"文件已存在，将进行覆盖更新: {repo_path} (SHA: {existing_sha[:8]}...)")
            else:
                logger.info(f"文件不存在，将创建新文件: {repo_path}")

            # 发送请求
            response = requests.put(url, headers=headers, json=data, timeout=30)

            # 检查响应状态码 - 201表示创建，200表示更新
            if response.status_code in [200, 201]:
                raw_url = f"https://raw.githubusercontent.com/{self.repo}/{self.branch}/{repo_path}"
                action = "更新" if existing_sha else "创建"
                logger.info(f"内存图片{action}成功: {filename} -> {raw_url}")
                return True, raw_url, repo_path
            else:
                error_msg = f"上传失败，状态码: {response.status_code}, 响应: {response.text}"
                logger.error(error_msg)
                return False, "", ""

        except Exception as e:
            logger.error(f"从内存上传图片失败 {filename}: {e}")
            return False, "", ""

    def upload_images_batch(self, local_dir: str, file_list: List[str] = None) -> Tuple[List[str], List[str]]:
        """
        批量上传图片到GitHub
        
        Args:
            local_dir: 本地目录路径
            file_list: 指定文件列表（可选，如果不指定则上传目录下所有图片）
        
        Returns:
            (成功的URL列表, 成功的仓库路径列表)
        """
        if not os.path.isdir(local_dir):
            logger.error(f"目录不存在: {local_dir}")
            return [], []
        
        image_urls = []
        repo_paths = []
        
        # 确定要上传的文件列表
        if file_list:
            files_to_upload = [os.path.join(local_dir, f) for f in file_list if os.path.isfile(os.path.join(local_dir, f))]
        else:
            files_to_upload = []
            for fname in os.listdir(local_dir):
                fpath = os.path.join(local_dir, fname)
                ext = os.path.splitext(fname)[1].lower()
                if os.path.isfile(fpath) and ext in IMAGE_EXTS:
                    files_to_upload.append(fpath)
        
        logger.info(f"准备上传 {len(files_to_upload)} 个图片文件")
        
        # 逐个上传
        for fpath in files_to_upload:
            try:
                success, url, repo_path = self.upload_single_image(fpath)
                if success:
                    image_urls.append(url)
                    repo_paths.append(repo_path)
                else:
                    logger.warning(f"跳过上传失败的文件: {fpath}")
            except Exception as e:
                logger.error(f"上传文件时发生错误 {fpath}: {e}")
        
        logger.info(f"批量上传完成: {len(image_urls)}/{len(files_to_upload)} 个文件成功")
        return image_urls, repo_paths
    
    def get_file_sha(self, file_path: str) -> Optional[str]:
        """获取GitHub文件的SHA值"""
        try:
            url = f"https://api.github.com/repos/{self.repo}/contents/{file_path}?ref={self.branch}"
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Accept": "application/vnd.github.v3+json"
            }

            logger.info(f"检查文件是否存在: {file_path}")
            response = requests.get(url, headers=headers, timeout=10)

            if response.status_code == 200:
                sha = response.json().get('sha')
                logger.info(f"文件已存在，获取到SHA: {file_path} -> {sha[:8]}...")
                return sha
            elif response.status_code == 404:
                logger.info(f"文件不存在: {file_path}")
                return None
            else:
                logger.warning(f"获取文件SHA时收到意外状态码 {response.status_code}: {file_path}")
                return None

        except Exception as e:
            logger.error(f"获取文件SHA失败 {file_path}: {e}")
            return None
    
    def delete_image(self, repo_path: str) -> bool:
        """删除GitHub上的图片"""
        try:
            sha = self.get_file_sha(repo_path)
            if not sha:
                logger.error(f"无法获取文件SHA: {repo_path}")
                return False
            
            url = f"https://api.github.com/repos/{self.repo}/contents/{repo_path}"
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Accept": "application/vnd.github.v3+json"
            }
            
            data = {
                "message": "delete image via dashboard",
                "sha": sha,
                "branch": self.branch
            }
            
            response = requests.delete(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                logger.info(f"删除图片成功: {repo_path}")
                return True
            else:
                logger.error(f"删除图片失败 {repo_path}: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"删除图片时发生错误 {repo_path}: {e}")
            return False
    
    def delete_images_batch(self, repo_paths: List[str]) -> int:
        """批量删除GitHub上的图片"""
        success_count = 0
        for repo_path in repo_paths:
            if self.delete_image(repo_path):
                success_count += 1
        
        logger.info(f"批量删除完成: {success_count}/{len(repo_paths)} 个文件成功")
        return success_count
    
    def test_connection(self) -> bool:
        """测试GitHub连接"""
        try:
            if not self.token or not self.repo:
                return False

            url = f"https://api.github.com/repos/{self.repo}"
            headers = {
                "Authorization": f"Bearer {self.token}",
                "Accept": "application/vnd.github.v3+json"
            }

            response = requests.get(url, headers=headers, timeout=10)
            return response.status_code == 200

        except Exception as e:
            logger.error(f"GitHub连接测试失败: {e}")
            return False

# 全局GitHub服务实例
github_service = GitHubService()
