#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
产品内容自动生成工具
根据model_name自动生成product_name、description和json_content
"""

import logging

logger = logging.getLogger(__name__)

def generate_product_name(model_name: str) -> str:
    """
    根据model_name生成产品名称
    
    Args:
        model_name: 产品型号名称
        
    Returns:
        生成的产品名称
    """
    return f"Дисплей для смартфона, предназначенный для модели {model_name}, цифровой сенсорный компонент экрана, черный каркас."

def generate_description(model_name: str) -> str:
    """
    根据model_name生成产品描述
    
    Args:
        model_name: 产品型号名称
        
    Returns:
        生成的产品描述
    """
    return f"""✔ 【Совместимость】: Подходит для {model_name}. Проверьте модель вашего устройства перед покупкой.

✔ 【Замена】: Для замены старого, разбитого, поцарапанного или поврежденного LCD дисплей.

✔ 【Качество】: Все продукты проходят строгий двойной тест на заводе, 100% идеальное состояние перед отправкой.

✔【Установка】: Это технический процесс, обычно выполняемый профессиональными инженерами. Мы настоятельно рекомендуем посмотреть обучающее видео перед ремонтом и рекомендуем доверить работу профессиональным инженерам, чтобы снизить риск повреждения телефона.

✔ 【Сервис】: Если у вас возникнут какие-либо вопросы, пожалуйста, свяжитесь с нами, и мы ответим вам в течение 24 часов."""

def generate_json_content(model_name: str) -> str:
    """
    根据model_name生成JSON内容
    
    Args:
        model_name: 产品型号名称
        
    Returns:
        生成的JSON内容字符串
    """
    return f"""{{
  "content": [
    {{
      "widgetName": "list",
      "theme": "bullet",
      "blocks": [
        {{
          "text": {{
            "size": "size2",
            "align": "left",
            "color": "color1",
            "items": [
              {{
                "type": "text",
                "content": "Подходит для {model_name}. Проверьте модель вашего устройства перед покупкой."
              }}
            ]
          }},
          "title": {{
            "items": [
              {{
                "type": "text",
                "content": "【Совместимость】"
              }}
            ],
            "size": "size4",
            "align": "left",
            "color": "color1"
          }}
        }},
        {{
          "text": {{
            "size": "size2",
            "align": "left",
            "color": "color1",
            "items": [
              {{
                "type": "text",
                "content": "Для замены старого, разбитого, поцарапанного или поврежденного LCD дисплей."
              }}
            ]
          }},
          "title": {{
            "items": [
              {{
                "type": "text",
                "content": "【Замена】"
              }}
            ],
            "size": "size4",
            "align": "left",
            "color": "color1"
          }}
        }},
        {{
          "text": {{
            "size": "size2",
            "align": "left",
            "color": "color1",
            "items": [
              {{
                "type": "text",
                "content": "Все продукты проходят строгий двойной тест на заводе, 100% идеальное состояние перед отправкой."
              }}
            ]
          }},
          "title": {{
            "items": [
              {{
                "type": "text",
                "content": "【Качество】"
              }}
            ],
            "size": "size4",
            "align": "left",
            "color": "color1"
          }}
        }},
        {{
          "text": {{
            "size": "size2",
            "align": "left",
            "color": "color1",
            "items": [
              {{
                "type": "text",
                "content": "Если у вас возникнут какие-либо вопросы, пожалуйста, свяжитесь с нами, и мы ответим вам в течение 24 часов."
              }}
            ]
          }},
          "title": {{
            "items": [
              {{
                "type": "text",
                "content": "【Сервис】"
              }}
            ],
            "size": "size4",
            "align": "left",
            "color": "color1"
          }}
        }}
      ]
    }}
  ],
  "version": 0.3
}}"""

def get_default_product_attributes() -> dict:
    """
    获取默认的产品属性
    
    Returns:
        包含默认属性的字典
    """
    return {
        'price_before_discount': 300.0,
        'gross_weight': 180,
        'package_width': 150,
        'package_height': 100,
        'package_length': 200,
        'brand': '无品牌',
        'color': '黑色',
        'type': '移动设备备件',
        'spare_type': '显示器/触摸屏',
        'tags': '#lcd',
        'material': '玻璃',
        'video_url': 'https://cloud.video.taobao.com/play/u/2219793157051/p/2/e/6/t/1/518545084555.mp4'
    }

def generate_complete_product_data(model_name: str, item_code: str = None) -> dict:
    """
    生成完整的产品数据
    
    Args:
        model_name: 产品型号名称
        item_code: 产品编码（可选，如果不提供会自动生成）
        
    Returns:
        包含所有字段的产品数据字典
    """
    if not item_code:
        item_code = model_name.replace(' ', '-')
    
    # 生成内容字段
    product_data = {
        'item_code': item_code,
        'model_name': model_name,
        'product_name': generate_product_name(model_name),
        'description': generate_description(model_name),
        'json_content': generate_json_content(model_name)
    }
    
    # 添加默认属性
    product_data.update(get_default_product_attributes())
    
    return product_data

def auto_fill_missing_fields(product_data: dict) -> dict:
    """
    自动填充缺失的字段

    Args:
        product_data: 现有的产品数据字典

    Returns:
        填充后的产品数据字典
    """
    model_name = product_data.get('model_name', '')
    if not model_name:
        logger.warning("model_name为空，无法自动填充字段")
        return product_data

    # 填充内容字段
    if not product_data.get('product_name'):
        product_data['product_name'] = generate_product_name(model_name)

    if not product_data.get('description'):
        product_data['description'] = generate_description(model_name)

    if not product_data.get('json_content'):
        product_data['json_content'] = generate_json_content(model_name)

    # 填充默认属性
    defaults = get_default_product_attributes()
    for key, value in defaults.items():
        if not product_data.get(key):
            product_data[key] = value

    return product_data

def insert_product_with_auto_content(db_manager, model_name: str, item_code: str = None, **kwargs) -> bool:
    """
    插入产品记录，自动生成内容字段

    Args:
        db_manager: 数据库管理器实例
        model_name: 产品型号名称
        item_code: 产品编码（可选）
        **kwargs: 其他可选字段

    Returns:
        插入是否成功
    """
    try:
        # 生成完整的产品数据
        product_data = generate_complete_product_data(model_name, item_code)

        # 用传入的参数覆盖默认值
        product_data.update(kwargs)

        # 构建插入查询
        insert_query = """
        INSERT INTO product_info (
            item_code, model_name, product_name, description, json_content,
            price_before_discount, gross_weight, package_width, package_height, package_length,
            brand, color, type, spare_type, tags, material, video_url
        )
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """

        result = db_manager.execute_query(
            insert_query,
            (
                product_data['item_code'], product_data['model_name'],
                product_data['product_name'], product_data['description'], product_data['json_content'],
                product_data['price_before_discount'], product_data['gross_weight'],
                product_data['package_width'], product_data['package_height'], product_data['package_length'],
                product_data['brand'], product_data['color'], product_data['type'],
                product_data['spare_type'], product_data['tags'], product_data['material'], product_data['video_url']
            ),
            fetch=False
        )

        return result and result > 0

    except Exception as e:
        logger.error(f"插入产品记录失败: {e}")
        return False

def update_missing_content_fields(db_manager) -> dict:
    """
    批量更新缺失内容字段的记录

    Args:
        db_manager: 数据库管理器实例

    Returns:
        更新结果字典
    """
    try:
        # 查找需要更新的记录
        query = """
        SELECT id, model_name
        FROM product_info
        WHERE product_name IS NULL OR description IS NULL OR json_content IS NULL
        """

        records = db_manager.execute_query(query)
        if not records:
            return {'success': True, 'updated_count': 0, 'message': '没有需要更新的记录'}

        updated_count = 0
        for record in records:
            model_name = record['model_name']
            if not model_name:
                continue

            # 生成内容
            product_name = generate_product_name(model_name)
            description = generate_description(model_name)
            json_content = generate_json_content(model_name)

            # 更新记录
            update_query = """
            UPDATE product_info
            SET product_name = COALESCE(product_name, %s),
                description = COALESCE(description, %s),
                json_content = COALESCE(json_content, %s)
            WHERE id = %s
            """

            result = db_manager.execute_query(
                update_query,
                (product_name, description, json_content, record['id']),
                fetch=False
            )

            if result and result > 0:
                updated_count += 1

        return {
            'success': True,
            'updated_count': updated_count,
            'total_found': len(records),
            'message': f'成功更新 {updated_count} 条记录'
        }

    except Exception as e:
        logger.error(f"批量更新内容字段失败: {e}")
        return {'success': False, 'error': str(e)}
