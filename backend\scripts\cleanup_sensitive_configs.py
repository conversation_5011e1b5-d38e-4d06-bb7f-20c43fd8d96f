#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理数据库敏感配置脚本
警告：此脚本会永久删除数据库中的敏感配置，请确保已迁移到环境变量
使用方法：python cleanup_sensitive_configs.py --confirm
"""

import os
import sys
import logging
import argparse
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from models.database import get_db_manager
from services.config_service import is_sensitive_config

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def list_sensitive_configs():
    """列出数据库中的敏感配置"""
    try:
        db_manager = get_db_manager()
        if not db_manager:
            logger.error("无法连接到数据库")
            return []
            
        query = """
        SELECT config_key, config_type, description, created_at
        FROM system_config 
        WHERE is_sensitive = TRUE OR config_type = 'encrypted'
        ORDER BY config_key
        """
        
        results = db_manager.execute_query(query)
        return results
        
    except Exception as e:
        logger.error(f"查询敏感配置失败: {e}")
        return []

def verify_env_variables():
    """验证环境变量是否已设置"""
    required_env_vars = {
        'GITHUB_TOKEN': 'GitHub Personal Access Token',
        'OZON_API_KEY': 'OZON API Key'
    }
    
    missing_vars = []
    for var_name, description in required_env_vars.items():
        if not os.environ.get(var_name):
            missing_vars.append(f"{var_name} ({description})")
    
    return missing_vars

def delete_sensitive_configs(dry_run=True):
    """删除数据库中的敏感配置"""
    try:
        db_manager = get_db_manager()
        if not db_manager:
            logger.error("无法连接到数据库")
            return False
        
        # 查询要删除的配置
        sensitive_configs = list_sensitive_configs()
        
        if not sensitive_configs:
            logger.info("✅ 数据库中没有敏感配置需要清理")
            return True
        
        logger.info(f"📋 找到 {len(sensitive_configs)} 个敏感配置:")
        for config in sensitive_configs:
            logger.info(f"  - {config['config_key']} ({config['config_type']})")
        
        if dry_run:
            logger.info("🔍 这是预览模式，不会实际删除数据")
            return True
        
        # 执行删除
        delete_query = """
        DELETE FROM system_config 
        WHERE is_sensitive = TRUE OR config_type = 'encrypted'
        """
        
        affected_rows = db_manager.execute_query(delete_query, fetch=False)
        logger.info(f"✅ 成功删除 {len(sensitive_configs)} 个敏感配置")
        
        # 同时清理相关的审计日志（可选）
        cleanup_audit = input("是否同时清理相关的审计日志？(y/N): ").lower().strip()
        if cleanup_audit == 'y':
            audit_query = """
            DELETE FROM config_audit_log 
            WHERE is_encrypted = TRUE
            """
            db_manager.execute_query(audit_query, fetch=False)
            logger.info("✅ 相关审计日志已清理")
        
        return True
        
    except Exception as e:
        logger.error(f"删除敏感配置失败: {e}")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='清理数据库中的敏感配置')
    parser.add_argument('--confirm', action='store_true', help='确认执行删除操作')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际删除')
    
    args = parser.parse_args()
    
    logger.info("🧹 敏感配置清理工具")
    logger.info("=" * 50)
    
    # 1. 列出当前敏感配置
    logger.info("🔍 检查数据库中的敏感配置...")
    sensitive_configs = list_sensitive_configs()
    
    if not sensitive_configs:
        logger.info("✅ 数据库中没有敏感配置，无需清理")
        return True
    
    logger.info(f"📋 找到 {len(sensitive_configs)} 个敏感配置:")
    for config in sensitive_configs:
        logger.info(f"  - {config['config_key']} ({config['config_type']}) - {config['description']}")
    
    # 2. 验证环境变量
    logger.info("\n🔍 验证环境变量设置...")
    missing_vars = verify_env_variables()
    
    if missing_vars:
        logger.error("❌ 以下环境变量未设置:")
        for var in missing_vars:
            logger.error(f"  - {var}")
        logger.error("请先设置这些环境变量，然后再运行清理脚本")
        return False
    
    logger.info("✅ 所有必需的环境变量都已设置")
    
    # 3. 执行清理
    if args.dry_run:
        logger.info("\n🔍 预览模式 - 显示将要删除的配置:")
        delete_sensitive_configs(dry_run=True)
        logger.info("💡 使用 --confirm 参数执行实际删除")
        return True
    
    if not args.confirm:
        logger.warning("\n⚠️ 这将永久删除数据库中的敏感配置！")
        logger.warning("请确保已经:")
        logger.warning("1. 备份了重要数据")
        logger.warning("2. 在.env文件中设置了所有必需的环境变量")
        logger.warning("3. 测试了应用程序功能正常")
        
        confirm = input("\n确认执行删除操作？(yes/no): ").lower().strip()
        if confirm != 'yes':
            logger.info("❌ 操作已取消")
            return False
    
    logger.info("\n🧹 开始清理敏感配置...")
    success = delete_sensitive_configs(dry_run=False)
    
    if success:
        logger.info("✅ 敏感配置清理完成！")
        logger.info("📌 建议重启应用程序以确保配置生效")
    else:
        logger.error("❌ 清理过程中出现错误")
    
    return success

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
