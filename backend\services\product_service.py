"""
产品服务模块
提供产品数据的查询、更新和管理功能，包含缓存优化
"""

import logging
from typing import List, Dict, Optional, Any
from models.database import db_manager
from utils.cache import cache_result, CacheKeyGenerator, get_cache_config

logger = logging.getLogger(__name__)

class ProductService:
    """产品服务类"""
    
    def __init__(self):
        self.cache_config = get_cache_config('product_list')
    
    # @cache_result(ttl=300, key_prefix="products:")  # 暂时禁用缓存进行调试
    def get_products_ready_for_upload(self, limit: int = 5, offset: int = 0,
                                    filters: Optional[Dict] = None) -> Dict[str, Any]:
        """
        获取准备上传的产品列表（带缓存）
        
        Args:
            limit: 限制数量
            offset: 偏移量
            filters: 筛选条件
        
        Returns:
            包含产品列表和统计信息的字典
        """
        try:
            # 构建基础查询 - 包含所有前端需要的字段
            base_query = """
                SELECT
                    id,
                    model_name,
                    item_code,
                    product_name,
                    price_cny,
                    procurement_cost,
                    main_image_url,
                    ozon_upload_status,
                    updated_at,
                    created_at,
                    -- 基本产品信息字段
                    tags,
                    description,
                    json_content,
                    keywords,
                    group_name,
                    -- 产品规格字段
                    qty_per_pack,
                    material,
                    display_type,
                    body_frame,
                    frame_color,
                    size_mm,
                    country_of_origin,
                    original_pack_qty,
                    dimensions,
                    -- 来源信息字段
                    source_url,
                    source_file,
                    config,
                    -- OZON API相关字段
                    ozon_product_id,
                    ozon_sku,
                    ozon_name,
                    ozon_price,
                    ozon_status,
                    ozon_visible,
                    -- 物理属性字段
                    gross_weight,
                    package_width,
                    package_height,
                    package_length,
                    price_before_discount,
                    -- 定价参数字段
                    packaging_cost,
                    weight,
                    target_profit,
                    promo_discount,
                    second_promo_discount,
                    -- 图片管理字段
                    image_upload_status,
                    github_image_urls,
                    last_image_update,
                    last_ozon_upload,
                    -- 同步相关字段
                    sync_status,
                    last_sync_time,
                    -- 其他可编辑字段
                    video_url
                FROM product_info
                WHERE 1=1
            """
            
            params = []
            
            # 应用筛选条件
            if filters:
                if filters.get('has_image'):
                    base_query += " AND main_image_url IS NOT NULL AND main_image_url != '' AND main_image_url != 'NULL'"

                if filters.get('status'):
                    if filters['status'] == 'ready':
                        # 'ready' 状态包括未上传和失败的产品
                        base_query += " AND (ozon_upload_status IS NULL OR ozon_upload_status IN ('none', 'failed'))"
                    else:
                        base_query += " AND ozon_upload_status = %s"
                        params.append(filters['status'])

                if filters.get('search'):
                    search_term = f"%{filters['search']}%"
                    base_query += " AND (model_name LIKE %s OR item_code LIKE %s OR product_name LIKE %s)"
                    params.extend([search_term, search_term, search_term])
            
            # 构建动态排序逻辑
            sort_by = filters.get('sort_by', 'updated_at')
            sort_order = filters.get('sort_order', 'desc')

            # 构建ORDER BY子句
            if sort_by == 'last_ozon_upload':
                # 对于last_ozon_upload字段，需要特殊处理NULL值
                if sort_order == 'desc':
                    # 降序：有值的在前，NULL在后
                    order_clause = "last_ozon_upload DESC, updated_at DESC"
                else:
                    # 升序：NULL在前，有值的在后
                    order_clause = "last_ozon_upload IS NULL DESC, last_ozon_upload ASC, updated_at DESC"
            else:
                # 对于其他字段，使用标准排序
                order_clause = f"{sort_by} {sort_order.upper()}"
                # 添加次要排序字段确保结果稳定
                if sort_by != 'updated_at':
                    order_clause += ", updated_at DESC"

            base_query += f"""
                ORDER BY {order_clause}
                LIMIT %s OFFSET %s
            """
            params.extend([limit, offset])
            
            # 执行查询
            products = db_manager.execute_query(base_query, params) or []
            
            # 获取总数 - 重新构建计数查询，避免ORDER BY和LIMIT的复杂性
            count_query = """
                SELECT COUNT(*)
                FROM product_info
                WHERE 1=1
            """
            count_params = []

            # 应用相同的筛选条件
            if filters:
                if filters.get('has_image'):
                    count_query += " AND main_image_url IS NOT NULL AND main_image_url != '' AND main_image_url != 'NULL'"

                if filters.get('status'):
                    if filters['status'] == 'ready':
                        count_query += " AND (ozon_upload_status IS NULL OR ozon_upload_status IN ('none', 'failed'))"
                    else:
                        count_query += " AND ozon_upload_status = %s"
                        count_params.append(filters['status'])

                if filters.get('search'):
                    search_term = f"%{filters['search']}%"
                    count_query += " AND (model_name LIKE %s OR item_code LIKE %s OR product_name LIKE %s)"
                    count_params.extend([search_term, search_term, search_term])

            count_result = db_manager.execute_query(count_query, count_params)
            total_count = count_result[0]['COUNT(*)'] if count_result else 0
            
            logger.info(f"获取产品列表: {len(products)} 个产品，总数: {total_count}")
            
            return {
                'products': products,
                'total_count': total_count,
                'limit': limit,
                'offset': offset,
                'has_more': (offset + len(products)) < total_count
            }
            
        except Exception as e:
            logger.error(f"获取产品列表失败: {e}")
            raise
    
    @cache_result(ttl=600, key_prefix="products:")
    def get_product_by_id(self, product_id: int) -> Optional[Dict[str, Any]]:
        """
        根据ID获取产品详情（带缓存）
        
        Args:
            product_id: 产品ID
        
        Returns:
            产品详情字典或None
        """
        try:
            # 使用明确的字段列表，确保与前端接口一致
            query = """
                SELECT
                    id,
                    model_name,
                    item_code,
                    product_name,
                    price_cny,
                    procurement_cost,
                    main_image_url,
                    ozon_upload_status,
                    updated_at,
                    created_at,
                    -- 基本产品信息字段
                    tags,
                    description,
                    json_content,
                    keywords,
                    group_name,
                    -- 产品规格字段
                    qty_per_pack,
                    material,
                    display_type,
                    body_frame,
                    frame_color,
                    size_mm,
                    country_of_origin,
                    original_pack_qty,
                    dimensions,
                    -- 来源信息字段
                    source_url,
                    source_file,
                    config,
                    -- OZON API相关字段
                    ozon_product_id,
                    ozon_sku,
                    ozon_name,
                    ozon_price,
                    ozon_status,
                    ozon_visible,
                    -- 物理属性字段
                    gross_weight,
                    package_width,
                    package_height,
                    package_length,
                    price_before_discount,
                    -- 定价参数字段
                    packaging_cost,
                    weight,
                    target_profit,
                    promo_discount,
                    second_promo_discount,
                    -- 图片管理字段
                    image_upload_status,
                    github_image_urls,
                    last_image_update,
                    last_ozon_upload,
                    -- 同步相关字段
                    sync_status,
                    last_sync_time,
                    -- 其他可编辑字段
                    video_url
                FROM product_info
                WHERE id = %s
            """
            
            result = db_manager.execute_query(query, (product_id,))
            
            if result:
                logger.debug(f"获取产品详情: ID {product_id}")
                return result[0]
            else:
                logger.warning(f"产品不存在: ID {product_id}")
                return None
                
        except Exception as e:
            logger.error(f"获取产品详情失败: {e}")
            raise
    
    def update_product_upload_status(self, product_id: int, status: str, 
                                   ozon_product_id: Optional[str] = None) -> bool:
        """
        更新产品上传状态
        
        Args:
            product_id: 产品ID
            status: 上传状态
            ozon_product_id: OZON产品ID
        
        Returns:
            是否更新成功
        """
        try:
            if ozon_product_id:
                query = """
                    UPDATE product_info 
                    SET ozon_upload_status = %s, ozon_product_id = %s, updated_at = NOW()
                    WHERE id = %s
                """
                params = (status, ozon_product_id, product_id)
            else:
                query = """
                    UPDATE product_info 
                    SET ozon_upload_status = %s, updated_at = NOW()
                    WHERE id = %s
                """
                params = (status, product_id)
            
            result = db_manager.execute_query(query, params, fetch=False)
            
            if result:
                logger.info(f"更新产品状态: ID {product_id}, 状态: {status}")
                # 清除相关缓存
                self._invalidate_product_cache(product_id)
                return True
            else:
                logger.warning(f"更新产品状态失败: ID {product_id}")
                return False
                
        except Exception as e:
            logger.error(f"更新产品状态异常: {e}")
            raise
    
    def batch_update_upload_status(self, product_updates: List[Dict[str, Any]]) -> int:
        """
        批量更新产品上传状态
        
        Args:
            product_updates: 更新列表，每个元素包含 product_id, status, ozon_product_id
        
        Returns:
            成功更新的数量
        """
        success_count = 0
        
        for update in product_updates:
            try:
                if self.update_product_upload_status(
                    update['product_id'], 
                    update['status'], 
                    update.get('ozon_product_id')
                ):
                    success_count += 1
            except Exception as e:
                logger.error(f"批量更新失败: {update}, 错误: {e}")
        
        logger.info(f"批量更新完成: {success_count}/{len(product_updates)} 成功")
        return success_count
    
    @cache_result(ttl=5, key_prefix="products:")  # 设置短TTL测试修复
    def get_upload_statistics(self) -> Dict[str, int]:
        """
        获取上传统计信息（带缓存）
        
        Returns:
            统计信息字典
        """
        try:
            query = """
                SELECT
                    COUNT(*) as total_count,
                    SUM(CASE WHEN main_image_url IS NOT NULL AND main_image_url != '' AND main_image_url != 'NULL' THEN 1 ELSE 0 END) as has_image_count,
                    SUM(CASE WHEN (ozon_upload_status IS NULL OR ozon_upload_status IN ('none', 'failed'))
                         AND main_image_url IS NOT NULL AND main_image_url != '' AND main_image_url != 'NULL'
                         THEN 1 ELSE 0 END) as ready_count,
                    SUM(CASE WHEN ozon_upload_status = 'uploaded' THEN 1 ELSE 0 END) as uploaded_count,
                    SUM(CASE WHEN ozon_upload_status IN ('pending', 'uploading', 'retrying') THEN 1 ELSE 0 END) as pending_count,
                    SUM(CASE WHEN ozon_upload_status = 'failed' THEN 1 ELSE 0 END) as failed_count
                FROM product_info
            """
            
            result = db_manager.execute_query(query)
            
            if result:
                stats = result[0]
                # 直接使用SQL查询计算的ready_count，确保与分页查询逻辑一致

                return {
                    'total_count': stats['total_count'],
                    'has_image_count': stats['has_image_count'],
                    'ready_count': stats['ready_count'],
                    'uploaded_count': stats['uploaded_count'],
                    'pending_count': stats['pending_count'],
                    'failed_count': stats['failed_count']
                }
            else:
                return {
                    'total_count': 0,
                    'has_image_count': 0,
                    'ready_count': 0,
                    'uploaded_count': 0,
                    'pending_count': 0,
                    'failed_count': 0
                }
                
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            raise
    
    def search_products(self, query: str, limit: int = 10) -> List[Dict[str, Any]]:
        """
        搜索产品
        
        Args:
            query: 搜索关键词
            limit: 限制数量
        
        Returns:
            产品列表
        """
        try:
            search_query = """
                SELECT id, model_name, item_code, product_name, main_image_url
                FROM product_info 
                WHERE model_name LIKE %s OR item_code LIKE %s OR product_name LIKE %s
                ORDER BY 
                    CASE 
                        WHEN model_name = %s THEN 1
                        WHEN item_code = %s THEN 2
                        WHEN model_name LIKE %s THEN 3
                        WHEN item_code LIKE %s THEN 4
                        ELSE 5
                    END,
                    updated_at DESC
                LIMIT %s
            """
            
            search_term = f"%{query}%"
            exact_term = query
            prefix_term = f"{query}%"
            
            params = [
                search_term, search_term, search_term,  # WHERE条件
                exact_term, exact_term,                 # 精确匹配排序
                prefix_term, prefix_term,               # 前缀匹配排序
                limit
            ]
            
            result = db_manager.execute_query(search_query, params) or []
            
            logger.debug(f"搜索产品: '{query}' 找到 {len(result)} 个结果")
            return result
            
        except Exception as e:
            logger.error(f"搜索产品失败: {e}")
            raise
    
    def _invalidate_product_cache(self, product_id: int) -> None:
        """清除产品相关缓存"""
        from utils.cache import invalidate_cache_pattern
        
        # 清除产品详情缓存
        invalidate_cache_pattern(f"product_detail:{product_id}")
        
        # 清除产品列表缓存
        invalidate_cache_pattern("products:get_products_ready_for_upload")
        
        # 清除统计信息缓存
        invalidate_cache_pattern("products:get_upload_statistics")

# 全局产品服务实例
product_service = ProductService()
