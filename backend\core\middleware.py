"""
Flask中间件配置模块
统一管理请求/响应处理中间件
"""

import time
import gzip
import logging
import uuid
from flask import Flask, request, g
from datetime import datetime

logger = logging.getLogger(__name__)

def register_middleware(app: Flask):
    """
    注册所有中间件
    
    Args:
        app: Flask应用实例
    """
    try:
        # 注册请求ID中间件
        register_request_id_middleware(app)
        
        # 注册性能监控中间件
        register_performance_middleware(app)
        
        # 注册响应压缩中间件
        register_compression_middleware(app)
        
        # 注册安全头中间件
        register_security_headers_middleware(app)
        
        logger.info("中间件注册完成")
        
    except Exception as e:
        logger.error(f"中间件注册失败: {e}")

def register_request_id_middleware(app: Flask):
    """注册请求ID中间件"""
    @app.before_request
    def add_request_id():
        """为每个请求添加唯一ID"""
        g.request_id = str(uuid.uuid4())[:8]
        g.start_time = time.time()

def register_performance_middleware(app: Flask):
    """注册性能监控中间件"""
    @app.before_request
    def before_request():
        """请求开始时记录时间"""
        if not hasattr(g, 'start_time'):
            g.start_time = time.time()
        
        # 记录请求开始
        logger.debug(f"[{g.get('request_id', 'unknown')}] 请求开始: {request.method} {request.path}")

    @app.after_request
    def after_request(response):
        """请求结束时记录性能数据"""
        try:
            if hasattr(g, 'start_time'):
                duration = time.time() - g.start_time
                request_id = g.get('request_id', 'unknown')
                
                # 记录慢查询（超过1秒）
                if duration > 1.0:
                    logger.warning(f"[{request_id}] 慢查询警告: {request.method} {request.path} - {duration:.3f}s")
                
                # 添加性能头部
                response.headers['X-Response-Time'] = f"{duration:.3f}s"
                response.headers['X-Request-ID'] = request_id
                
                # 记录请求完成
                logger.debug(f"[{request_id}] 请求完成: {request.method} {request.path} - {duration:.3f}s - {response.status_code}")
        
        except Exception as e:
            logger.error(f"性能监控中间件错误: {e}")
        
        return response

def register_compression_middleware(app: Flask):
    """注册响应压缩中间件"""
    @app.after_request
    def compress_response(response):
        """自动压缩API响应"""
        try:
            # 只压缩大于1KB的JSON响应
            if (response.content_type and 'application/json' in response.content_type and
                len(response.get_data()) > 1024):

                accept_encoding = request.headers.get('Accept-Encoding', '')
                if 'gzip' in accept_encoding:
                    compressed_data = gzip.compress(response.get_data())
                    response.set_data(compressed_data)
                    response.headers['Content-Encoding'] = 'gzip'
                    response.headers['Content-Length'] = len(compressed_data)
                    response.headers['Vary'] = 'Accept-Encoding'
                    
                    logger.debug(f"响应已压缩: {len(response.get_data())} -> {len(compressed_data)} bytes")
        
        except Exception as e:
            logger.warning(f"响应压缩失败: {e}")
        
        return response

def register_security_headers_middleware(app: Flask):
    """注册安全头中间件"""
    @app.after_request
    def add_security_headers(response):
        """添加安全相关的HTTP头"""
        try:
            # 添加安全头
            response.headers['X-Content-Type-Options'] = 'nosniff'
            response.headers['X-Frame-Options'] = 'DENY'
            response.headers['X-XSS-Protection'] = '1; mode=block'
            
            # 添加时间戳
            response.headers['X-Timestamp'] = datetime.utcnow().isoformat()
            
            # 开发环境下添加调试信息
            if app.config.get('DEBUG'):
                response.headers['X-Debug-Mode'] = 'true'
        
        except Exception as e:
            logger.error(f"安全头中间件错误: {e}")
        
        return response

def register_cors_middleware(app: Flask):
    """注册CORS中间件（备用方案）"""
    @app.after_request
    def after_request_cors(response):
        """手动处理CORS（如果Flask-CORS不可用）"""
        try:
            origin = request.headers.get('Origin')
            if origin:
                # 允许的源 - 从环境变量获取
                import os
                frontend_url = os.environ.get('FRONTEND_URL', 'http://localhost:3000')
                default_origins = [frontend_url, 'http://127.0.0.1:3000']
                allowed_origins = app.config.get('CORS_ORIGINS', default_origins)
                if origin in allowed_origins:
                    response.headers['Access-Control-Allow-Origin'] = origin
                    response.headers['Access-Control-Allow-Credentials'] = 'true'
                    response.headers['Access-Control-Allow-Methods'] = 'GET, POST, PUT, DELETE, OPTIONS'
                    response.headers['Access-Control-Allow-Headers'] = 'Content-Type, Authorization, X-Requested-With'
        
        except Exception as e:
            logger.error(f"CORS中间件错误: {e}")
        
        return response

def register_logging_middleware(app: Flask):
    """注册日志中间件"""
    @app.before_request
    def log_request_info():
        """记录请求信息"""
        try:
            request_id = g.get('request_id', 'unknown')
            logger.info(f"[{request_id}] {request.method} {request.path} - IP: {request.remote_addr}")
            
            # 记录请求体（仅在调试模式下）
            if app.config.get('DEBUG') and request.is_json:
                logger.debug(f"[{request_id}] 请求体: {request.get_json()}")
        
        except Exception as e:
            logger.error(f"请求日志记录失败: {e}")

    @app.after_request
    def log_response_info(response):
        """记录响应信息"""
        try:
            request_id = g.get('request_id', 'unknown')
            logger.info(f"[{request_id}] 响应: {response.status_code}")
            
            # 记录错误响应的详细信息
            if response.status_code >= 400:
                logger.warning(f"[{request_id}] 错误响应: {response.status_code} - {response.get_data(as_text=True)[:200]}")
        
        except Exception as e:
            logger.error(f"响应日志记录失败: {e}")
        
        return response

def register_rate_limiting_middleware(app: Flask):
    """注册限流中间件（简单实现）"""
    from collections import defaultdict
    from time import time
    
    # 简单的内存限流器
    request_counts = defaultdict(list)
    
    @app.before_request
    def rate_limit():
        """简单的限流检查"""
        try:
            if not app.config.get('RATE_LIMITING_ENABLED', False):
                return
            
            client_ip = request.remote_addr
            current_time = time()
            
            # 清理过期的请求记录
            request_counts[client_ip] = [
                req_time for req_time in request_counts[client_ip]
                if current_time - req_time < 60  # 1分钟窗口
            ]
            
            # 检查请求频率
            max_requests = app.config.get('RATE_LIMIT_PER_MINUTE', 100)
            if len(request_counts[client_ip]) >= max_requests:
                logger.warning(f"限流触发: IP {client_ip} 超过 {max_requests} 请求/分钟")
                return {'error': 'Rate limit exceeded'}, 429
            
            # 记录当前请求
            request_counts[client_ip].append(current_time)
        
        except Exception as e:
            logger.error(f"限流中间件错误: {e}")

# 中间件健康检查
def check_middleware_health():
    """检查中间件健康状态"""
    return {
        'request_id': True,
        'performance': True,
        'compression': True,
        'security_headers': True,
        'logging': True
    }
