#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通用电商平台API数据查询脚本
查询和展示存储在数据库中的API数据

作者: Claude 4.0 Sonnet  
创建时间: 2025-06-25
"""

import json
import sys
import os
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import get_db_manager


class PlatformApiDataQuery:
    """通用电商平台API数据查询器"""
    
    def __init__(self):
        self.db = get_db_manager()
    
    def list_all_apis(self, platform=None):
        """列出所有API"""
        title = f"📋 {platform + ' ' if platform else ''}API列表"
        print(title)
        print("=" * 80)
        
        sql = """
        SELECT id, platform_name, api_name, api_path, api_category, http_method, created_at
        FROM platform_api_data 
        """
        params = ()
        
        if platform:
            sql += " WHERE platform_name = %s"
            params = (platform,)
        
        sql += " ORDER BY platform_name, api_category, id"
        
        try:
            results = self.db.execute_query(sql, params)
            
            if not results:
                print("❌ 没有找到API数据")
                return
            
            print(f"{'ID':<4} {'平台':<8} {'分类':<12} {'API名称':<20} {'路径':<25} {'方法':<6} {'创建时间':<12}")
            print("-" * 80)
            
            for row in results:
                created_at = row[6].strftime('%Y-%m-%d') if row[6] else ''
                print(f"{row[0]:<4} {row[1]:<8} {row[4]:<12} {row[2]:<20} {row[3]:<25} {row[5]:<6} {created_at:<12}")
            
            print(f"\n✅ 共找到 {len(results)} 个API接口")
            
        except Exception as e:
            print(f"❌ 查询失败: {str(e)}")
    
    def show_api_detail(self, api_id):
        """显示API详情"""
        print(f"🔍 API详情 (ID: {api_id})")
        print("=" * 100)
        
        sql = """
        SELECT * FROM platform_api_data WHERE id = %s
        """
        
        try:
            results = self.db.execute_query(sql, (api_id,))
            
            if not results:
                print("❌ 没有找到指定的API")
                return
            
            api = results[0]
            
            print(f"🏷️  平台名称: {api[1]}")
            print(f"📌 API名称: {api[2]}")
            print(f"🔗 API路径: {api[3]}")
            print(f"🌐 基础URL: {api[4]}")
            print(f"📡 HTTP方法: {api[5]}")
            print(f"📊 API版本: {api[6] or '未指定'}")
            print(f"📝 描述: {api[7] or '无'}")
            print(f"🏷️  分类: {api[8] or '未分类'}")
            print(f"🔐 认证类型: {api[9] or '未指定'}")
            print()
            
            # 认证头
            if api[10]:
                print("🔑 认证头:")
                auth_headers = json.loads(api[10])
                for key, value in auth_headers.items():
                    print(f"  {key}: {value}")
                print()
            
            # 请求示例
            if api[13]:
                print("📋 请求示例:")
                request_example = json.loads(api[13])
                print(json.dumps(request_example, indent=2, ensure_ascii=False))
                print()
            
            # 响应示例
            if api[15]:
                print("📄 响应示例:")
                response_example = json.loads(api[15])
                print(json.dumps(response_example, indent=2, ensure_ascii=False))
                print()
            
            # 使用说明
            if api[18]:
                print("💡 使用说明:")
                print(api[18])
                print()
            
            # 限制说明
            if api[19]:
                print("⚠️ 限制说明:")
                print(api[19])
                print()
            
            print(f"✅ 状态: {'启用' if api[21] else '禁用'}")
            print(f"⏰ 创建时间: {api[23]}")
            print(f"🔄 更新时间: {api[24]}")
            
        except Exception as e:
            print(f"❌ 查询失败: {str(e)}")
    
    def search_apis(self, keyword, platform=None):
        """搜索API"""
        title = f"🔍 搜索API: '{keyword}'"
        if platform:
            title += f" (平台: {platform})"
        print(title)
        print("=" * 80)
        
        sql = """
        SELECT id, platform_name, api_name, api_path, api_description, api_category
        FROM platform_api_data 
        WHERE (api_name LIKE %s OR api_path LIKE %s OR api_description LIKE %s OR api_category LIKE %s)
        """
        params = [f"%{keyword}%"] * 4
        
        if platform:
            sql += " AND platform_name = %s"
            params.append(platform)
        
        sql += " ORDER BY platform_name, api_category, id"
        
        try:
            results = self.db.execute_query(sql, params)
            
            if not results:
                print("❌ 没有找到匹配的API")
                return
            
            for row in results:
                print(f"ID: {row[0]} | 平台: {row[1]} | 分类: {row[5]}")
                print(f"名称: {row[2]}")
                print(f"路径: {row[3]}")
                print(f"描述: {row[4] or '无'}")
                print("-" * 60)
            
            print(f"✅ 找到 {len(results)} 个匹配的API")
            
        except Exception as e:
            print(f"❌ 搜索失败: {str(e)}")
    
    def list_platforms(self):
        """列出所有平台"""
        print("🏢 平台列表")
        print("=" * 50)
        
        sql = """
        SELECT platform_name, COUNT(*) as api_count, 
               COUNT(DISTINCT api_category) as category_count
        FROM platform_api_data 
        GROUP BY platform_name
        ORDER BY platform_name
        """
        
        try:
            results = self.db.execute_query(sql)
            
            if not results:
                print("❌ 没有找到平台数据")
                return
            
            print(f"{'平台名称':<15} {'API数量':<10} {'分类数量':<10}")
            print("-" * 40)
            
            for row in results:
                print(f"{row[0]:<15} {row[1]:<10} {row[2]:<10}")
            
            print(f"\n✅ 共 {len(results)} 个平台")
            
        except Exception as e:
            print(f"❌ 查询失败: {str(e)}")
    
    def list_categories(self, platform=None):
        """列出API分类"""
        title = f"📂 {platform + ' ' if platform else ''}API分类"
        print(title)
        print("=" * 50)
        
        sql = """
        SELECT api_category, COUNT(*) as api_count
        FROM platform_api_data 
        """
        params = ()
        
        if platform:
            sql += " WHERE platform_name = %s"
            params = (platform,)
        
        sql += " GROUP BY api_category ORDER BY api_category"
        
        try:
            results = self.db.execute_query(sql, params)
            
            if not results:
                print("❌ 没有找到分类数据")
                return
            
            print(f"{'分类名称':<15} {'API数量':<10}")
            print("-" * 30)
            
            for row in results:
                category = row[0] or '未分类'
                print(f"{category:<15} {row[1]:<10}")
            
            print(f"\n✅ 共 {len(results)} 个分类")
            
        except Exception as e:
            print(f"❌ 查询失败: {str(e)}")
    
    def export_api_json(self, api_id, output_file):
        """导出API为JSON文件"""
        print(f"📤 导出API (ID: {api_id}) 到文件: {output_file}")
        
        sql = """
        SELECT * FROM platform_api_data WHERE id = %s
        """
        
        try:
            results = self.db.execute_query(sql, (api_id,))
            
            if not results:
                print("❌ 没有找到指定的API")
                return
            
            api = results[0]
            
            # 构建导出数据
            export_data = {
                'id': api[0],
                'platform_name': api[1],
                'api_name': api[2],
                'api_path': api[3],
                'base_url': api[4],
                'http_method': api[5],
                'api_version': api[6],
                'api_description': api[7],
                'api_category': api[8],
                'auth_type': api[9],
                'auth_headers': json.loads(api[10]) if api[10] else None,
                'request_headers': json.loads(api[11]) if api[11] else None,
                'request_body_schema': json.loads(api[12]) if api[12] else None,
                'request_example': json.loads(api[13]) if api[13] else None,
                'request_parameters': json.loads(api[14]) if api[14] else None,
                'response_schema': json.loads(api[15]) if api[15] else None,
                'response_example': json.loads(api[16]) if api[16] else None,
                'response_codes': json.loads(api[17]) if api[17] else None,
                'parameters_doc': api[18],
                'usage_notes': api[19],
                'limitations': api[20],
                'examples_doc': api[21],
                'is_active': api[22],
                'last_tested': api[23].isoformat() if api[23] else None,
                'test_status': api[24],
                'created_at': api[25].isoformat() if api[25] else None,
                'updated_at': api[26].isoformat() if api[26] else None
            }
            
            # 写入文件
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ 成功导出到: {output_file}")
            
        except Exception as e:
            print(f"❌ 导出失败: {str(e)}")
    
    def get_statistics(self):
        """获取统计信息"""
        print("📊 平台API数据统计")
        print("=" * 50)
        
        try:
            # 总数统计
            total_sql = "SELECT COUNT(*) FROM platform_api_data"
            total_result = self.db.execute_query(total_sql)
            total_count = total_result[0][0] if total_result else 0
            
            # 按平台统计
            platform_sql = """
            SELECT platform_name, COUNT(*) 
            FROM platform_api_data 
            GROUP BY platform_name
            """
            platform_results = self.db.execute_query(platform_sql)
            
            # 按HTTP方法统计
            method_sql = """
            SELECT http_method, COUNT(*) 
            FROM platform_api_data 
            GROUP BY http_method
            """
            method_results = self.db.execute_query(method_sql)
            
            # 按分类统计
            category_sql = """
            SELECT api_category, COUNT(*) 
            FROM platform_api_data 
            GROUP BY api_category
            """
            category_results = self.db.execute_query(category_sql)
            
            print(f"📋 API总数: {total_count}")
            print()
            
            print("🏢 平台分布:")
            for platform, count in platform_results:
                print(f"  {platform}: {count}")
            print()
            
            print("📡 HTTP方法分布:")
            for method, count in method_results:
                print(f"  {method}: {count}")
            print()
            
            print("📂 分类分布:")
            for category, count in category_results:
                category_name = category or '未分类'
                print(f"  {category_name}: {count}")
            
        except Exception as e:
            print(f"❌ 统计失败: {str(e)}")


def main():
    """主函数"""
    query = PlatformApiDataQuery()
    
    if len(sys.argv) < 2:
        print("🔧 通用平台API数据查询工具")
        print("=" * 50)
        print("用法:")
        print("  python query_platform_api_data.py list [platform]           # 列出API")
        print("  python query_platform_api_data.py detail <id>               # 显示API详情")
        print("  python query_platform_api_data.py search <keyword> [platform] # 搜索API")
        print("  python query_platform_api_data.py platforms                 # 列出平台")
        print("  python query_platform_api_data.py categories [platform]     # 列出分类")
        print("  python query_platform_api_data.py export <id> <file>        # 导出API")
        print("  python query_platform_api_data.py stats                     # 显示统计")
        return
    
    command = sys.argv[1]
    
    if command == 'list':
        platform = sys.argv[2] if len(sys.argv) > 2 else None
        query.list_all_apis(platform)
    elif command == 'detail' and len(sys.argv) > 2:
        query.show_api_detail(int(sys.argv[2]))
    elif command == 'search' and len(sys.argv) > 2:
        keyword = sys.argv[2]
        platform = sys.argv[3] if len(sys.argv) > 3 else None
        query.search_apis(keyword, platform)
    elif command == 'platforms':
        query.list_platforms()
    elif command == 'categories':
        platform = sys.argv[2] if len(sys.argv) > 2 else None
        query.list_categories(platform)
    elif command == 'export' and len(sys.argv) > 3:
        query.export_api_json(int(sys.argv[2]), sys.argv[3])
    elif command == 'stats':
        query.get_statistics()
    else:
        print("❌ 无效的命令或参数")


if __name__ == '__main__':
    main()
