#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理路由
提供系统配置的增删改查和验证功能
"""

import logging
from datetime import datetime
from flask import Blueprint, request, jsonify
from services.config_service import (
    config_service, validate_github_config,
    validate_ozon_config, validate_database_config,
    ConfigEncryption
)
from models.database import get_db_manager
from services.github_service import github_service
from services.ozon_service import ozon_service

logger = logging.getLogger(__name__)

# 创建蓝图
config_manager_bp = Blueprint('config_manager', __name__)

# 模板路由已移除，前端使用React SPA

@config_manager_bp.route('/api/configs', methods=['GET'])
def api_get_configs():
    """获取所有配置API - 敏感配置从环境变量读取"""
    try:
        include_sensitive = request.args.get('include_sensitive', 'false').lower() == 'true'

        # 在开发环境下，强制不返回敏感配置的实际值
        configs = config_service.get_all_configs(False)  # 强制为False，不返回敏感值

        # 添加环境变量中的敏感配置状态
        import os
        from services.config_service import is_sensitive_config

        # 检查敏感配置是否已设置，但不返回实际值
        github_token_set = bool(os.environ.get('GITHUB_TOKEN'))
        ozon_api_key_set = bool(os.environ.get('OZON_API_KEY'))

        env_sensitive_configs = {
            'github_token': {
                'value': '✅ 已配置' if github_token_set else '❌ 未配置',
                'type': 'string',
                'description': 'GitHub Personal Access Token (从环境变量读取)',
                'is_sensitive': True,
                'source': 'environment',
                'configured': github_token_set,
                'can_test': github_token_set
            },
            'ozon_api_key': {
                'value': '✅ 已配置' if ozon_api_key_set else '❌ 未配置',
                'type': 'string',
                'description': 'OZON API Key (从环境变量读取)',
                'is_sensitive': True,
                'source': 'environment',
                'configured': ozon_api_key_set,
                'can_test': ozon_api_key_set
            }
        }

        # 合并配置，环境变量配置优先
        for key, config in env_sensitive_configs.items():
            configs[key] = config

        return jsonify({
            'success': True,
            'data': {
                'configs': configs,
                'total_count': len(configs)
            }
        })

    except Exception as e:
        logger.error(f"获取配置失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取配置失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/config/<config_key>', methods=['GET'])
def api_get_config(config_key):
    """获取单个配置API - 敏感配置不返回实际值"""
    try:
        from services.config_service import is_sensitive_config
        import os

        # 检查是否为敏感配置
        if is_sensitive_config(config_key):
            # 对于敏感配置，只返回是否已配置的状态
            if config_key == 'github_token':
                configured = bool(os.environ.get('GITHUB_TOKEN'))
                return jsonify({
                    'success': True,
                    'data': {
                        'key': config_key,
                        'value': '✅ 已配置' if configured else '❌ 未配置',
                        'is_sensitive': True,
                        'configured': configured,
                        'source': 'environment'
                    }
                })
            elif config_key == 'ozon_api_key':
                configured = bool(os.environ.get('OZON_API_KEY'))
                return jsonify({
                    'success': True,
                    'data': {
                        'key': config_key,
                        'value': '✅ 已配置' if configured else '❌ 未配置',
                        'is_sensitive': True,
                        'configured': configured,
                        'source': 'environment'
                    }
                })

        # 非敏感配置正常返回
        value = config_service.get_config(config_key)

        if value is not None:
            return jsonify({
                'success': True,
                'data': {
                    'key': config_key,
                    'value': value,
                    'is_sensitive': False
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '配置不存在'
            }), 404

    except Exception as e:
        logger.error(f"获取配置失败 {config_key}: {e}")
        return jsonify({
            'success': False,
            'error': f'获取配置失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/config', methods=['POST'])
def api_set_config():
    """设置配置API"""
    try:
        data = request.get_json()
        config_key = data.get('key')
        config_value = data.get('value')
        config_type = data.get('type', 'string')
        description = data.get('description', '')
        is_sensitive = data.get('is_sensitive', False)
        
        if not config_key:
            return jsonify({
                'success': False,
                'error': '配置键不能为空'
            }), 400

        # 检查是否为敏感配置
        from services.config_service import is_sensitive_config
        if is_sensitive_config(config_key):
            return jsonify({
                'success': False,
                'error': f'敏感配置 {config_key} 只能通过环境变量设置，请在.env文件中配置'
            }), 400

        success = config_service.set_config(
            config_key,
            config_value,
            config_type,
            description,
            is_sensitive
        )
        
        if success:
            # 如果是关键配置，重新加载服务配置
            if config_key.startswith('github_'):
                github_service.reload_config()
            elif config_key.startswith('ozon_'):
                ozon_service.reload_config()
            
            return jsonify({
                'success': True,
                'message': '配置设置成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '配置设置失败'
            }), 500
            
    except Exception as e:
        logger.error(f"设置配置失败: {e}")
        return jsonify({
            'success': False,
            'error': f'设置配置失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/config/<config_key>', methods=['DELETE'])
def api_delete_config(config_key):
    """删除配置API"""
    try:
        success = config_service.delete_config(config_key)
        
        if success:
            return jsonify({
                'success': True,
                'message': '配置删除成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '配置删除失败或配置不存在'
            }), 404
            
    except Exception as e:
        logger.error(f"删除配置失败 {config_key}: {e}")
        return jsonify({
            'success': False,
            'error': f'删除配置失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/test-github', methods=['POST'])
def api_test_github():
    """测试GitHub连接API - 优先从环境变量读取"""
    try:
        import os
        data = request.get_json()

        # 强制使用环境变量中的配置，忽略前端传递的敏感参数
        # 这样可以避免前端传递状态字符串导致的编码错误
        token = os.environ.get('GITHUB_TOKEN')
        repo = os.environ.get('GITHUB_REPO') or config_service.get_config('github_repo', '')
        branch = os.environ.get('GITHUB_BRANCH') or config_service.get_config('github_branch', 'main')
        
        logger.info(f"🧪 GitHub测试 - 使用环境变量配置")
        logger.info(f"   仓库: {repo}")
        logger.info(f"   分支: {branch}")
        logger.info(f"   Token: {'已设置' if token else '未设置'}")

        if not token:
            logger.warning("❌ GitHub Token未在环境变量中设置")
            return jsonify({
                'success': False,
                'error': 'GitHub Token未配置，请在.env文件中设置GITHUB_TOKEN'
            }), 400

        if not repo:
            logger.warning("❌ GitHub仓库未配置")
            return jsonify({
                'success': False,
                'error': 'GitHub仓库未配置，请在.env文件中设置GITHUB_REPO'
            }), 400
        
        # 测试连接
        is_valid = validate_github_config(token, repo, branch)
        
        if is_valid:
            return jsonify({
                'success': True,
                'message': 'GitHub连接测试成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': 'GitHub连接测试失败，请检查Token和仓库设置'
            }), 400
            
    except Exception as e:
        logger.error(f"GitHub连接测试失败: {e}")
        return jsonify({
            'success': False,
            'error': f'连接测试失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/test-ozon', methods=['POST'])
def api_test_ozon():
    """测试OZON API连接API"""
    try:
        logger.info("🧪 收到OZON API连接测试请求")
        data = request.get_json()

        # 强制使用环境变量中的配置，忽略前端传递的敏感参数
        # 这样可以避免前端传递状态字符串导致的编码错误
        import os
        client_id = os.environ.get('OZON_CLIENT_ID') or config_service.get_config('ozon_client_id', '')
        api_key = os.environ.get('OZON_API_KEY')

        logger.info(f"🧪 OZON测试 - 使用环境变量配置")
        logger.info(f"   Client ID: {client_id}")
        logger.info(f"   API Key: {'已设置' if api_key else '未设置'}")

        if not client_id:
            logger.warning("❌ OZON Client ID未配置")
            return jsonify({
                'success': False,
                'error': 'OZON Client ID未配置，请在.env文件中设置OZON_CLIENT_ID'
            }), 400

        if not api_key:
            logger.warning("❌ OZON API Key未在环境变量中设置")
            return jsonify({
                'success': False,
                'error': 'OZON API Key未配置，请在.env文件中设置OZON_API_KEY'
            }), 400

        # 测试连接
        logger.info("🔍 开始验证OZON API连接...")
        is_valid = validate_ozon_config(client_id, api_key)
        
        if is_valid:
            logger.info("✅ OZON API连接测试成功!")
            return jsonify({
                'success': True,
                'message': 'OZON API连接测试成功'
            })
        else:
            logger.warning("❌ OZON API连接测试失败")
            return jsonify({
                'success': False,
                'error': 'OZON API连接测试失败，请检查Client ID和API Key'
            }), 400
            
    except Exception as e:
        logger.error(f"OZON连接测试失败: {e}")
        return jsonify({
            'success': False,
            'error': f'连接测试失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/test-database', methods=['POST'])
def api_test_database():
    """测试数据库连接API"""
    try:
        is_valid = validate_database_config()
        
        if is_valid:
            return jsonify({
                'success': True,
                'message': '数据库连接测试成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '数据库连接测试失败'
            }), 400
            
    except Exception as e:
        logger.error(f"数据库连接测试失败: {e}")
        return jsonify({
            'success': False,
            'error': f'连接测试失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/system-status', methods=['GET'])
def api_get_system_status():
    """获取系统状态API"""
    try:
        # 测试各个组件的连接状态
        github_status = github_service.test_connection()
        ozon_status = ozon_service.test_connection()
        database_status = validate_database_config()
        
        # 获取关键配置状态
        github_token = config_service.get_config('github_token', '')
        github_repo = config_service.get_config('github_repo', '')
        ozon_client_id = config_service.get_config('ozon_client_id', '')
        ozon_api_key = config_service.get_config('ozon_api_key', '')
        local_folder = config_service.get_config('local_folder_path', '')
        
        status_info = {
            'github': {
                'connected': github_status,
                'configured': bool(github_token and github_repo),
                'token_set': bool(github_token),
                'repo_set': bool(github_repo)
            },
            'ozon': {
                'connected': ozon_status,
                'configured': bool(ozon_client_id and ozon_api_key),
                'client_id_set': bool(ozon_client_id),
                'api_key_set': bool(ozon_api_key)
            },
            'database': {
                'connected': database_status
            },
            'local_folder': {
                'configured': bool(local_folder),
                'path': local_folder
            }
        }
        
        # 计算整体状态
        overall_status = all([
            status_info['github']['connected'],
            status_info['ozon']['connected'],
            status_info['database']['connected']
        ])
        
        return jsonify({
            'success': True,
            'data': {
                'overall_status': overall_status,
                'components': status_info,
                'timestamp': str(datetime.now())
            }
        })
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取系统状态失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/reset-defaults', methods=['POST'])
def api_reset_defaults():
    """重置为默认配置API"""
    try:
        # 重新初始化默认配置
        from migrations.migrate import initialize_default_configs
        initialize_default_configs()
        
        # 重新加载服务配置
        github_service.reload_config()
        ozon_service.reload_config()
        
        return jsonify({
            'success': True,
            'message': '默认配置重置成功'
        })
        
    except Exception as e:
        logger.error(f"重置默认配置失败: {e}")
        return jsonify({
            'success': False,
            'error': f'重置默认配置失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/audit-logs', methods=['GET'])
def api_get_audit_logs():
    """获取配置审计日志API"""
    try:
        # 获取查询参数
        page = int(request.args.get('page', 1))
        limit = int(request.args.get('limit', 20))
        config_key = request.args.get('config_key', '')
        action = request.args.get('action', '')
        include_encrypted = request.args.get('include_encrypted', 'false').lower() == 'true'

        # 计算偏移量
        offset = (page - 1) * limit

        # 构建查询条件
        where_conditions = []
        params = []

        if config_key:
            where_conditions.append("config_key LIKE %s")
            params.append(f"%{config_key}%")

        if action:
            where_conditions.append("action = %s")
            params.append(action)

        where_clause = " WHERE " + " AND ".join(where_conditions) if where_conditions else ""

        # 查询总数
        count_query = f"SELECT COUNT(*) as total FROM config_audit_log{where_clause}"

        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        count_result = db_manager.execute_query(count_query, params)
        total = count_result[0]['total'] if count_result else 0

        # 查询数据
        data_query = f"""
        SELECT id, user_id, config_key, action, old_value, new_value,
               old_value_hash, new_value_hash, is_encrypted, timestamp
        FROM config_audit_log{where_clause}
        ORDER BY timestamp DESC
        LIMIT %s OFFSET %s
        """

        data_params = params + [limit, offset]
        results = db_manager.execute_query(data_query, data_params)

        # 处理结果
        logs = []
        for record in results:
            log_entry = {
                'id': record['id'],
                'user_id': record['user_id'],
                'config_key': record['config_key'],
                'action': record['action'],
                'timestamp': record['timestamp'].isoformat() if record['timestamp'] else None,
                'is_encrypted': bool(record['is_encrypted'])
            }

            # 处理敏感数据
            if record['is_encrypted'] and include_encrypted:
                # 管理员权限：解密显示
                try:
                    log_entry['old_value'] = ConfigEncryption.decrypt_for_audit(record['old_value']) if record['old_value'] else None
                    log_entry['new_value'] = ConfigEncryption.decrypt_for_audit(record['new_value']) if record['new_value'] else None
                    log_entry['old_value_hash'] = record['old_value_hash']
                    log_entry['new_value_hash'] = record['new_value_hash']
                except Exception as e:
                    logger.error(f"解密审计日志失败: {e}")
                    log_entry['old_value'] = "[解密失败]"
                    log_entry['new_value'] = "[解密失败]"
            elif record['is_encrypted']:
                # 普通权限：显示加密状态
                log_entry['old_value'] = "[已加密]" if record['old_value'] else None
                log_entry['new_value'] = "[已加密]" if record['new_value'] else None
                log_entry['old_value_hash'] = record['old_value_hash']
                log_entry['new_value_hash'] = record['new_value_hash']
            else:
                # 非敏感数据：直接显示
                log_entry['old_value'] = record['old_value']
                log_entry['new_value'] = record['new_value']
                log_entry['old_value_hash'] = record['old_value_hash']
                log_entry['new_value_hash'] = record['new_value_hash']

            logs.append(log_entry)

        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'pagination': {
                    'page': page,
                    'limit': limit,
                    'total': total,
                    'pages': (total + limit - 1) // limit
                }
            }
        })

    except Exception as e:
        logger.error(f"获取审计日志失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取审计日志失败: {str(e)}'
        }), 500

@config_manager_bp.route('/api/audit-logs/verify', methods=['POST'])
def api_verify_audit_log():
    """验证审计日志数据完整性API"""
    try:
        data = request.get_json()
        log_id = data.get('log_id')
        expected_value = data.get('expected_value')

        if not log_id or not expected_value:
            return jsonify({
                'success': False,
                'error': '缺少必要参数'
            }), 400

        # 查询审计日志
        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        query = """
        SELECT new_value, new_value_hash, is_encrypted
        FROM config_audit_log
        WHERE id = %s
        """

        results = db_manager.execute_query(query, (log_id,))
        if not results:
            return jsonify({
                'success': False,
                'error': '审计日志不存在'
            }), 404

        record = results[0]

        # 验证哈希
        expected_hash = ConfigEncryption.generate_hash(expected_value)
        actual_hash = record['new_value_hash']

        is_valid = expected_hash == actual_hash

        return jsonify({
            'success': True,
            'data': {
                'is_valid': is_valid,
                'expected_hash': expected_hash,
                'actual_hash': actual_hash,
                'is_encrypted': bool(record['is_encrypted'])
            }
        })

    except Exception as e:
        logger.error(f"验证审计日志失败: {e}")
        return jsonify({
            'success': False,
            'error': f'验证失败: {str(e)}'
        }), 500
