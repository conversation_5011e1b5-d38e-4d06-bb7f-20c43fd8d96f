"""
产品编辑API路由
"""

from flask import Blueprint, request, jsonify
from models.database import get_db_manager
import logging

logger = logging.getLogger(__name__)

product_editor_bp = Blueprint('product_editor', __name__)

@product_editor_bp.route('/test', methods=['GET'])
def test_route():
    """测试路由"""
    return jsonify({
        'success': True,
        'message': 'Product Editor API is working!',
        'timestamp': '2025-06-23'
    })

@product_editor_bp.route('/api/update-product/<int:product_id>', methods=['PUT'])
def api_update_product(product_id):
    """更新产品信息API"""
    try:
        logger.info(f"📝 收到产品更新请求: ID={product_id}")
        
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '没有提供更新数据'
            }), 400

        logger.info(f"📋 更新数据: {data}")

        # 验证产品是否存在
        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        # 检查产品是否存在
        check_query = "SELECT id FROM product_info WHERE id = %s"
        existing = db_manager.execute_query(check_query, (product_id,))
        
        if not existing:
            return jsonify({
                'success': False,
                'error': '产品不存在'
            }), 404

        # 构建更新查询 - 支持前端发送的所有47个字段
        allowed_fields = {
            # 基本信息 (8个)
            'item_code': 'item_code',
            'sku': 'sku',
            'model_name': 'model_name',
            'product_name': 'product_name',
            'brand': 'brand',
            'color': 'color',
            'type': 'type',
            'spare_type': 'spare_type',

            # 价格信息 (7个)
            'price_cny': 'price_cny',
            'price_before_discount': 'price_before_discount',
            'procurement_cost': 'procurement_cost',
            'packaging_cost': 'packaging_cost',
            'target_profit': 'target_profit',
            'promo_discount': 'promo_discount',
            'second_promo_discount': 'second_promo_discount',

            # 物理属性 (9个)
            'gross_weight': 'gross_weight',
            'weight': 'weight',
            'package_width': 'package_width',
            'package_height': 'package_height',
            'package_length': 'package_length',
            'size_mm': 'size_mm',
            'dimensions': 'dimensions',
            'qty_per_pack': 'qty_per_pack',
            'original_pack_qty': 'original_pack_qty',

            # 产品详情 (9个)
            'description': 'description',
            'keywords': 'keywords',
            'tags': 'tags',
            'material': 'material',
            'display_type': 'display_type',
            'body_frame': 'body_frame',
            'frame_color': 'frame_color',
            'country_of_origin': 'country_of_origin',
            'group_name': 'group_name',

            # 媒体资源 (6个)
            'main_image_url': 'main_image_url',
            'other_image_urls': 'other_image_urls',
            'image_360_url': 'image_360_url',
            'image_item_code': 'image_item_code',
            'video_url': 'video_url',
            'github_image_urls': 'github_image_urls',

            # 技术数据 (4个)
            'json_content': 'json_content',
            'config': 'config',
            'source_url': 'source_url',
            'source_file': 'source_file'
        }

        # 过滤有效字段
        update_fields = {}
        for field, db_field in allowed_fields.items():
            if field in data:
                update_fields[db_field] = data[field]

        if not update_fields:
            return jsonify({
                'success': False,
                'error': '没有有效的更新字段'
            }), 400

        # 构建SQL更新语句
        set_clauses = []
        params = []
        
        for db_field, value in update_fields.items():
            set_clauses.append(f"{db_field} = %s")
            params.append(value)

        # 添加更新时间
        set_clauses.append("updated_at = NOW()")
        params.append(product_id)

        update_query = f"""
        UPDATE product_info 
        SET {', '.join(set_clauses)}
        WHERE id = %s
        """

        # 执行更新
        result = db_manager.execute_query(update_query, params, fetch=False)

        if result:
            
            # 获取更新后的产品信息 - 返回所有字段
            select_query = """
            SELECT
                -- 基本信息
                id, item_code, sku, model_name, product_name, brand, color, type, spare_type,
                -- 价格信息
                price_cny, price_before_discount, procurement_cost, packaging_cost,
                target_profit, promo_discount, second_promo_discount,
                -- 物理属性
                gross_weight, weight, package_width, package_height, package_length,
                size_mm, dimensions, qty_per_pack, original_pack_qty,
                -- 产品详情
                description, keywords, tags, material, display_type, body_frame,
                frame_color, country_of_origin, group_name,
                -- 媒体资源
                main_image_url, other_image_urls, image_360_url, image_item_code,
                video_url, github_image_urls,
                -- 技术数据
                json_content, config, source_url, source_file,
                -- OZON相关
                ozon_upload_status, ozon_product_id, ozon_sku, ozon_name,
                ozon_price, ozon_status, ozon_visible,
                -- 系统字段
                sync_status, last_sync_time, image_upload_status,
                last_image_update, last_ozon_upload, created_at, updated_at
            FROM product_info
            WHERE id = %s
            """
            
            updated_product = db_manager.execute_query(select_query, (product_id,))
            
            return jsonify({
                'success': True,
                'data': {
                    'product': updated_product[0] if updated_product else None,
                    'updated_fields': list(update_fields.keys()),
                    'message': f'产品信息更新成功'
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': '更新失败'
            }), 500

    except Exception as e:
        logger.error(f"更新产品信息异常: {e}")
        return jsonify({
            'success': False,
            'error': f'更新失败: {str(e)}'
        }), 500

@product_editor_bp.route('/api/validate-product', methods=['POST'])
def api_validate_product():
    """验证产品数据API"""
    try:
        data = request.get_json()
        if not data:
            return jsonify({
                'success': False,
                'error': '没有提供验证数据'
            }), 400

        errors = []

        # 验证必填字段
        required_fields = {
            'product_name': '产品名称',
            'model_name': '型号名称',
            'item_code': '商品代码',
            'main_image_url': '主图URL',
            'price_cny': '价格'
        }

        for field, label in required_fields.items():
            if field in data:
                value = data[field]
                if not value or (isinstance(value, str) and value.strip() == ''):
                    errors.append({
                        'field': field,
                        'message': f'{label}不能为空'
                    })

        # 验证数字字段
        number_fields = {
            'price_cny': ('价格', 0.01, 999999),
            'procurement_cost': ('采购成本', 0, 999999),
            'gross_weight': ('重量', 1, 50000),
            'package_width': ('包装宽度', 1, 2000),
            'package_height': ('包装高度', 1, 2000),
            'package_length': ('包装长度', 1, 2000)
        }

        for field, (label, min_val, max_val) in number_fields.items():
            if field in data:
                value = data[field]
                if value is not None:
                    try:
                        num_value = float(value)
                        if num_value < min_val:
                            errors.append({
                                'field': field,
                                'message': f'{label}不能小于{min_val}'
                            })
                        elif num_value > max_val:
                            errors.append({
                                'field': field,
                                'message': f'{label}不能大于{max_val}'
                            })
                    except (ValueError, TypeError):
                        errors.append({
                            'field': field,
                            'message': f'{label}必须是有效数字'
                        })

        # 验证URL字段
        url_fields = ['main_image_url', 'video_url']
        for field in url_fields:
            if field in data and data[field]:
                value = data[field]
                if not value.startswith(('http://', 'https://')):
                    errors.append({
                        'field': field,
                        'message': f'{field}必须是有效的URL'
                    })

        # 验证字符串长度
        string_fields = {
            'product_name': ('产品名称', 1, 500),
            'model_name': ('型号名称', 1, 255),
            'item_code': ('商品代码', 1, 100)
        }

        for field, (label, min_len, max_len) in string_fields.items():
            if field in data and data[field]:
                value = str(data[field])
                if len(value) < min_len:
                    errors.append({
                        'field': field,
                        'message': f'{label}长度不能少于{min_len}个字符'
                    })
                elif len(value) > max_len:
                    errors.append({
                        'field': field,
                        'message': f'{label}长度不能超过{max_len}个字符'
                    })

        return jsonify({
            'success': True,
            'data': {
                'is_valid': len(errors) == 0,
                'errors': errors
            }
        })

    except Exception as e:
        logger.error(f"验证产品数据异常: {e}")
        return jsonify({
            'success': False,
            'error': f'验证失败: {str(e)}'
        }), 500

@product_editor_bp.route('/api/product/<int:product_id>', methods=['GET'])
def api_get_product(product_id):
    """获取单个产品详细信息API"""
    try:
        db_manager = get_db_manager()
        if not db_manager:
            return jsonify({
                'success': False,
                'error': '数据库连接失败'
            }), 500

        query = """
        SELECT
            -- 基本信息
            id, item_code, sku, model_name, product_name, brand, color, type, spare_type,
            -- 价格信息
            price_cny, price_before_discount, procurement_cost, packaging_cost,
            target_profit, promo_discount, second_promo_discount,
            -- 物理属性
            gross_weight, weight, package_width, package_height, package_length,
            size_mm, dimensions, qty_per_pack, original_pack_qty,
            -- 产品详情
            description, keywords, tags, material, display_type, body_frame,
            frame_color, country_of_origin, group_name,
            -- 媒体资源
            main_image_url, other_image_urls, image_360_url, image_item_code,
            video_url, github_image_urls,
            -- 技术数据
            json_content, config, source_url, source_file,
            -- OZON相关
            ozon_upload_status, ozon_product_id, ozon_sku, ozon_name,
            ozon_price, ozon_status, ozon_visible,
            -- 系统字段
            sync_status, last_sync_time, image_upload_status,
            last_image_update, last_ozon_upload, created_at, updated_at
        FROM product_info
        WHERE id = %s
        """
        
        result = db_manager.execute_query(query, (product_id,))
        
        if not result:
            return jsonify({
                'success': False,
                'error': '产品不存在'
            }), 404

        return jsonify({
            'success': True,
            'data': {
                'product': result[0]
            }
        })

    except Exception as e:
        logger.error(f"获取产品信息异常: {e}")
        return jsonify({
            'success': False,
            'error': f'获取产品信息失败: {str(e)}'
        }), 500
