#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flask应用数据库模型和操作
"""

import pymysql
import json
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging
from logging.handlers import RotatingFileHandler
from contextlib import contextmanager
import threading
import time
from queue import Queue, Empty

# 配置日志
def setup_database_logging():
    """配置数据库模块日志"""
    logger = logging.getLogger(__name__)

    # 避免重复添加handler
    if logger.handlers:
        return logger

    # 创建logs目录
    current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    logs_dir = os.path.join(current_dir, 'logs')
    if not os.path.exists(logs_dir):
        os.makedirs(logs_dir)

    # 文件handler
    file_handler = RotatingFileHandler(
        os.path.join(logs_dir, 'database.log'),
        maxBytes=5242880,  # 5MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s [%(name)s]: %(message)s'
    ))
    file_handler.setLevel(logging.INFO)

    # 控制台handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(
        '%(asctime)s %(levelname)s [DB]: %(message)s'
    ))
    console_handler.setLevel(logging.INFO)

    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    logger.setLevel(logging.INFO)

    return logger

logger = setup_database_logging()

class ConnectionPool:
    """简单的数据库连接池实现"""

    def __init__(self, config, max_connections=10, min_connections=2):
        self.config = config
        self.max_connections = max_connections
        self.min_connections = min_connections
        self.pool = Queue(maxsize=max_connections)
        self.active_connections = 0
        self.lock = threading.Lock()

        # 初始化最小连接数
        for _ in range(min_connections):
            try:
                conn = pymysql.connect(**config)
                self.pool.put(conn)
                self.active_connections += 1
            except Exception as e:
                logger.error(f"初始化连接池失败: {e}")

    def get_connection(self):
        """从连接池获取连接"""
        try:
            # 尝试从池中获取连接
            conn = self.pool.get(timeout=5)

            # 检查连接是否有效
            if not self._is_connection_alive(conn):
                conn = self._create_new_connection()

            return conn
        except Empty:
            # 池中没有可用连接，创建新连接
            with self.lock:
                if self.active_connections < self.max_connections:
                    return self._create_new_connection()
                else:
                    raise Exception("连接池已满，无法创建新连接")

    def return_connection(self, conn):
        """将连接返回到池中"""
        if self._is_connection_alive(conn):
            try:
                self.pool.put_nowait(conn)
            except:
                # 池已满，关闭连接
                conn.close()
                with self.lock:
                    self.active_connections -= 1
        else:
            conn.close()
            with self.lock:
                self.active_connections -= 1

    def _create_new_connection(self):
        """创建新的数据库连接"""
        conn = pymysql.connect(**self.config)
        with self.lock:
            self.active_connections += 1
        return conn

    def _is_connection_alive(self, conn):
        """检查连接是否有效"""
        try:
            conn.ping(reconnect=False)
            return True
        except:
            return False

    def close_all(self):
        """关闭所有连接"""
        while not self.pool.empty():
            try:
                conn = self.pool.get_nowait()
                conn.close()
            except Empty:
                break
        self.active_connections = 0

class DatabaseManager:
    """MySQL数据库管理器（Flask版本）"""

    def __init__(self, host='localhost', port=3306, user='root', password='root', database='model_database'):
        """
        初始化数据库连接配置

        Args:
            host: 数据库主机
            port: 数据库端口
            user: 用户名
            password: 密码
            database: 数据库名
        """
        self.config = {
            'host': host,
            'port': port,
            'user': user,
            'password': password,
            'database': database,
            'charset': 'utf8mb4',
            'autocommit': True,
            'connect_timeout': 30,
            'read_timeout': 30,
            'write_timeout': 30,
            'cursorclass': pymysql.cursors.DictCursor
        }

        # 初始化连接池
        self.pool = ConnectionPool(self.config, max_connections=20, min_connections=5)

        # 性能统计
        self.query_count = 0
        self.total_query_time = 0.0
        self.slow_query_threshold = 1.0  # 慢查询阈值（秒）

        logger.info("Flask数据库管理器初始化完成（带连接池）")

    @contextmanager
    def get_connection(self):
        """获取数据库连接（上下文管理器，使用连接池）"""
        connection = None
        try:
            connection = self.pool.get_connection()
            yield connection
        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            if connection:
                try:
                    connection.rollback()
                except:
                    pass
            raise
        finally:
            if connection:
                self.pool.return_connection(connection)
    
    def execute_query(self, query: str, params: tuple = None, fetch: bool = True) -> Optional[List[Dict]]:
        """
        执行SQL查询（带性能监控）

        Args:
            query: SQL查询语句
            params: 查询参数
            fetch: 是否返回结果

        Returns:
            查询结果列表
        """
        start_time = time.time()
        try:
            with self.get_connection() as connection:
                with connection.cursor() as cursor:
                    if params:
                        cursor.execute(query, params)
                    else:
                        cursor.execute(query)

                    if fetch:
                        result = cursor.fetchall()
                        execution_time = time.time() - start_time
                        self._log_query_performance(query, execution_time, len(result) if result else 0)
                        return result
                    else:
                        connection.commit()
                        execution_time = time.time() - start_time
                        self._log_query_performance(query, execution_time, cursor.rowcount)
                        return cursor.rowcount

        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"执行查询失败: {e} (耗时: {execution_time:.3f}s)")
            logger.error(f"查询语句: {query}")
            logger.error(f"参数: {params}")
            raise

    def _log_query_performance(self, query: str, execution_time: float, result_count: int):
        """记录查询性能"""
        self.query_count += 1
        self.total_query_time += execution_time

        # 记录慢查询
        if execution_time > self.slow_query_threshold:
            logger.warning(f"慢查询检测: {execution_time:.3f}s, 结果数: {result_count}")
            logger.warning(f"查询语句: {query[:200]}...")

        # 每100次查询输出一次统计
        if self.query_count % 100 == 0:
            avg_time = self.total_query_time / self.query_count
            logger.info(f"查询统计: 总计{self.query_count}次, 平均耗时{avg_time:.3f}s")

    def get_performance_stats(self) -> Dict:
        """获取性能统计信息"""
        avg_time = self.total_query_time / self.query_count if self.query_count > 0 else 0
        return {
            'total_queries': self.query_count,
            'total_time': self.total_query_time,
            'average_time': avg_time,
            'active_connections': self.pool.active_connections,
            'pool_size': self.pool.pool.qsize()
        }

# SQLite支持已移除，只保留MySQL

# 创建全局数据库管理器实例（延迟初始化）
db_manager = None

def get_db_manager():
    """获取数据库管理器实例（延迟初始化）"""
    global db_manager
    if db_manager is None:
        try:
            # 从配置文件获取数据库连接参数
            from config import Config
            db_manager = DatabaseManager(
                host=Config.DATABASE_HOST,
                port=Config.DATABASE_PORT,
                user=Config.DATABASE_USER,
                password=Config.DATABASE_PASSWORD,
                database=Config.DATABASE_NAME
            )
            logger.info("MySQL数据库管理器初始化成功")
        except Exception as e:
            logger.error(f"MySQL数据库管理器初始化失败: {e}")
            raise Exception(f"无法连接到MySQL数据库: {e}")
    return db_manager

# MockDatabaseManager已移除

def get_dashboard_data(page: int = 1, per_page: int = 10, has_links: bool = False, no_links: bool = False) -> List[Dict]:
    """
    获取仪表板数据（分页）

    Args:
        page: 页码（从1开始）
        per_page: 每页数量
        has_links: 是否只返回有商品链接的记录
        no_links: 是否只返回无商品链接的记录

    Returns:
        仪表板数据列表
    """
    try:
        offset = (page - 1) * per_page

        # 构建WHERE条件
        where_condition = ""
        if has_links:
            where_condition = "WHERE ks.links IS NOT NULL AND ks.links != '' AND ks.links != '[]'"
        elif no_links:
            where_condition = "WHERE ks.links IS NULL OR ks.links = '' OR ks.links = '[]'"

        query = f"""
        SELECT
            ks.keyword,
            ks.total_found,
            ks.matched_count,
            ks.answer as ai_analysis,
            ks.prices,
            ks.links,
            ks.suggested_price_cny,
            ks.suggested_price_rub,
            ks.created_at,
            ks.updated_at
        FROM keywords_search ks
        {where_condition}
        ORDER BY ks.updated_at DESC
        LIMIT %s OFFSET %s
        """
        
        results = get_db_manager().execute_query(query, (per_page, offset))
        
        # 处理每行数据，添加商品链接信息
        dashboard_data = []
        for row in results:
            # 解析价格和链接
            prices_list = row['prices'].split(';') if row['prices'] else []
            links_list = row['links'].split(';') if row['links'] else []
            
            # 创建商品数据
            products = []
            for i, (price, link) in enumerate(zip(prices_list, links_list)):
                if price.strip() and link.strip():
                    products.append({
                        'index': i + 1,
                        'price': price.strip(),
                        'link': link.strip(),
                        'product_name': f"商品{i + 1}"
                    })
            
            # 获取该关键词的定价参数
            pricing_params = get_product_info_pricing_params(row['keyword'])

            dashboard_data.append({
                'keyword': row['keyword'],
                'total_found': row['total_found'],
                'matched_count': row['matched_count'],
                'ai_analysis': row['ai_analysis'],
                'products': products,
                'suggested_price_cny': row['suggested_price_cny'],
                'suggested_price_rub': row['suggested_price_rub'],
                'pricing_params': pricing_params,  # 添加定价参数
                'created_at': row['created_at'],
                'updated_at': row['updated_at']
            })
        
        return dashboard_data

    except Exception as e:
        logger.error(f"获取仪表板数据失败: {e}")
        return []

def get_single_keyword_data(keyword: str) -> Optional[Dict]:
    """
    获取单个关键词的数据（优化版本）

    Args:
        keyword: 关键词

    Returns:
        关键词数据字典，如果未找到则返回None
    """
    try:
        query = """
        SELECT
            ks.keyword,
            ks.total_found,
            ks.matched_count,
            ks.answer as ai_analysis,
            ks.prices,
            ks.links,
            ks.suggested_price_cny,
            ks.suggested_price_rub,
            ks.created_at,
            ks.updated_at
        FROM keywords_search ks
        WHERE ks.keyword = %s
        """

        results = get_db_manager().execute_query(query, (keyword,))

        if not results:
            return None

        row = results[0]

        # 解析商品链接（支持两种格式：JSON和分号分隔）
        products = []
        if row['prices'] and row['links']:
            try:
                # 首先尝试JSON格式
                import json
                try:
                    prices = json.loads(row['prices']) if isinstance(row['prices'], str) else row['prices']
                    links = json.loads(row['links']) if isinstance(row['links'], str) else row['links']
                except (json.JSONDecodeError, TypeError):
                    # 如果JSON解析失败，使用分号分隔格式
                    prices = row['prices'].split(';') if row['prices'] else []
                    links = row['links'].split(';') if row['links'] else []

                for i, (price, link) in enumerate(zip(prices, links)):
                    if str(price).strip() and str(link).strip():
                        products.append({
                            'price': str(price).strip(),
                            'link': str(link).strip(),
                            'index': i + 1
                        })
            except Exception as e:
                logger.warning(f"解析商品数据失败: {e}")
                products = []

        # 获取该关键词的定价参数
        pricing_params = get_product_info_pricing_params(row['keyword'])

        return {
            'keyword': row['keyword'],
            'total_found': row['total_found'],
            'matched_count': row['matched_count'],
            'ai_analysis': row['ai_analysis'],
            'products': products,
            'suggested_price_cny': row['suggested_price_cny'],
            'suggested_price_rub': row['suggested_price_rub'],
            'pricing_params': pricing_params,
            'created_at': row['created_at'],
            'updated_at': row['updated_at']
        }

    except Exception as e:
        logger.error(f"获取单个关键词数据失败: {e}")
        return None

def get_total_count(has_links: bool = False, no_links: bool = False) -> int:
    """
    获取总记录数

    Args:
        has_links: 是否只统计有商品链接的记录
        no_links: 是否只统计无商品链接的记录

    Returns:
        总记录数
    """
    try:
        # 构建WHERE条件
        where_condition = ""
        if has_links:
            where_condition = "WHERE links IS NOT NULL AND links != '' AND links != '[]'"
        elif no_links:
            where_condition = "WHERE links IS NULL OR links = '' OR links = '[]'"

        query = f"SELECT COUNT(*) as count FROM keywords_search {where_condition}"
        result = get_db_manager().execute_query(query)
        return result[0]['count'] if result else 0
    except Exception as e:
        logger.error(f"获取总记录数失败: {e}")
        return 0

def update_suggested_price(keyword: str, price_cny: str = None, price_rub: str = None) -> bool:
    """
    更新建议价格到keywords_search表和product_info表

    Args:
        keyword: 关键词
        price_cny: 人民币价格
        price_rub: 卢布价格

    Returns:
        是否更新成功
    """
    try:
        success_count = 0

        # 1. 更新keywords_search表
        if price_cny or price_rub:
            updates = []
            params = []

            if price_cny:
                updates.append("suggested_price_cny = %s")
                params.append(price_cny)

            if price_rub:
                updates.append("suggested_price_rub = %s")
                params.append(price_rub)

            params.append(keyword)

            query = f"""
            UPDATE keywords_search
            SET {', '.join(updates)}, updated_at = CURRENT_TIMESTAMP
            WHERE keyword = %s
            """

            rows_affected = get_db_manager().execute_query(query, tuple(params), fetch=False)
            if rows_affected and rows_affected > 0:
                success_count += 1
                logger.info(f"成功更新keywords_search表中的价格: {keyword}")

        # 2. 更新product_info表
        if price_cny:
            # 处理关键词变体（与其他函数保持一致）
            keyword_variants = [
                keyword,  # 原始关键词
                keyword.replace(' lcd', '').replace(' screen', '').strip(),  # 去掉lcd/screen后缀
                keyword.replace(' ', '-'),  # 空格替换为连字符
            ]

            # 尝试多种匹配方式更新product_info表
            update_queries = []
            for variant in keyword_variants:
                if variant:  # 确保变体不为空
                    update_queries.extend([
                        ("UPDATE product_info SET price_cny = %s WHERE model_name = %s", (price_cny, variant)),
                        ("UPDATE product_info SET price_cny = %s WHERE item_code = %s", (price_cny, variant)),
                        ("UPDATE product_info SET price_cny = %s WHERE keywords LIKE %s", (price_cny, f'%{variant}%')),
                    ])

            for query, params in update_queries:
                try:
                    rows_affected = get_db_manager().execute_query(query, params, fetch=False)
                    if rows_affected and rows_affected > 0:
                        success_count += 1
                        logger.info(f"成功更新product_info表中的价格: {keyword} -> {price_cny}")
                        break  # 找到匹配的记录就停止
                except Exception as e:
                    logger.debug(f"product_info更新查询失败: {e}")
                    continue

        return success_count > 0

    except Exception as e:
        logger.error(f"更新建议价格失败: {e}")
        return False

def get_product_info_pricing_params(keyword: str) -> dict:
    """
    获取商品的价格计算参数

    Args:
        keyword: 关键词

    Returns:
        价格计算参数字典
    """
    try:
        # 尝试从product_info表获取所有定价参数
        # 处理关键词变体（去掉常见后缀如 lcd, screen等）
        keyword_variants = [
            keyword,  # 原始关键词
            keyword.replace(' lcd', '').replace(' screen', '').strip(),  # 去掉lcd/screen后缀
            keyword.replace(' ', '-'),  # 空格替换为连字符
        ]

        queries = []
        for variant in keyword_variants:
            if variant:  # 确保变体不为空
                queries.extend([
                    ("""SELECT procurement_cost, packaging_cost, weight, target_profit,
                               promo_discount, second_promo_discount
                        FROM product_info WHERE model_name = %s""", (variant,)),
                    ("""SELECT procurement_cost, packaging_cost, weight, target_profit,
                               promo_discount, second_promo_discount
                        FROM product_info WHERE item_code = %s""", (variant,)),
                    ("""SELECT procurement_cost, packaging_cost, weight, target_profit,
                               promo_discount, second_promo_discount
                        FROM product_info WHERE keywords LIKE %s""", (f'%{variant}%',)),
                ])

        for query, params in queries:
            try:
                results = get_db_manager().execute_query(query, params)
                if results and len(results) > 0:
                    row = results[0]
                    # 构建参数字典，使用数据库值或默认值
                    return {
                        'procurement_cost': float(row.get('procurement_cost', 46.0)) if row.get('procurement_cost') else 46.0,
                        'packaging_cost': float(row.get('packaging_cost', 10.0)) if row.get('packaging_cost') else 10.0,
                        'weight': int(row.get('weight', 180)) if row.get('weight') else 180,
                        'target_profit': float(row.get('target_profit', 100.0)) if row.get('target_profit') else 100.0,
                        'promo_discount': float(row.get('promo_discount', 15.0)) if row.get('promo_discount') else 15.0,
                        'second_promo_discount': float(row.get('second_promo_discount', 5.0)) if row.get('second_promo_discount') else 5.0
                    }
            except Exception as e:
                logger.debug(f"查询失败: {e}")
                continue

        # 返回默认值
        return {
            'procurement_cost': 46.0,
            'packaging_cost': 10.0,
            'weight': 180,
            'target_profit': 100.0,
            'promo_discount': 15.0,
            'second_promo_discount': 5.0
        }

    except Exception as e:
        logger.error(f"获取价格计算参数失败: {e}")
        return {
            'procurement_cost': 46.0,
            'packaging_cost': 10.0,
            'weight': 180,
            'target_profit': 100.0,
            'promo_discount': 15.0,
            'second_promo_discount': 5.0
        }

def update_product_info_parameter(keyword: str, field: str, value) -> bool:
    """
    更新product_info表中的定价参数

    Args:
        keyword: 关键词
        field: 字段名
        value: 字段值

    Returns:
        是否更新成功
    """
    try:
        # 验证字段名是否合法
        valid_fields = [
            'procurement_cost', 'packaging_cost', 'weight',
            'target_profit', 'promo_discount', 'second_promo_discount'
        ]

        if field not in valid_fields:
            logger.error(f"无效的字段名: {field}")
            return False

        # 处理关键词变体（与get_product_info_pricing_params保持一致）
        keyword_variants = [
            keyword,  # 原始关键词
            keyword.replace(' lcd', '').replace(' screen', '').strip(),  # 去掉lcd/screen后缀
            keyword.replace(' ', '-'),  # 空格替换为连字符
        ]

        # 尝试多种匹配方式更新
        update_queries = []
        for variant in keyword_variants:
            if variant:  # 确保变体不为空
                update_queries.extend([
                    (f"UPDATE product_info SET {field} = %s WHERE model_name = %s", (value, variant)),
                    (f"UPDATE product_info SET {field} = %s WHERE item_code = %s", (value, variant)),
                    (f"UPDATE product_info SET {field} = %s WHERE keywords LIKE %s", (value, f'%{variant}%')),
                ])

        for query, params in update_queries:
            try:
                rows_affected = get_db_manager().execute_query(query, params, fetch=False)
                if rows_affected and rows_affected > 0:
                    logger.info(f"成功更新 {keyword} 的 {field} = {value}")
                    return True
            except Exception as e:
                logger.debug(f"更新查询失败: {e}")
                continue

        logger.warning(f"未找到匹配的记录来更新: {keyword}")
        return False

    except Exception as e:
        logger.error(f"更新产品信息参数失败: {e}")
        return False

def batch_update_empty_links_with_template_params() -> dict:
    """
    批量更新商品链接为空的记录，使用Cubot KingKong ACE3的参数作为模板

    Returns:
        更新结果字典
    """
    try:
        # 1. 获取模板参数（Cubot KingKong ACE3）
        template_query = """
        SELECT price_cny, procurement_cost, packaging_cost, weight,
               target_profit, promo_discount, second_promo_discount
        FROM product_info
        WHERE model_name = 'Cubot KingKong ACE3'
        """

        template_result = get_db_manager().execute_query(template_query)
        if not template_result:
            return {
                'success': False,
                'error': '未找到模板记录 Cubot KingKong ACE3'
            }

        template = template_result[0]
        logger.info(f"获取到模板参数: {template}")

        # 2. 查找商品链接为空的记录
        empty_links_query = """
        SELECT keyword FROM keywords_search
        WHERE links IS NULL OR links = '' OR links = '[]'
        """

        empty_records = get_db_manager().execute_query(empty_links_query)
        if not empty_records:
            return {
                'success': True,
                'message': '没有找到需要更新的记录',
                'updated_count': 0
            }

        logger.info(f"找到 {len(empty_records)} 个需要更新的记录")

        # 3. 批量更新这些记录的参数
        updated_count = 0
        failed_count = 0

        for record in empty_records:
            keyword = record['keyword']

            # 为每个关键词更新所有参数
            success = update_keyword_all_params(keyword, template)
            if success:
                updated_count += 1
                logger.info(f"成功更新关键词: {keyword}")
            else:
                failed_count += 1
                logger.warning(f"更新失败: {keyword}")

        return {
            'success': True,
            'message': f'批量更新完成',
            'total_found': len(empty_records),
            'updated_count': updated_count,
            'failed_count': failed_count,
            'template_params': template
        }

    except Exception as e:
        logger.error(f"批量更新失败: {e}")
        return {
            'success': False,
            'error': str(e)
        }

def update_keyword_all_params(keyword: str, template_params: dict) -> bool:
    """
    更新单个关键词的所有参数

    Args:
        keyword: 关键词
        template_params: 模板参数字典

    Returns:
        是否更新成功
    """
    try:
        # 处理关键词变体
        keyword_variants = [
            keyword,
            keyword.replace(' lcd', '').replace(' screen', '').strip(),
            keyword.replace(' ', '-'),
        ]

        # 准备更新的字段和值
        update_fields = [
            ('price_cny', template_params.get('price_cny')),
            ('procurement_cost', template_params.get('procurement_cost')),
            ('packaging_cost', template_params.get('packaging_cost')),
            ('weight', template_params.get('weight')),
            ('target_profit', template_params.get('target_profit')),
            ('promo_discount', template_params.get('promo_discount')),
            ('second_promo_discount', template_params.get('second_promo_discount'))
        ]

        # 过滤掉None值
        valid_fields = [(field, value) for field, value in update_fields if value is not None]

        if not valid_fields:
            logger.warning(f"没有有效的参数可以更新: {keyword}")
            return False

        # 构建UPDATE语句
        set_clause = ', '.join([f"{field} = %s" for field, _ in valid_fields])
        values = [value for _, value in valid_fields]

        # 尝试多种匹配方式更新
        update_queries = []
        for variant in keyword_variants:
            if variant:
                update_queries.extend([
                    (f"UPDATE product_info SET {set_clause} WHERE model_name = %s", values + [variant]),
                    (f"UPDATE product_info SET {set_clause} WHERE item_code = %s", values + [variant]),
                    (f"UPDATE product_info SET {set_clause} WHERE keywords LIKE %s", values + [f'%{variant}%']),
                ])

        # 执行更新
        for query, params in update_queries:
            try:
                rows_affected = get_db_manager().execute_query(query, params, fetch=False)
                if rows_affected and rows_affected > 0:
                    logger.info(f"成功更新 {keyword} 的所有参数")
                    return True
            except Exception as e:
                logger.debug(f"更新查询失败: {e}")
                continue

        logger.warning(f"未找到匹配的记录来更新: {keyword}")
        return False

    except Exception as e:
        logger.error(f"更新关键词参数失败: {e}")
        return False
