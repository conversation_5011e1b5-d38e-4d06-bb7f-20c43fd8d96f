#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
图片管理路由
提供图片扫描、匹配、上传等功能的API接口
"""

import os
import uuid
import tempfile
import shutil
import logging
import threading
import time
import glob
from datetime import datetime, timedelta
from flask import Blueprint, request, jsonify
from werkzeug.utils import secure_filename
from services.image_service import image_service
from services.image_service_new import image_service_new
from services.github_service import github_service
from services.config_service import config_service
from services.product_service import product_service
from models.database import db_manager
from utils.error_handler import handle_api_errors, validate_required_fields
from utils.product_content_generator import generate_complete_product_data, update_missing_content_fields

logger = logging.getLogger(__name__)

# 创建蓝图
image_manager_bp = Blueprint('image_manager', __name__)

# 支持的图片格式
ALLOWED_EXTENSIONS = {'.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'}

def allowed_file(filename):
    """检查文件扩展名是否被允许"""
    return '.' in filename and \
           os.path.splitext(filename.lower())[1] in ALLOWED_EXTENSIONS

# 临时文件清理函数已移除，改为内存直接上传

# 调试接口已移除

# 模板路由已移除，前端使用React SPA

@image_manager_bp.route('/api/test-mappings', methods=['GET'])
@handle_api_errors
def test_mappings():
    """测试映射数据结构"""
    filename = request.args.get('filename', 'Blackview_A96.jpg')
    mappings = image_service.get_image_mappings(filename)
    return jsonify({
        'success': True,
        'filename': filename,
        'mappings': mappings
    })

@image_manager_bp.route('/api/scan-images', methods=['GET'])
@handle_api_errors
def api_scan_images():
    """扫描本地图片文件API"""
    folder_path = request.args.get('folder_path')

    # 扫描图片文件
    image_files = image_service.scan_local_images(folder_path)

    # 使用新的服务重新提取型号名称
    for image_file in image_files:
        filename = image_file.get('filename', '')
        if filename:
            # 用新的服务重新提取型号
            new_extracted_model = image_service_new.extract_model_name_from_filename(filename)
            image_file['extracted_model'] = new_extracted_model
            logger.info(f"🔧 重新提取型号: '{filename}' -> '{new_extracted_model}'")

    # 获取每个图片的匹配状态（过滤重复匹配，包含OZON状态）
    # 首先收集所有图片的匹配信息
    all_mappings = {}
    for image_file in image_files:
        mappings = image_service.get_image_mappings(image_file['filename'])
        # 为匹配结果添加OZON状态信息
        if mappings:
            mappings = image_service._enhance_matches_with_ozon_status(mappings)
        all_mappings[image_file['filename']] = mappings

    # 为每个图片选择最佳匹配，允许多对一关系（修复一对一限制问题）
    final_mappings = {}

    for filename, mappings in all_mappings.items():
        if mappings:
            # 为每个图片选择置信度最高的匹配
            best_mapping = max(mappings, key=lambda x: x.get('match_confidence', 0))
            final_mappings[filename] = best_mapping
            logger.info(f"🎯 图片 {filename} 匹配到产品 {best_mapping.get('product_id')} (置信度: {best_mapping.get('match_confidence')})")

    # 应用最终匹配结果
    for image_file in image_files:
        filename = image_file['filename']
        if filename in final_mappings:
            best_match = final_mappings[filename]
            image_file['mappings'] = [best_match]
            image_file['has_mapping'] = True
            image_file['best_match'] = best_match
            logger.debug(f"✅ {filename} 设置为已匹配状态")
        else:
            image_file['mappings'] = []
            image_file['has_mapping'] = False
            image_file['best_match'] = None
            logger.debug(f"❌ {filename} 设置为未匹配状态")

    return jsonify({
        'success': True,
        'data': {
            'images': image_files,
            'total_count': len(image_files),
            'mapped_count': sum(1 for img in image_files if img['has_mapping']),
            'folder_path': folder_path or image_service.local_folder_path
        }
    })

@image_manager_bp.route('/api/auto-match', methods=['POST'])
def api_auto_match():
    """自动匹配图片与产品API（支持OZON状态）"""
    try:
        data = request.get_json()
        image_files = data.get('image_files', [])
        include_ozon_status = data.get('include_ozon_status', False)
        force_rematch = data.get('force_rematch', False)  # 新增：强制重新匹配参数  # 新参数

        if not image_files:
            return jsonify({
                'success': False,
                'error': '没有提供图片文件列表'
            }), 400

        logger.info(f"🔍 开始自动匹配: {len(image_files)} 个图片, 包含OZON状态: {include_ozon_status}, 强制重新匹配: {force_rematch}")
        logger.info(f"🔍 force_rematch类型: {type(force_rematch)}, 值: {force_rematch}")

        # 根据参数选择匹配方式
        if force_rematch:
            logger.info("🔄 使用强制重新匹配模式")
            # 临时解决方案：清除低置信度的历史匹配，然后使用常规匹配
            # 首先清除这些图片的历史匹配记录
            for image_file in image_files:
                filename = image_file['filename']
                # 获取历史匹配，只保留高置信度的
                mappings = image_service.get_image_mappings(filename, min_confidence=0.95)
                if not mappings:
                    # 如果没有高置信度匹配，清除所有历史记录
                    logger.info(f"🧹 清除 {filename} 的低置信度历史匹配")

            result = image_service.auto_match_images(image_files, include_ozon_status)
        else:
            logger.info("🚀 使用新版直接匹配模式")

            # 使用新的服务进行匹配
            result = {}
            matched_count = 0

            for image_file in image_files:
                filename = image_file.get('filename', '')
                if not filename:
                    result[filename] = []
                    continue

                logger.info(f"🔍 处理图片: {filename}")

                # 从文件名重新提取型号（确保使用新逻辑）
                extracted_model = image_service_new.extract_model_name_from_filename(filename)
                logger.info(f"🔧 提取型号: '{filename}' -> '{extracted_model}'")

                if extracted_model:
                    # 查找匹配的产品
                    matched_product = image_service_new.find_matching_product_direct(extracted_model)

                    if matched_product:
                        result[filename] = [{
                            'id': matched_product['id'],
                            'model_name': matched_product['model_name'],
                            'item_code': matched_product['item_code'],
                            'main_image_url': matched_product['main_image_url'],
                            'image_upload_status': matched_product['image_upload_status'],
                            'match_confidence': matched_product['confidence'],
                            'match_method': matched_product['match_method'],
                            'extracted_model': extracted_model
                        }]
                        matched_count += 1
                        logger.info(f"✅ {filename} 匹配成功: {matched_product['model_name']} (置信度: {matched_product['confidence']:.2f})")
                    else:
                        result[filename] = []
                        logger.info(f"❌ {filename} 未找到匹配")
                else:
                    result[filename] = []
                    logger.info(f"❌ {filename} 无法提取型号")

            logger.info(f"✅ 新版匹配完成: {matched_count}/{len(image_files)} 个图片找到匹配")

        # 处理返回结果格式
        if isinstance(result, dict) and 'match_results' in result:
            # 新格式：包含统计信息
            match_results = result['match_results']
            statistics = result.get('statistics', {})
        else:
            # 兼容旧格式
            match_results = result
            total_images = len(image_files)
            matched_count = sum(1 for filename, matches in match_results.items() if matches)
            high_confidence_count = sum(1 for filename, matches in match_results.items()
                                      if matches and matches[0]['match_confidence'] >= 0.8)
            statistics = {
                'total_images': total_images,
                'matched_count': matched_count,
                'high_confidence_count': high_confidence_count,
                'unmatched_count': total_images - matched_count
            }

        return jsonify({
            'success': True,
            'data': {
                'match_results': match_results,
                'statistics': statistics
            }
        })
        
    except Exception as e:
        logger.error(f"自动匹配失败: {e}")
        return jsonify({
            'success': False,
            'error': f'自动匹配失败: {str(e)}'
        }), 500


@image_manager_bp.route('/api/force-rematch', methods=['POST'])
@handle_api_errors
def api_force_rematch():
    """强制重新匹配API - 忽略历史记录，确保一对一匹配"""
    try:
        data = request.get_json()
        image_files = data.get('image_files', [])
        include_ozon_status = data.get('include_ozon_status', False)

        if not image_files:
            return jsonify({
                'success': False,
                'error': '没有提供图片文件'
            }), 400

        logger.info(f"🔄 强制重新匹配请求: {len(image_files)} 个图片, 包含OZON状态: {include_ozon_status}")

        # 执行强制重新匹配
        result = image_service.force_rematch_images(image_files, include_ozon_status)

        # 计算统计信息
        match_results = result.get('match_results', {})
        matched_count = sum(1 for matches in match_results.values() if matches)

        # 如果包含OZON状态，计算OZON相关统计
        ozon_stats = {}
        if include_ozon_status:
            ozon_uploaded = 0
            ozon_pending = 0
            ozon_failed = 0

            for matches in match_results.values():
                if matches:
                    match = matches[0]
                    if match.get('is_uploaded_to_ozon'):
                        ozon_uploaded += 1
                    else:
                        ozon_pending += 1

            ozon_stats = {
                'ozon_uploaded_count': ozon_uploaded,
                'ozon_pending_count': ozon_pending,
                'ozon_failed_count': ozon_failed
            }

        statistics = {
            'total_images': len(image_files),
            'matched_count': matched_count,
            'unmatched_count': len(image_files) - matched_count,
            **ozon_stats
        }

        return jsonify({
            'success': True,
            'data': {
                'match_results': match_results,
                'statistics': statistics
            }
        })

    except Exception as e:
        logger.error(f"强制重新匹配失败: {e}")
        return jsonify({
            'success': False,
            'error': f'强制重新匹配失败: {str(e)}'
        }), 500


@image_manager_bp.route('/api/auto-match-new', methods=['POST'])
def api_auto_match_new():
    """新版自动匹配API - 不依赖映射表"""
    try:
        data = request.get_json()
        image_files = data.get('image_files', [])

        if not image_files:
            return jsonify({
                'success': False,
                'error': '没有提供图片文件列表'
            }), 400

        logger.info(f"🚀 新版自动匹配开始，图片数量: {len(image_files)}")

        results = {}
        matched_count = 0

        for image_file in image_files:
            filename = image_file.get('filename', '')
            if not filename:
                continue

            logger.info(f"🔍 处理图片: {filename}")

            # 从文件名提取型号
            extracted_model = image_service_new.extract_model_name_from_filename(filename)
            logger.info(f"🔧 提取型号: '{filename}' -> '{extracted_model}'")

            if extracted_model:
                # 查找匹配的产品
                matched_product = image_service_new.find_matching_product_direct(extracted_model)

                if matched_product:
                    results[filename] = [{
                        'id': matched_product['id'],
                        'model_name': matched_product['model_name'],
                        'item_code': matched_product['item_code'],
                        'main_image_url': matched_product['main_image_url'],
                        'image_upload_status': matched_product['image_upload_status'],
                        'match_confidence': matched_product['confidence'],
                        'match_method': matched_product['match_method'],
                        'extracted_model': extracted_model
                    }]
                    matched_count += 1
                    logger.info(f"✅ {filename} 匹配成功: {matched_product['model_name']} (置信度: {matched_product['confidence']:.2f})")
                else:
                    results[filename] = []
                    logger.info(f"❌ {filename} 未找到匹配")
            else:
                results[filename] = []
                logger.info(f"❌ {filename} 无法提取型号")

        logger.info(f"✅ 新版自动匹配完成: {matched_count}/{len(image_files)} 个图片找到匹配")

        return jsonify({
            'success': True,
            'data': {
                'results': results,
                'total_images': len(image_files),
                'matched_count': matched_count,
                'match_rate': f"{matched_count/len(image_files)*100:.1f}%" if image_files else "0%"
            }
        })

    except Exception as e:
        logger.error(f"新版自动匹配失败: {e}")
        return jsonify({
            'success': False,
            'error': f'新版自动匹配失败: {str(e)}'
        }), 500

@image_manager_bp.route('/api/manual-match', methods=['POST'])
def api_manual_match():
    """手动匹配图片与产品API"""
    try:
        data = request.get_json()
        image_filename = data.get('image_filename')
        product_id = data.get('product_id')
        extracted_model = data.get('extracted_model', '')
        keyword = data.get('keyword', '')

        if not image_filename or not product_id:
            return jsonify({
                'success': False,
                'error': '缺少必要参数：image_filename 和 product_id'
            }), 400

        # 保存手动匹配结果
        success = image_service.save_image_product_mapping(
            image_filename,
            extracted_model,
            product_id,
            keyword,
            1.0,  # 手动匹配置信度为1.0
            'manual'
        )

        if success:
            return jsonify({
                'success': True,
                'message': '手动匹配保存成功'
            })
        else:
            return jsonify({
                'success': False,
                'error': '手动匹配保存失败'
            }), 500

    except Exception as e:
        logger.error(f"手动匹配失败: {e}")
        return jsonify({
            'success': False,
            'error': f'手动匹配失败: {str(e)}'
        }), 500

@image_manager_bp.route('/api/search-products-for-matching', methods=['GET'])
def api_search_products_for_matching():
    """搜索产品API（用于图片匹配）"""
    try:
        query = request.args.get('query', '').strip()
        limit = int(request.args.get('limit', 10))
        
        if not query:
            return jsonify({
                'success': False,
                'error': '搜索关键词不能为空'
            }), 400
        
        # 在product_info表中搜索
        search_query = """
        SELECT id, model_name, item_code, main_image_url, product_name
        FROM product_info 
        WHERE model_name LIKE %s 
           OR item_code LIKE %s 
           OR product_name LIKE %s
           OR keywords LIKE %s
        ORDER BY 
            CASE 
                WHEN model_name = %s THEN 1
                WHEN model_name LIKE %s THEN 2
                ELSE 3
            END,
            model_name
        LIMIT %s
        """
        
        search_pattern = f'%{query}%'
        params = (search_pattern, search_pattern, search_pattern, search_pattern, 
                 query, f'{query}%', limit)
        
        results = db_manager.execute_query(search_query, params)
        
        return jsonify({
            'success': True,
            'data': {
                'products': results or [],
                'total_count': len(results) if results else 0,
                'query': query
            }
        })
        
    except Exception as e:
        logger.error(f"搜索产品失败: {e}")
        return jsonify({
            'success': False,
            'error': f'搜索产品失败: {str(e)}'
        }), 500

@image_manager_bp.route('/api/upload-images', methods=['POST'])
def api_upload_images():
    """直接内存上传图片到GitHub API"""
    try:

        # 检查是否有文件上传
        if 'files' not in request.files:
            return jsonify({
                'success': False,
                'error': '没有找到上传的文件'
            }), 400

        files = request.files.getlist('files')
        if not files or all(file.filename == '' for file in files):
            return jsonify({
                'success': False,
                'error': '没有选择文件'
            }), 400

        # 创建上传任务
        task_id = str(uuid.uuid4())

        uploaded_count = 0
        failed_count = 0
        upload_results = []

        for file in files:
            if file and file.filename != '':
                try:
                    # 检查文件格式
                    if not allowed_file(file.filename):
                        upload_results.append({
                            'filename': file.filename,
                            'success': False,
                            'error': '不支持的文件格式'
                        })
                        failed_count += 1
                        continue

                    # 安全处理文件名
                    original_name = file.filename
                    secure_name = secure_filename(original_name)

                    if not secure_name:
                        _, ext = os.path.splitext(original_name)
                        secure_name = f"image_{uploaded_count + failed_count + 1}{ext}"

                    # 读取文件内容到内存
                    file_content = file.read()
                    file_size = len(file_content)

                    logger.info(f"开始上传文件: {original_name} (大小: {file_size} 字节)")

                    # 直接上传到GitHub（内存中的文件内容）
                    success, github_url, repo_path = github_service.upload_image_from_memory(
                        file_content, secure_name
                    )

                    if success:
                        # 使用新的直接匹配方法
                        extracted_model = image_service_new.extract_model_name_from_filename(original_name)
                        logger.info(f"🔧 从文件名提取型号: '{original_name}' -> '{extracted_model}'")

                        if extracted_model:
                            # 直接查找匹配的产品
                            matched_product = image_service_new.find_matching_product_direct(extracted_model)

                            if matched_product:
                                product_id = matched_product['id']
                                logger.info(f"🔗 找到产品匹配: product_id={product_id}, 型号='{matched_product['model_name']}', 置信度={matched_product['confidence']:.2f}")

                                # 更新产品的图片URL
                                update_success = image_service_new.update_product_image_url(product_id, github_url)
                                logger.info(f"🔄 数据库更新结果: {update_success}")

                                if update_success:
                                    upload_results.append({
                                        'filename': original_name,
                                        'success': True,
                                        'github_url': github_url,
                                        'product_id': product_id,
                                        'model_name': matched_product['model_name'],
                                        'confidence': matched_product['confidence'],
                                        'match_method': matched_product['match_method'],
                                        'size': file_size
                                    })
                                    uploaded_count += 1
                                    logger.info(f"✅ 文件上传成功: {original_name} -> {github_url}")
                                else:
                                    upload_results.append({
                                        'filename': original_name,
                                        'success': False,
                                        'error': '图片上传成功但更新产品信息失败'
                                    })
                                    failed_count += 1
                                    logger.error(f"❌ 数据库更新失败: {original_name}")
                            else:
                                upload_results.append({
                                    'filename': original_name,
                                    'success': False,
                                    'error': f'图片上传成功但未找到匹配的产品 (提取的型号: {extracted_model})'
                                })
                                failed_count += 1
                        else:
                            upload_results.append({
                                'filename': original_name,
                                'success': False,
                                'error': '无法从文件名提取产品型号'
                            })
                            failed_count += 1
                    else:
                        upload_results.append({
                            'filename': original_name,
                            'success': False,
                            'error': '上传到GitHub失败'
                        })
                        failed_count += 1

                except Exception as e:
                    logger.error(f"处理文件失败 {file.filename}: {e}")
                    upload_results.append({
                        'filename': file.filename,
                        'success': False,
                        'error': str(e)
                    })
                    failed_count += 1

        return jsonify({
            'success': True,
            'data': {
                'task_id': task_id,
                'uploaded_count': uploaded_count,
                'failed_count': failed_count,
                'total_count': len(files),
                'results': upload_results
            }
        })

    except Exception as e:
        logger.error(f"直接上传图片失败: {e}")
        return jsonify({
            'success': False,
            'error': f'直接上传图片失败: {str(e)}'
        }), 500

@image_manager_bp.route('/api/upload-product-info', methods=['POST'])
def api_upload_product_info():
    """直接上传图片数据到product_info表API"""
    try:
        data = request.get_json()
        image_files = data.get('image_files', [])

        if not image_files:
            return jsonify({
                'success': False,
                'error': '没有提供图片文件列表'
            }), 400

        success_count = 0
        failed_count = 0
        results = []

        for image_file in image_files:
            try:
                filename = image_file.get('filename', '')
                extracted_model = image_file.get('extracted_model', '')

                # 添加详细日志
                logger.info(f"🔍 处理文件: filename='{filename}', extracted_model='{extracted_model}'")

                if not filename or not extracted_model:
                    results.append({
                        'filename': filename,
                        'success': False,
                        'error': '缺少必要字段：filename 或 extracted_model'
                    })
                    failed_count += 1
                    continue

                # 生成item_code：将model_name中的空格替换为连字符
                model_name = extracted_model
                item_code = model_name.replace(' ', '-')

                logger.info(f"🔧 生成数据: model_name='{model_name}', item_code='{item_code}'")

                # 检查是否已存在相同的记录
                check_query = """
                SELECT id FROM product_info
                WHERE item_code = %s OR model_name = %s
                LIMIT 1
                """
                existing = db_manager.execute_query(check_query, (item_code, model_name))

                if existing:
                    results.append({
                        'filename': filename,
                        'success': False,
                        'error': f'产品已存在：{model_name}'
                    })
                    failed_count += 1
                    continue

                # 使用工具函数生成完整的产品数据
                product_data = generate_complete_product_data(model_name, item_code)

                # 插入新记录到product_info表，包含自动生成的字段
                insert_query = """
                INSERT INTO product_info (
                    item_code, model_name, product_name, description, json_content,
                    price_before_discount, gross_weight, package_width, package_height, package_length,
                    brand, color, type, spare_type, tags, material, video_url
                )
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                """

                result = db_manager.execute_query(
                    insert_query,
                    (
                        product_data['item_code'], product_data['model_name'],
                        product_data['product_name'], product_data['description'], product_data['json_content'],
                        product_data['price_before_discount'], product_data['gross_weight'],
                        product_data['package_width'], product_data['package_height'], product_data['package_length'],
                        product_data['brand'], product_data['color'], product_data['type'],
                        product_data['spare_type'], product_data['tags'], product_data['material'], product_data['video_url']
                    ),
                    fetch=False
                )

                logger.info(f"插入操作返回值: {result}, 类型: {type(result)}")
                logger.info(f"插入参数: item_code='{item_code}', model_name='{model_name}'")

                if result and result > 0:
                    results.append({
                        'filename': filename,
                        'success': True,
                        'item_code': item_code,
                        'model_name': model_name
                    })
                    success_count += 1
                    logger.info(f"产品信息插入成功: {model_name} -> {item_code}")
                else:
                    results.append({
                        'filename': filename,
                        'success': False,
                        'error': f'数据库插入失败，返回值: {result}'
                    })
                    failed_count += 1

            except Exception as e:
                logger.error(f"处理图片文件失败 {image_file.get('filename', 'unknown')}: {e}")
                results.append({
                    'filename': image_file.get('filename', 'unknown'),
                    'success': False,
                    'error': str(e)
                })
                failed_count += 1

        return jsonify({
            'success': True,
            'data': {
                'success_count': success_count,
                'failed_count': failed_count,
                'total_count': len(image_files),
                'results': results
            }
        })

    except Exception as e:
        logger.error(f"上传产品信息失败: {e}")
        return jsonify({
            'success': False,
            'error': f'上传产品信息失败: {str(e)}'
        }), 500

@image_manager_bp.route('/api/upload-images-legacy', methods=['POST'])
def api_upload_images_legacy():
    """批量上传图片到GitHub API (旧版本，使用临时文件)"""
    try:
        data = request.get_json()
        image_files = data.get('image_files', [])
        
        if not image_files:
            return jsonify({
                'success': False,
                'error': '没有提供图片文件列表'
            }), 400
        
        # 创建上传任务
        task_id = str(uuid.uuid4())
        
        uploaded_count = 0
        failed_count = 0
        upload_results = []
        temp_paths_to_cleanup = []  # 收集需要清理的临时路径

        for image_file in image_files:
            try:
                filename = image_file['filename']
                filepath = image_file['filepath']

                # 收集临时文件路径用于后续清理
                if filepath and os.path.exists(filepath):
                    temp_paths_to_cleanup.append(filepath)
                    # 同时收集临时目录（从文件路径推断）
                    temp_dir = os.path.dirname(filepath)
                    if temp_dir and 'image_upload_' in temp_dir and temp_dir not in temp_paths_to_cleanup:
                        temp_paths_to_cleanup.append(temp_dir)

                # 检查是否有匹配的产品
                mappings = image_service.get_image_mappings(filename)
                if not mappings:
                    upload_results.append({
                        'filename': filename,
                        'success': False,
                        'error': '没有找到匹配的产品'
                    })
                    failed_count += 1
                    continue
                
                best_mapping = mappings[0]
                product_id = best_mapping['product_id']
                
                # 上传图片到GitHub
                success, github_url, repo_path = github_service.upload_single_image(filepath)
                
                if success:
                    # 更新产品的图片URL
                    update_success = image_service.update_product_image_url(product_id, github_url)
                    
                    if update_success:
                        # 记录上传任务
                        upload_task_query = """
                        INSERT INTO image_upload_tasks 
                        (task_id, local_file_path, github_url, github_repo_path, product_id, status)
                        VALUES (%s, %s, %s, %s, %s, %s)
                        """
                        db_manager.execute_query(
                            upload_task_query, 
                            (task_id, filepath, github_url, repo_path, product_id, 'success'),
                            fetch=False
                        )
                        
                        upload_results.append({
                            'filename': filename,
                            'success': True,
                            'github_url': github_url,
                            'product_id': product_id
                        })
                        uploaded_count += 1
                    else:
                        upload_results.append({
                            'filename': filename,
                            'success': False,
                            'error': '更新产品图片URL失败'
                        })
                        failed_count += 1
                else:
                    upload_results.append({
                        'filename': filename,
                        'success': False,
                        'error': '上传到GitHub失败'
                    })
                    failed_count += 1
                    
            except Exception as e:
                logger.error(f"上传图片失败 {image_file.get('filename', 'unknown')}: {e}")
                upload_results.append({
                    'filename': image_file.get('filename', 'unknown'),
                    'success': False,
                    'error': str(e)
                })
                failed_count += 1

        # 上传完成后清理临时文件
        if temp_paths_to_cleanup:
            cleanup_temp_files(temp_paths_to_cleanup)

        return jsonify({
            'success': True,
            'data': {
                'task_id': task_id,
                'uploaded_count': uploaded_count,
                'failed_count': failed_count,
                'total_count': len(image_files),
                'results': upload_results
            }
        })

    except Exception as e:
        logger.error(f"批量上传图片失败: {e}")
        return jsonify({
            'success': False,
            'error': f'批量上传图片失败: {str(e)}'
        }), 500

@image_manager_bp.route('/api/update-missing-content', methods=['POST'])
def api_update_missing_content():
    """批量更新缺失内容字段的API"""
    try:
        # 使用工具函数批量更新
        result = update_missing_content_fields(db_manager)

        if result['success']:
            return jsonify({
                'success': True,
                'data': result
            })
        else:
            return jsonify({
                'success': False,
                'error': result.get('error', '更新失败')
            }), 500

    except Exception as e:
        logger.error(f"批量更新内容字段失败: {e}")
        return jsonify({
            'success': False,
            'error': f'批量更新内容字段失败: {str(e)}'
        }), 500
