#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
OZON商品上传超时检测服务
"""

import json
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from models.database import get_db_manager
from services.config_service import ExtendedConfig

logger = logging.getLogger(__name__)

class UploadTimeoutDetector:
    """上传超时检测器"""
    
    def __init__(self):
        self.db = get_db_manager()
        self.config = ExtendedConfig()
        
        # 从配置中获取参数
        self.timeout_minutes = int(self.config.get_config('upload_timeout.default_timeout_minutes', 30))
        self.max_retry_count = int(self.config.get_config('upload_timeout.max_retry_count', 3))

        # 处理重试间隔配置
        retry_intervals_config = self.config.get_config('upload_timeout.retry_intervals', [5, 15, 30])
        if isinstance(retry_intervals_config, str):
            self.retry_intervals = json.loads(retry_intervals_config)
        else:
            self.retry_intervals = retry_intervals_config

        enable_auto_retry_config = self.config.get_config('upload_timeout.enable_auto_retry', True)
        if isinstance(enable_auto_retry_config, str):
            self.enable_auto_retry = enable_auto_retry_config.lower() == 'true'
        else:
            self.enable_auto_retry = bool(enable_auto_retry_config)
        
        logger.info(f"超时检测器初始化: timeout={self.timeout_minutes}分钟, max_retry={self.max_retry_count}, auto_retry={self.enable_auto_retry}")
    
    def detect_timeout_products(self) -> List[Dict]:
        """
        检测超时的商品
        
        Returns:
            超时商品列表
        """
        try:
            timeout_threshold = datetime.now() - timedelta(minutes=self.timeout_minutes)
            
            query = """
                SELECT id, model_name, product_name, ozon_upload_status, 
                       upload_started_at, upload_timeout_count, max_retry_count,
                       TIMESTAMPDIFF(MINUTE, upload_started_at, NOW()) as upload_duration_minutes
                FROM product_info 
                WHERE ozon_upload_status = 'uploading' 
                AND upload_started_at IS NOT NULL
                AND upload_started_at < %s
                AND (upload_timeout_count < max_retry_count OR max_retry_count = 0)
            """
            
            timeout_products = self.db.execute_query(query, (timeout_threshold,))
            
            logger.info(f"检测到 {len(timeout_products)} 个超时商品")
            
            return timeout_products or []
            
        except Exception as e:
            logger.error(f"检测超时商品失败: {e}")
            return []
    
    def handle_timeout_product(self, product_id: int) -> bool:
        """
        处理超时商品
        
        Args:
            product_id: 商品ID
            
        Returns:
            是否处理成功
        """
        try:
            # 获取商品信息
            product = self.db.execute_query(
                "SELECT * FROM product_info WHERE id = %s", 
                (product_id,)
            )
            
            if not product:
                logger.warning(f"商品 {product_id} 不存在")
                return False
            
            product_data = product[0]
            current_timeout_count = product_data.get('upload_timeout_count', 0)
            max_retry_count = product_data.get('max_retry_count', self.max_retry_count)
            
            # 记录超时日志
            self._log_timeout_event(product_id, current_timeout_count)
            
            # 更新超时次数和时间
            new_timeout_count = current_timeout_count + 1
            
            if new_timeout_count >= max_retry_count:
                # 达到最大重试次数，标记为失败
                self._mark_product_failed(product_id, new_timeout_count)
                logger.warning(f"商品 {product_id} 达到最大重试次数 {max_retry_count}，标记为失败")
            else:
                # 安排重试
                if self.enable_auto_retry:
                    self._schedule_retry(product_id, new_timeout_count)
                    logger.info(f"商品 {product_id} 安排重试，当前重试次数: {new_timeout_count}")
                else:
                    self._mark_product_timeout(product_id, new_timeout_count)
                    logger.info(f"商品 {product_id} 标记为超时，需要手动重试")
            
            return True
            
        except Exception as e:
            logger.error(f"处理超时商品 {product_id} 失败: {e}")
            return False
    
    def _log_timeout_event(self, product_id: int, retry_count: int):
        """记录超时事件"""
        try:
            self.db.execute_query("""
                INSERT INTO upload_logs (
                    product_id, upload_status, timeout_minutes, retry_count, created_at
                ) VALUES (%s, 'timeout', %s, %s, NOW())
            """, (product_id, self.timeout_minutes, retry_count))
            
        except Exception as e:
            logger.error(f"记录超时日志失败: {e}")
    
    def _mark_product_failed(self, product_id: int, timeout_count: int):
        """标记商品为失败状态"""
        try:
            self.db.execute_query("""
                UPDATE product_info 
                SET ozon_upload_status = 'failed',
                    upload_timeout_count = %s,
                    last_timeout_at = NOW(),
                    updated_at = NOW()
                WHERE id = %s
            """, (timeout_count, product_id))
            
            # 记录失败日志
            self.db.execute_query("""
                INSERT INTO upload_logs (
                    product_id, upload_status, error_message, retry_count, created_at
                ) VALUES (%s, 'failed', '达到最大重试次数', %s, NOW())
            """, (product_id, timeout_count))
            
        except Exception as e:
            logger.error(f"标记商品失败状态失败: {e}")
    
    def _mark_product_timeout(self, product_id: int, timeout_count: int):
        """标记商品为超时状态（等待手动重试）"""
        try:
            self.db.execute_query("""
                UPDATE product_info 
                SET ozon_upload_status = 'timeout',
                    upload_timeout_count = %s,
                    last_timeout_at = NOW(),
                    upload_started_at = NULL,
                    updated_at = NOW()
                WHERE id = %s
            """, (timeout_count, product_id))
            
        except Exception as e:
            logger.error(f"标记商品超时状态失败: {e}")
    
    def _schedule_retry(self, product_id: int, timeout_count: int):
        """安排重试"""
        try:
            # 计算下次重试时间
            retry_delay = self._get_retry_delay(timeout_count)
            next_retry_at = datetime.now() + timedelta(minutes=retry_delay)
            
            # 更新商品状态为重试中
            self.db.execute_query("""
                UPDATE product_info 
                SET ozon_upload_status = 'retrying',
                    upload_timeout_count = %s,
                    last_timeout_at = NOW(),
                    upload_started_at = NULL,
                    updated_at = NOW()
                WHERE id = %s
            """, (timeout_count, product_id))
            
            # 记录重试日志
            self.db.execute_query("""
                INSERT INTO upload_logs (
                    product_id, upload_status, retry_count, next_retry_at, created_at
                ) VALUES (%s, 'retry', %s, %s, NOW())
            """, (product_id, timeout_count, next_retry_at))
            
        except Exception as e:
            logger.error(f"安排重试失败: {e}")
    
    def _get_retry_delay(self, timeout_count: int) -> int:
        """
        获取重试延迟时间
        
        Args:
            timeout_count: 当前超时次数
            
        Returns:
            延迟分钟数
        """
        if timeout_count <= len(self.retry_intervals):
            return self.retry_intervals[timeout_count - 1]
        else:
            # 超出配置的重试间隔，使用最后一个间隔
            return self.retry_intervals[-1]
    
    def get_products_ready_for_retry(self) -> List[Dict]:
        """
        获取准备重试的商品
        
        Returns:
            准备重试的商品列表
        """
        try:
            query = """
                SELECT p.id, p.model_name, p.product_name, p.upload_timeout_count,
                       ul.next_retry_at
                FROM product_info p
                JOIN upload_logs ul ON p.id = ul.product_id
                WHERE p.ozon_upload_status = 'retrying'
                AND ul.upload_status = 'retry'
                AND ul.next_retry_at <= NOW()
                AND ul.id = (
                    SELECT MAX(id) FROM upload_logs 
                    WHERE product_id = p.id AND upload_status = 'retry'
                )
            """
            
            ready_products = self.db.execute_query(query)
            
            logger.info(f"找到 {len(ready_products or [])} 个准备重试的商品")
            
            return ready_products or []
            
        except Exception as e:
            logger.error(f"获取准备重试的商品失败: {e}")
            return []
    
    def reset_product_for_retry(self, product_id: int) -> bool:
        """
        重置商品状态以便重试
        
        Args:
            product_id: 商品ID
            
        Returns:
            是否重置成功
        """
        try:
            self.db.execute_query("""
                UPDATE product_info 
                SET ozon_upload_status = 'pending',
                    upload_started_at = NULL,
                    updated_at = NOW()
                WHERE id = %s
            """, (product_id,))
            
            logger.info(f"商品 {product_id} 已重置为待上传状态")
            return True
            
        except Exception as e:
            logger.error(f"重置商品状态失败: {e}")
            return False
    
    def get_timeout_statistics(self) -> Dict:
        """
        获取超时统计信息
        
        Returns:
            统计信息字典
        """
        try:
            # 获取各状态商品数量
            stats_query = """
                SELECT 
                    COUNT(*) as total_products,
                    COUNT(CASE WHEN ozon_upload_status = 'uploading' THEN 1 END) as uploading_count,
                    COUNT(CASE WHEN ozon_upload_status = 'timeout' THEN 1 END) as timeout_count,
                    COUNT(CASE WHEN ozon_upload_status = 'retrying' THEN 1 END) as retrying_count,
                    COUNT(CASE WHEN ozon_upload_status = 'failed' AND upload_timeout_count > 0 THEN 1 END) as failed_by_timeout_count,
                    AVG(upload_timeout_count) as avg_timeout_count
                FROM product_info
                WHERE upload_timeout_count > 0 OR ozon_upload_status IN ('uploading', 'timeout', 'retrying')
            """
            
            stats = self.db.execute_query(stats_query)
            
            if stats:
                return {
                    'total_products': stats[0]['total_products'] or 0,
                    'uploading_count': stats[0]['uploading_count'] or 0,
                    'timeout_count': stats[0]['timeout_count'] or 0,
                    'retrying_count': stats[0]['retrying_count'] or 0,
                    'failed_by_timeout_count': stats[0]['failed_by_timeout_count'] or 0,
                    'avg_timeout_count': float(stats[0]['avg_timeout_count'] or 0),
                    'last_check_time': datetime.now().isoformat()
                }
            else:
                return {
                    'total_products': 0,
                    'uploading_count': 0,
                    'timeout_count': 0,
                    'retrying_count': 0,
                    'failed_by_timeout_count': 0,
                    'avg_timeout_count': 0.0,
                    'last_check_time': datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"获取超时统计失败: {e}")
            return {}
    
    def cleanup_old_logs(self, days: int = None) -> int:
        """
        清理旧的上传日志
        
        Args:
            days: 保留天数，默认从配置获取
            
        Returns:
            清理的记录数
        """
        try:
            if days is None:
                days = int(self.config.get_config('upload_timeout.cleanup_after_days', '7'))
            
            cleanup_date = datetime.now() - timedelta(days=days)
            
            result = self.db.execute_query("""
                DELETE FROM upload_logs 
                WHERE created_at < %s
            """, (cleanup_date,))
            
            # 获取删除的行数（这个方法可能因数据库而异）
            deleted_count = self.db.get_affected_rows() if hasattr(self.db, 'get_affected_rows') else 0
            
            logger.info(f"清理了 {deleted_count} 条 {days} 天前的上传日志")
            
            return deleted_count
            
        except Exception as e:
            logger.error(f"清理旧日志失败: {e}")
            return 0
