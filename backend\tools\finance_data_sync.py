#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON财务数据同步工具 - 可直接运行或作为MySQL工具调用
"""

import sys
import os
import json
import logging
from datetime import datetime, timedelta
from decimal import Decimal

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import get_db_manager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def json_serializer(obj):
    """JSON序列化处理函数，处理datetime和Decimal对象"""
    if isinstance(obj, datetime):
        return obj.isoformat()
    elif isinstance(obj, Decimal):
        return float(obj)
    raise TypeError(f"Object of type {obj.__class__.__name__} is not JSON serializable")

def sync_ozon_finance_data(days_back: int = 30) -> dict:
    """
    同步OZON财务数据到数据库

    Args:
        days_back: 向前同步多少天的数据，默认30天（近一个月）

    Returns:
        dict: 同步结果
    """
    logger.info(f"🔄 开始同步OZON财务数据，回溯 {days_back} 天")
    
    try:
        # 导入财务服务
        from services.ozon_finance_service import OzonFinanceService
        
        finance_service = OzonFinanceService()
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days_back)
        
        date_from = start_date.strftime('%Y-%m-%d')
        date_to = end_date.strftime('%Y-%m-%d')
        
        logger.info(f"📅 同步日期范围: {date_from} 到 {date_to}")
        
        # 获取交易数据
        transactions = finance_service.fetch_finance_transactions(date_from, date_to)
        
        if not transactions:
            result = {
                'success': True,
                'message': '没有新的财务交易数据',
                'sync_count': 0,
                'error_count': 0,
                'date_range': f'{date_from} 到 {date_to}'
            }
            logger.info(f"ℹ️ {result['message']}")
            return result
        
        # 保存到数据库
        success_count, error_count = finance_service.save_transactions_to_db(transactions)
        
        result = {
            'success': True,
            'message': f'财务数据同步完成',
            'sync_count': success_count,
            'error_count': error_count,
            'total_fetched': len(transactions),
            'date_range': f'{date_from} 到 {date_to}',
            'timestamp': datetime.now().isoformat()
        }
        
        logger.info(f"🎉 财务数据同步完成: 成功 {success_count} 条, 失败 {error_count} 条")
        return result
        
    except Exception as e:
        error_msg = f"同步财务数据失败: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            'success': False,
            'error': error_msg,
            'timestamp': datetime.now().isoformat()
        }

def get_finance_data_summary() -> dict:
    """
    获取财务数据摘要统计
    
    Returns:
        dict: 统计信息
    """
    logger.info("📊 获取财务数据摘要统计")
    
    try:
        db_manager = get_db_manager()
        
        # 基本统计
        basic_stats_query = """
        SELECT 
            COUNT(*) as total_transactions,
            MIN(operation_date) as earliest_date,
            MAX(operation_date) as latest_date,
            SUM(CASE WHEN amount > 0 THEN amount ELSE 0 END) as total_income,
            SUM(CASE WHEN amount < 0 THEN amount ELSE 0 END) as total_expense,
            SUM(amount) as net_amount,
            COUNT(DISTINCT transaction_category) as category_count
        FROM ozon_finance_transactions
        """
        
        basic_stats = db_manager.execute_query(basic_stats_query)
        
        # 按类别统计
        category_stats_query = """
        SELECT 
            transaction_category,
            COUNT(*) as transaction_count,
            SUM(amount) as total_amount,
            AVG(amount) as avg_amount,
            MIN(operation_date) as first_transaction,
            MAX(operation_date) as last_transaction
        FROM ozon_finance_transactions
        GROUP BY transaction_category
        ORDER BY total_amount DESC
        """
        
        category_stats = db_manager.execute_query(category_stats_query)
        
        # 最近7天统计
        recent_stats_query = """
        SELECT 
            DATE(operation_date) as transaction_date,
            COUNT(*) as daily_count,
            SUM(amount) as daily_amount
        FROM ozon_finance_transactions
        WHERE operation_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
        GROUP BY DATE(operation_date)
        ORDER BY transaction_date DESC
        """
        
        recent_stats = db_manager.execute_query(recent_stats_query)
        
        result = {
            'success': True,
            'data': {
                'basic_statistics': basic_stats[0] if basic_stats else {},
                'category_statistics': category_stats,
                'recent_daily_statistics': recent_stats,
                'generated_at': datetime.now().isoformat()
            }
        }
        
        logger.info("✅ 财务数据摘要统计获取完成")
        return result
        
    except Exception as e:
        error_msg = f"获取财务数据统计失败: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            'success': False,
            'error': error_msg
        }

def cleanup_old_finance_data(days_to_keep: int = 90) -> dict:
    """
    清理旧的财务数据
    
    Args:
        days_to_keep: 保留多少天的数据，默认90天
    
    Returns:
        dict: 清理结果
    """
    logger.info(f"🧹 开始清理 {days_to_keep} 天前的财务数据")
    
    try:
        db_manager = get_db_manager()
        
        # 计算截止日期
        cutoff_date = datetime.now() - timedelta(days=days_to_keep)
        cutoff_str = cutoff_date.strftime('%Y-%m-%d')
        
        # 查询要删除的记录数
        count_query = """
        SELECT COUNT(*) as count_to_delete
        FROM ozon_finance_transactions
        WHERE operation_date < %s
        """
        
        count_result = db_manager.execute_query(count_query, (cutoff_str,))
        count_to_delete = count_result[0]['count_to_delete'] if count_result else 0
        
        if count_to_delete == 0:
            result = {
                'success': True,
                'message': '没有需要清理的旧数据',
                'deleted_count': 0,
                'cutoff_date': cutoff_str
            }
            logger.info(f"ℹ️ {result['message']}")
            return result
        
        # 执行删除
        delete_query = """
        DELETE FROM ozon_finance_transactions
        WHERE operation_date < %s
        """
        
        delete_result = db_manager.execute_query(delete_query, (cutoff_str,), fetch=False)
        
        if delete_result:
            result = {
                'success': True,
                'message': f'成功清理旧财务数据',
                'deleted_count': count_to_delete,
                'cutoff_date': cutoff_str,
                'timestamp': datetime.now().isoformat()
            }
            logger.info(f"✅ 清理完成: 删除 {count_to_delete} 条记录")
        else:
            result = {
                'success': False,
                'error': '删除操作失败',
                'cutoff_date': cutoff_str
            }
            logger.error("❌ 删除操作失败")
        
        return result
        
    except Exception as e:
        error_msg = f"清理财务数据失败: {str(e)}"
        logger.error(f"❌ {error_msg}")
        return {
            'success': False,
            'error': error_msg
        }

def main():
    """主函数 - 命令行工具入口"""
    import argparse
    
    parser = argparse.ArgumentParser(description='OZON财务数据同步工具')
    parser.add_argument('--action', choices=['sync', 'summary', 'cleanup'], 
                       default='sync', help='执行的操作')
    parser.add_argument('--days', type=int, default=30,
                       help='同步或清理的天数（默认30天，近一个月）')
    
    args = parser.parse_args()
    
    print("🔧 OZON财务数据同步工具")
    print("=" * 50)
    
    if args.action == 'sync':
        print(f"🔄 同步最近 {args.days} 天的财务数据...")
        result = sync_ozon_finance_data(args.days)
        
    elif args.action == 'summary':
        print("📊 获取财务数据摘要统计...")
        result = get_finance_data_summary()
        
    elif args.action == 'cleanup':
        print(f"🧹 清理 {args.days} 天前的财务数据...")
        result = cleanup_old_finance_data(args.days)
    
    print("\n📄 执行结果:")
    print(json.dumps(result, ensure_ascii=False, indent=2, default=json_serializer))
    
    if result.get('success'):
        print("\n✅ 操作完成")
    else:
        print("\n❌ 操作失败")
        sys.exit(1)

if __name__ == '__main__':
    main()
