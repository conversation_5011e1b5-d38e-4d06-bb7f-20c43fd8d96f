#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OZON商品同步API路由
提供商品验证和信息同步的API接口
"""

import logging
import sys
import os
import json
import requests
import pymysql
import pymysql.cursors
from datetime import datetime
from flask import Blueprint, request, jsonify
from services.ozon_sync_service import ozon_sync_service
from models.database import db_manager

# 添加spider目录到路径，以便导入OzonPriceSyncAPI
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'spider'))

# 创建蓝图
ozon_sync_bp = Blueprint('ozon_sync', __name__)

# 配置日志
logger = logging.getLogger('routes.ozon_sync')

# 模板路由已移除，前端使用React SPA

@ozon_sync_bp.route('/api/full-sync', methods=['POST'])
def api_full_sync():
    """完整的商品同步API"""
    try:
        logger.info("🚀 收到完整同步请求")
        
        data = request.get_json() or {}
        target_offer_id = data.get('target_offer_id')  # 可选，只同步特定商品
        
        if target_offer_id:
            logger.info(f"📋 目标商品: {target_offer_id}")
        else:
            logger.info("📋 执行全量同步")
        
        # 执行同步
        result = ozon_sync_service.full_sync_products(target_offer_id)
        
        if result['success']:
            logger.info(f"✅ 同步完成: 总数={result['total_products']}, 成功={result['synced_products']}, 失败={result['failed_products']}")
            return jsonify({
                'success': True,
                'message': '商品同步完成',
                'data': result
            })
        else:
            logger.error(f"❌ 同步失败: {result.get('error')}")
            return jsonify({
                'success': False,
                'error': result.get('error', '同步失败'),
                'data': result
            }), 500
            
    except Exception as e:
        logger.error(f"完整同步API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'同步异常: {str(e)}'
        }), 500

@ozon_sync_bp.route('/api/validate-product', methods=['POST'])
def api_validate_product():
    """验证特定商品API"""
    try:
        data = request.get_json()
        item_code = data.get('item_code')
        
        if not item_code:
            return jsonify({
                'success': False,
                'error': '缺少商品代码'
            }), 400
        
        logger.info(f"🔍 收到商品验证请求: {item_code}")
        
        # 执行验证
        result = ozon_sync_service.validate_specific_product(item_code)
        
        if result['success']:
            logger.info(f"✅ 商品验证完成: {item_code}")
            return jsonify({
                'success': True,
                'message': result['message'],
                'data': result
            })
        else:
            logger.warning(f"⚠️ 商品验证失败: {item_code}")
            return jsonify({
                'success': False,
                'error': result.get('error', '验证失败'),
                'data': result
            }), 404
            
    except Exception as e:
        logger.error(f"商品验证API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'验证异常: {str(e)}'
        }), 500

@ozon_sync_bp.route('/api/sync-report', methods=['GET'])
def api_sync_report():
    """获取同步状态报告API"""
    try:
        logger.info("📊 收到同步报告请求")
        
        # 获取报告
        report = ozon_sync_service.get_sync_report()
        
        if report['success']:
            logger.info("✅ 同步报告生成成功")
            return jsonify({
                'success': True,
                'data': report
            })
        else:
            logger.error(f"❌ 同步报告生成失败: {report.get('error')}")
            return jsonify({
                'success': False,
                'error': report.get('error', '报告生成失败')
            }), 500
            
    except Exception as e:
        logger.error(f"同步报告API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'报告生成异常: {str(e)}'
        }), 500

@ozon_sync_bp.route('/api/sync-logs', methods=['GET'])
def api_sync_logs():
    """获取同步日志API"""
    try:
        limit = int(request.args.get('limit', 20))
        
        query = """
        SELECT * FROM ozon_sync_logs 
        ORDER BY created_at DESC 
        LIMIT %s
        """
        
        logs = db_manager.execute_query(query, (limit,))
        
        return jsonify({
            'success': True,
            'data': {
                'logs': logs,
                'total_count': len(logs)
            }
        })
        
    except Exception as e:
        logger.error(f"获取同步日志失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取日志失败: {str(e)}'
        }), 500

@ozon_sync_bp.route('/api/product-status', methods=['GET'])
def api_product_status():
    """获取商品同步状态API"""
    try:
        item_code = request.args.get('item_code')
        status = request.args.get('status')  # pending, synced, failed, not_found
        limit = int(request.args.get('limit', 50))
        offset = int(request.args.get('offset', 0))
        
        # 构建查询条件
        conditions = ["item_code IS NOT NULL"]
        params = []
        
        if item_code:
            conditions.append("item_code LIKE %s")
            params.append(f"%{item_code}%")
        
        if status:
            conditions.append("sync_status = %s")
            params.append(status)
        
        where_clause = " AND ".join(conditions)
        
        # 查询商品
        query = f"""
        SELECT item_code, model_name, ozon_product_id, ozon_sku, ozon_name,
               ozon_status, ozon_visible, sync_status, last_sync_time,
               sync_error_message
        FROM product_info 
        WHERE {where_clause}
        ORDER BY last_sync_time DESC, item_code
        LIMIT %s OFFSET %s
        """
        
        params.extend([limit, offset])
        products = db_manager.execute_query(query, tuple(params))
        
        # 获取总数
        count_query = f"""
        SELECT COUNT(*) as total 
        FROM product_info 
        WHERE {where_clause}
        """
        
        count_result = db_manager.execute_query(count_query, tuple(params[:-2]))
        total_count = count_result[0]['total'] if count_result else 0
        
        return jsonify({
            'success': True,
            'data': {
                'products': products,
                'total_count': total_count,
                'limit': limit,
                'offset': offset
            }
        })
        
    except Exception as e:
        logger.error(f"获取商品状态失败: {e}")
        return jsonify({
            'success': False,
            'error': f'获取商品状态失败: {str(e)}'
        }), 500

@ozon_sync_bp.route('/api/reset-sync-status', methods=['POST'])
def api_reset_sync_status():
    """重置商品同步状态API"""
    try:
        data = request.get_json()
        item_codes = data.get('item_codes', [])
        
        if not item_codes:
            return jsonify({
                'success': False,
                'error': '缺少商品代码列表'
            }), 400
        
        logger.info(f"🔄 重置 {len(item_codes)} 个商品的同步状态")
        
        # 重置状态
        placeholders = ','.join(['%s'] * len(item_codes))
        query = f"""
        UPDATE product_info 
        SET sync_status = 'pending', 
            ozon_product_id = NULL,
            ozon_sku = NULL,
            ozon_name = NULL,
            ozon_price = NULL,
            ozon_status = NULL,
            ozon_visible = NULL,
            last_sync_time = NULL,
            sync_error_message = NULL
        WHERE item_code IN ({placeholders})
        """
        
        result = db_manager.execute_query(query, tuple(item_codes), fetch=False)
        
        if result:
            logger.info(f"✅ 成功重置 {len(item_codes)} 个商品的同步状态")
            return jsonify({
                'success': True,
                'message': f'成功重置 {len(item_codes)} 个商品的同步状态'
            })
        else:
            logger.error("❌ 重置同步状态失败")
            return jsonify({
                'success': False,
                'error': '重置同步状态失败'
            }), 500
            
    except Exception as e:
        logger.error(f"重置同步状态API异常: {e}")
        return jsonify({
            'success': False,
            'error': f'重置状态异常: {str(e)}'
        }), 500


# ==================== OZON价格同步功能 ====================

class OzonPriceSyncAPI:
    """OZON价格同步API类"""

    def __init__(self):
        """初始化OZON价格同步API"""
        self.api_config = None
        self.load_api_config()

    def get_db_connection(self):
        """获取数据库连接"""
        from dotenv import load_dotenv

        # 尝试多个.env文件路径
        env_paths = ['.env', '../.env', '../../.env']
        for env_path in env_paths:
            if os.path.exists(env_path):
                load_dotenv(env_path)
                break

        try:
            connection = pymysql.connect(
                host=os.getenv('DATABASE_HOST', 'localhost'),
                port=int(os.getenv('DATABASE_PORT', 3306)),
                user=os.getenv('DATABASE_USER', 'root'),
                password=os.getenv('DATABASE_PASSWORD', ''),
                database=os.getenv('DATABASE_NAME', 'model_database'),
                charset='utf8mb4',
                cursorclass=pymysql.cursors.DictCursor,
                autocommit=True,
                connect_timeout=10,
                read_timeout=10,
                write_timeout=10
            )
            # 测试连接
            connection.ping(reconnect=True)
            return connection
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            raise

    def load_api_config(self):
        """从platform_api_data表加载API配置"""
        try:
            connection = self.get_db_connection()
            cursor = connection.cursor(pymysql.cursors.DictCursor)

            cursor.execute("SELECT * FROM platform_api_data WHERE id = 14")
            result = cursor.fetchone()

            if result:
                self.api_config = {
                    'id': result['id'],
                    'platform_name': result['platform_name'],
                    'api_name': result['api_name'],
                    'api_path': result['api_path'],
                    'base_url': result['base_url'],
                    'http_method': result['http_method'],
                    'api_description': result['api_description'],
                    'is_active': result['is_active'],
                    'created_at': result['created_at'],
                    'updated_at': result['updated_at']
                }
                logger.info(f"✅ 加载API配置成功: {self.api_config['api_name']}")
            else:
                logger.error("❌ 未找到id=14的API配置")

            cursor.close()
            connection.close()

        except Exception as e:
            logger.error(f"❌ 加载API配置失败: {e}")

    def call_ozon_price_api(self, cursor="", limit=100):
        """调用OZON价格API获取商品价格信息"""
        try:
            # 确保API配置已加载
            if not self.api_config:
                self.load_api_config()
                if not self.api_config:
                    raise ValueError("未找到API配置")

            # 构建请求URL
            url = f"{self.api_config['base_url']}{self.api_config['api_path']}"

            # 构建请求头
            headers = {
                'Api-Key': os.getenv('OZON_API_KEY'),
                'Client-Id': os.getenv('OZON_CLIENT_ID'),
                'Content-Type': 'application/json'
            }

            # 构建请求体
            payload = {
                "limit": limit,
                "filter": {
                    "visibility": "ALL"
                }
            }

            if cursor:
                payload["cursor"] = cursor

            logger.info(f"🌐 调用OZON API: {url}")
            logger.info(f"📦 请求参数: limit={limit}, cursor={cursor[:20] if cursor else 'None'}...")

            import requests
            response = requests.post(url, headers=headers, json=payload, timeout=30)
            response.raise_for_status()

            data = response.json()
            logger.info(f"✅ API调用成功，获取到 {len(data.get('items', []))} 个商品")

            return data

        except Exception as e:
            logger.error(f"❌ OZON API调用失败: {e}")
            raise

    def save_price_data(self, items):
        """保存价格数据到ozon_product_prices表"""
        try:
            connection = self.get_db_connection()
            cursor = connection.cursor()

            saved_count = 0

            for item in items:
                try:
                    # 提取价格信息
                    price_info = item.get('price', {})
                    product_id = item.get('product_id')
                    offer_id = item.get('offer_id')

                    if not product_id or not offer_id:
                        logger.warning(f"⚠️ 跳过无效商品: product_id={product_id}, offer_id={offer_id}")
                        continue

                    # 构建插入数据
                    price_data = {
                        'product_id': product_id,
                        'offer_id': offer_id,
                        'auto_action_enabled': price_info.get('auto_action_enabled', False),
                        'currency_code': price_info.get('currency_code', 'RUB'),
                        'marketing_price': float(price_info.get('marketing_price', 0)),
                        'marketing_seller_price': float(price_info.get('marketing_seller_price', 0)),
                        'min_price': float(price_info.get('min_price', 0)),
                        'old_price': float(price_info.get('old_price', 0)),
                        'price': float(price_info.get('price', 0)),
                        'retail_price': float(price_info.get('retail_price', 0)),
                        'vat': float(price_info.get('vat', 0)),
                        'auto_add_to_ozon_actions_list_enabled': price_info.get('auto_add_to_ozon_actions_list_enabled', False),
                        'net_price': float(price_info.get('net_price', 0)),
                        'acquiring': float(item.get('acquiring', 0)),
                        'volume_weight': float(item.get('volume_weight', 0))
                    }

                    # 插入或更新数据
                    sql = """
                        INSERT INTO ozon_product_prices (
                            product_id, offer_id, auto_action_enabled, currency_code,
                            marketing_price, marketing_seller_price, min_price, old_price,
                            price, retail_price, vat, auto_add_to_ozon_actions_list_enabled,
                            net_price, acquiring, volume_weight
                        ) VALUES (
                            %(product_id)s, %(offer_id)s, %(auto_action_enabled)s, %(currency_code)s,
                            %(marketing_price)s, %(marketing_seller_price)s, %(min_price)s, %(old_price)s,
                            %(price)s, %(retail_price)s, %(vat)s, %(auto_add_to_ozon_actions_list_enabled)s,
                            %(net_price)s, %(acquiring)s, %(volume_weight)s
                        ) ON DUPLICATE KEY UPDATE
                            auto_action_enabled = VALUES(auto_action_enabled),
                            currency_code = VALUES(currency_code),
                            marketing_price = VALUES(marketing_price),
                            marketing_seller_price = VALUES(marketing_seller_price),
                            min_price = VALUES(min_price),
                            old_price = VALUES(old_price),
                            price = VALUES(price),
                            retail_price = VALUES(retail_price),
                            vat = VALUES(vat),
                            auto_add_to_ozon_actions_list_enabled = VALUES(auto_add_to_ozon_actions_list_enabled),
                            net_price = VALUES(net_price),
                            acquiring = VALUES(acquiring),
                            volume_weight = VALUES(volume_weight),
                            updated_at = CURRENT_TIMESTAMP
                    """

                    cursor.execute(sql, price_data)
                    saved_count += 1

                except Exception as item_error:
                    logger.error(f"❌ 处理商品失败 {offer_id}: {item_error}")
                    continue

            connection.commit()
            cursor.close()
            connection.close()

            logger.info(f"💾 成功保存 {saved_count} 个商品价格数据")
            return saved_count

        except Exception as e:
            logger.error(f"❌ 保存价格数据失败: {e}")
            return 0

    def sync_all_prices(self):
        """同步所有商品价格"""
        try:
            logger.info("� 开始同步OZON商品价格...")

            # 加载API配置
            self.load_api_config()
            if not self.api_config:
                return {
                    'success': False,
                    'message': '未找到API配置',
                    'total_synced': 0,
                    'total_pages': 0
                }

            total_synced = 0
            total_pages = 0
            cursor = ""

            # 分页获取所有商品价格数据
            while True:
                # 调用API获取数据
                response_data = self.call_ozon_price_api(cursor=cursor, limit=100)

                items = response_data.get('items', [])
                if not items:
                    logger.info("📋 没有更多商品数据，同步完成")
                    break

                # 保存数据
                saved_count = self.save_price_data(items)
                total_synced += saved_count
                total_pages += 1

                logger.info(f"📄 第 {total_pages} 页: 处理 {len(items)} 个商品，保存 {saved_count} 个")

                # 检查是否有下一页
                cursor = response_data.get('cursor', '')
                if not cursor:
                    logger.info("📋 已处理所有页面，同步完成")
                    break

                # 避免API限制，稍作延迟
                import time
                time.sleep(0.5)

            logger.info(f"✅ 价格同步完成，共同步 {total_synced} 个商品")

            return {
                'success': True,
                'message': f'成功同步 {total_synced} 个商品价格',
                'total_synced': total_synced,
                'total_pages': total_pages
            }

        except Exception as e:
            logger.error(f"❌ 价格同步失败: {e}")
            return {
                'success': False,
                'message': f'价格同步失败: {str(e)}',
                'total_synced': 0,
                'total_pages': 0
            }


# 创建价格同步实例
price_sync_api = OzonPriceSyncAPI()


@ozon_sync_bp.route('/api/ozon/sync-prices', methods=['POST'])
def api_sync_prices():
    """同步OZON商品价格API端点"""
    try:
        logger.info("📡 收到OZON价格同步请求")

        # 执行价格同步
        result = price_sync_api.sync_all_prices()

        # 返回结果
        status_code = 200 if result['success'] else 500
        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"❌ 价格同步API处理失败: {e}")
        return jsonify({
            'success': False,
            'message': f'API处理失败: {str(e)}',
            'total_synced': 0,
            'total_pages': 0
        }), 500


@ozon_sync_bp.route('/api/ozon/price-stats', methods=['GET'])
def api_price_stats():
    """获取价格统计信息"""
    try:
        # 使用db_manager查询统计信息
        query = """
            SELECT
                COUNT(*) as total_products,
                COUNT(CASE WHEN marketing_price_rub > 0 THEN 1 END) as products_with_price,
                AVG(marketing_price_rub) as avg_price,
                MIN(marketing_price_rub) as min_price,
                MAX(marketing_price_rub) as max_price
            FROM ozon_current_prices
        """

        result = db_manager.execute_query(query, fetch=True)

        if result and len(result) > 0:
            stats = result[0]  # 这是一个字典
            return jsonify({
                'success': True,
                'stats': {
                    'total_products': stats['total_products'],
                    'products_with_price': stats['products_with_price'],
                    'avg_price': round(float(stats['avg_price']) if stats['avg_price'] else 0, 2),
                    'min_price': float(stats['min_price']) if stats['min_price'] else 0,
                    'max_price': float(stats['max_price']) if stats['max_price'] else 0
                }
            })
        else:
            return jsonify({
                'success': True,
                'stats': {
                    'total_products': 0,
                    'products_with_price': 0,
                    'avg_price': 0,
                    'min_price': 0,
                    'max_price': 0
                }
            })

    except Exception as e:
        logger.error(f"❌ 获取价格统计失败: {e}")
        return jsonify({
            'success': False,
            'message': f'获取统计失败: {str(e)}'
        }), 500
